# Email Scheduler Setup Guide - Daily Mode

## Overview
This system automates onboarding reminder emails and IT support notifications based on configurable schedules. The system runs continuously in the background and sends emails daily at the exact configured times with duplicate prevention.

## Features
- ✅ **Daily Scheduling** - Checks every minute; runs daily at exact configured times with 1-minute tolerance
- ✅ **Precise Timing** - Sends emails once per day at the scheduled time only
- ✅ **Multiple Email Types** - Onboarding reminders and IT support notifications (each runs at its own schedule)
- ✅ **Web UI Configuration** - Easy time setting through web interface
- ✅ **Timezone Support** - Follows server timezone (Asia/Kuala_Lumpur)
- ✅ **Background Processing** - Runs continuously without manual intervention
- ✅ **Duplicate Prevention** - Daily lock files prevent multiple sends per day
- ✅ **Auto Cleanup** - Old lock files automatically removed after 2 days
- ✅ **Status Monitoring** - Built-in status checker for system health

## Required SQL Tables

### 1. Onboarding Cron Configuration Table
```sql
CREATE TABLE IF NOT EXISTS `onboarding_cron_config` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `config_type` varchar(50) NOT NULL DEFAULT 'cron_reminder',
    `cron_time` varchar(5) NOT NULL DEFAULT '09:00',
    `is_enabled` tinyint(1) NOT NULL DEFAULT 1,
    `email_addresses` TEXT NOT NULL,
    `email_subject` varchar(255) DEFAULT 'Daily Onboarding Reminder',
    `email_template` TEXT DEFAULT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `config_type` (`config_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

### 2. Onboarding Email Configuration Table
```sql
CREATE TABLE IF NOT EXISTS `onboarding_email_config` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `config_type` varchar(50) NOT NULL,
    `email_addresses` TEXT NOT NULL,
    `cron_time` varchar(8) DEFAULT NULL,
    `email_subject` varchar(255) DEFAULT NULL,
    `email_template` TEXT DEFAULT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `config_type` (`config_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

### 3. Email Configuration List Table
```sql
CREATE TABLE IF NOT EXISTS `email_config_list` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `config_name` varchar(100) NOT NULL,
    `email_addresses` TEXT NOT NULL,
    `description` TEXT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `config_name` (`config_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

## Initial Data Setup

### Insert Default Onboarding Configuration
```sql
INSERT INTO `onboarding_cron_config`
(`config_type`, `cron_time`, `is_enabled`, `email_addresses`, `email_subject`, `email_template`)
VALUES (
    'cron_reminder',
    '09:00',
    1,
    '<EMAIL>\<EMAIL>',
    'Daily Onboarding Reminder',
    'Dear Team,\n\nToday''s onboarding schedule:\n{onboarding_list}\n\nTotal: {total_count} new employees\n\nBest regards,\nHR Team'
)
ON DUPLICATE KEY UPDATE
`cron_time` = '09:00',
`is_enabled` = 1,
`email_addresses` = '<EMAIL>\<EMAIL>',
`email_subject` = 'Daily Onboarding Reminder',
`email_template` = 'Dear Team,\n\nToday''s onboarding schedule:\n{onboarding_list}\n\nTotal: {total_count} new employees\n\nBest regards,\nHR Team';
```

### Insert IT Support Configuration
```sql
INSERT INTO `onboarding_email_config` 
(`config_type`, `email_addresses`, `cron_time`, `email_subject`, `email_template`) 
VALUES (
    'it_support', 
    '<EMAIL>', 
    '09:30:00', 
    'IT Assignment Notification', 
    'New employee IT setup required'
) 
ON DUPLICATE KEY UPDATE 
`email_addresses` = '<EMAIL>', 
`cron_time` = '09:30:00', 
`email_subject` = 'IT Assignment Notification', 
`email_template` = 'New employee IT setup required';
```

## File Structure

### Core Files
- `email_scheduler.php` - Main scheduler daemon
- `auto_email_runner.php` - Email processing logic
- `start_scheduler.php` - Start the scheduler
- `stop_scheduler.php` - Stop the scheduler
- `check_scheduler.php` - Check scheduler status
- `onboarding_email_config.php` - Web UI for configuration

### Configuration Files
- `database.php` - Database connection
- `logs/` - Directory for log files

## Installation Steps

### 1. Database Setup
Execute the SQL table creation scripts above in your MySQL database.

### 2. File Permissions
Ensure the `logs/` directory is writable:
```bash
chmod 755 logs/
```

### 3. Timezone Configuration
The system uses Asia/Kuala_Lumpur timezone. Modify in files if needed:
```php
date_default_timezone_set('Asia/Kuala_Lumpur');
```

## Usage Workflow

### 1. Configure Email Schedules
1. Open `onboarding_email_config.php` in your web browser
2. Set the desired time for daily email sending
3. Select email configurations to include
4. Enable the cron job
5. Click "Update Configuration"

### 2. Start the Scheduler
```bash
# For Windows/XAMPP (use full PHP path)
C:\xampp\php\php.exe start_scheduler.php

# For Linux/Unix systems with PHP in PATH
php start_scheduler.php
```

### 3. Check Status
```bash
# For Windows/XAMPP (use full PHP path)
C:\xampp\php\php.exe check_scheduler.php

# For Linux/Unix systems with PHP in PATH
php check_scheduler.php
```

### 4. Stop the Scheduler (if needed)
```bash
# For Windows/XAMPP (use full PHP path)
C:\xampp\php\php.exe stop_scheduler.php

# For Linux/Unix systems with PHP in PATH
php stop_scheduler.php
```

## How It Works

### Daily Scheduling Process
1. **Scheduler starts** and runs continuously in background
2. **Every minute** it checks current time against configured schedules
3. **When time matches** (±1 minute tolerance), emails are sent once per day
4. **Daily lock files** prevent duplicate sends on same day (auto-cleanup after 2 days)
5. **Next day** the cycle repeats automatically

### Email Types Processed
1. **Onboarding Reminders** - General notifications for new employees
2. **IT Support Notifications** - IT assignment alerts for new hires

### Time Matching Logic
- **1-minute tolerance window** for precise daily scheduling
- **Server timezone** used for all time calculations
- **Daily execution** ensures emails sent once per day maximum

### Duplicate Prevention System
- **Lock files created**: `logs/onboarding_reminder_YYYY-MM-DD.lock`
- **Lock files created**: `logs/it_support_notification_YYYY-MM-DD.lock`
- **Auto cleanup**: Files older than 2 days automatically removed
- **Safety check**: Always checks for existing lock before sending

## Monitoring

### Log Files
- `logs/email_scheduler.log` - Scheduler activity and status
- `logs/auto_runner.log` - Email sending details
- `logs/scheduler_output.log` - System output and errors
- `logs/*_reminder_*.lock` - Daily lock files (prevent duplicates)
- `logs/*_notification_*.lock` - Daily lock files (prevent duplicates)

### Status Checking
Use `php check_scheduler.php` to view:
- ✅ Scheduler running status (PID and uptime)
- ✅ Database configuration status
- ✅ Daily lock file status (sent/not sent today)
- ✅ Recent scheduler activity (last 10 log entries)
- ✅ Log file sizes and last update times
- ✅ Process information and health check

### Example Status Output
```
=== Email Scheduler Status Check ===
Current Time: 2025-09-03 00:31:16 (Malaysia Time)

[2025-09-03 00:31:16] === Scheduler Status ===
[2025-09-03 00:31:16] ✓ Email Scheduler is RUNNING (PID: 23884)
[2025-09-03 00:31:16] ✓ Started: 2 minutes ago

[2025-09-03 00:31:16] === Database Connection ===
[2025-09-03 00:31:16] ✓ Database connection successful
[2025-09-03 00:31:16] ✓ Onboarding Config: Enabled, Time: 00:35
[2025-09-03 00:31:16] ✓ IT Support Config: Time: 00:40

[2025-09-03 00:31:16] === Daily Lock Files ===
[2025-09-03 00:31:16] ○ Onboarding Reminder: Not sent yet today
[2025-09-03 00:31:16] ○ IT Support Notification: Not sent yet today
```

## Troubleshooting

### Common Issues
1. **Scheduler not running** - Use `php start_scheduler.php`
2. **Emails not sending** - Check time configuration and database
3. **Duplicate emails** - Daily locks prevent this automatically
4. **Wrong timezone** - Verify timezone settings in PHP files
5. **"Process cannot access file"** - Kill stale PHP processes: `taskkill /F /IM php.exe`
6. **Lock files not working** - Check `logs/` directory permissions
7. **Status checker not found** - File was recreated, use `php check_scheduler.php`

### Debug Commands
```bash
# Check current configuration
php -r "require 'database.php'; $r=mysqli_query(\$conn,'SELECT * FROM onboarding_cron_config'); while(\$row=mysqli_fetch_assoc(\$r)) print_r(\$row);"

# View recent logs
Get-Content logs/email_scheduler.log | Select-Object -Last 10

# Check for running PHP processes
tasklist /FI "IMAGENAME eq php.exe" /FO TABLE

# Check lock files
Get-ChildItem logs/*lock* | Format-Table Name, LastWriteTime, Length

# Force stop all PHP processes (if needed)
taskkill /F /IM php.exe

# Test scheduler status
php check_scheduler.php
```

## Production Deployment

### 1. Set Production Schedule
Configure appropriate times (e.g., 9:00 AM) through the web interface.

### 2. Start Scheduler Service
```bash
php start_scheduler.php
```

### 3. Monitor Daily
Check logs regularly to ensure emails are being sent successfully.

### 4. Backup Configuration
Regularly backup the database tables containing email configurations.

## System Architecture

### Component Diagram
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Web UI        │    │   Scheduler      │    │   Database      │
│ (Configuration) │◄──►│   (Background)   │◄──►│   (Config)      │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌──────────────────┐
                       │   Email Runner   │
                       │   (Processing)   │
                       └──────────────────┘
                              │
                              ▼
                       ┌──────────────────┐
                       │   SMTP Server    │
                       │   (Delivery)     │
                       └──────────────────┘
```

### Data Flow
1. **Configuration** → Web UI saves schedule to database
2. **Scheduling** → Background scheduler checks time every minute
3. **Lock Check** → System checks for daily lock files before sending
4. **Execution** → Email runner processes and sends emails (if not locked)
5. **Lock Creation** → Daily lock files created after successful send
6. **Logging** → All activities logged for monitoring
7. **Cleanup** → Old lock files automatically removed after 2 days

## API Reference

### Configuration Functions
```php
// Update onboarding schedule
UPDATE onboarding_cron_config SET cron_time = 'HH:MM' WHERE is_enabled = 1;

// Update IT support schedule
UPDATE onboarding_email_config SET cron_time = 'HH:MM:SS' WHERE config_type = 'it_support';

// Enable/disable cron job
UPDATE onboarding_cron_config SET is_enabled = 1; -- Enable
UPDATE onboarding_cron_config SET is_enabled = 0; -- Disable
```

### Status Queries
```php
// Check current configuration
SELECT * FROM onboarding_cron_config WHERE is_enabled = 1;
SELECT * FROM onboarding_email_config WHERE config_type = 'it_support';

// View email configurations
SELECT * FROM email_config_list;
```

## Security Considerations

### File Permissions
- Ensure log directory is not web-accessible
- Protect configuration files from unauthorized access
- Use proper database user permissions

### Email Security
- Configure SMTP authentication properly
- Use encrypted connections when possible
- Validate email addresses before sending

### Process Security
- Run scheduler with appropriate user permissions
- Monitor for unauthorized process modifications
- Regular security updates for PHP and dependencies

## Quick Start Commands

### Start System
```bash
# 1. Start the scheduler
php start_scheduler.php

# 2. Check status
php check_scheduler.php

# 3. View recent activity
Get-Content logs/email_scheduler.log | Select-Object -Last 5
```

### Stop System
```bash
# 1. Stop the scheduler
php stop_scheduler.php

# 2. Verify stopped
php check_scheduler.php
```

### Emergency Reset
```bash
# 1. Kill all PHP processes
taskkill /F /IM php.exe

# 2. Clear lock files (if needed)
Remove-Item logs/*lock*

# 3. Restart scheduler
php start_scheduler.php
```

---

**System Status**: ✅ Fully Functional with Daily Mode & Duplicate Prevention
**Last Updated**: 2025-09-03
**Timezone**: Asia/Kuala_Lumpur
**Version**: 2.0 (Daily Mode)
**Compatibility**: PHP 7.4+, MySQL 5.7+
**Features**: Daily scheduling, duplicate prevention, auto-cleanup, status monitoring
