<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0">
<meta name="description" content="CLL Warehouse Inventory System">
<meta name="author" content="Powered by CLL Systems Sdn Bhd">
<meta name="robots" content="noindex, nofollow">
<title>HR Assets Faulty</title>

<link rel="shortcut icon" type="image/x-icon" href="https://www.cllsystems.com/wp-content/themes/cllsystems/img/favicon.png">

<link rel="stylesheet" href="assets/css/bootstrap.min.css">

<link rel="stylesheet" href="assets/css/animate.css">

<link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">

<link rel="stylesheet" href="assets/css/dataTables.bootstrap4.min.css">

<link rel="stylesheet" href="assets/plugins/fontawesome/css/fontawesome.min.css">
<link rel="stylesheet" href="assets/plugins/fontawesome/css/all.min.css">

<link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
<div id="global-loader">
<div class="whirly-loader"> </div>
</div>

<div class="main-wrapper">

    <?php 
    $role_id = $_SESSION["role_id"];
    $sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
    $result20 = mysqli_query($conn, $sql20);
    
    if ($result20) {
        $row20 = mysqli_fetch_assoc($result20);
        if ($row20) {
           $view = $row20['inventory_view'];
           $all = $row20['inventory_all'];
           if($view != '1' and $all !='1'){
            header("location: ./index.php");
           }
        } else {
            echo "<script>alert('Role data not found')</script>";
        }
    } else {
        echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
    }
    include("header.php");?>
    </div>

    <div class="page-wrapper">
        <div class="content">
        <div class="page-header">
        <div class="page-title">
        <h4>HR-Assets Faulty</h4>
        <h6>Add/Update User</h6>
        </div>
        </div>
        
        <div class="card">
        <div class="card-body">
        <div class="row">
        <div class="col-lg-3 col-sm-6 col-12">
        <div class="form-group">
        <label>Office</label>
            <select class="select">
            <option>Select</option>
            <option>CLL Systems</option>
            <option>Axperien</option>
            </select>
        </div>
        </div>
        
        <div class="col-lg-3 col-sm-6 col-12">
        <div class="form-group">
        <label>Location</label>
        <select class="select">
            <option>Select</option>
            <option>CLL Systems</option>
            <option>Axperien</option>
        
            </select>
        </div>
        </div>
        
        <div class="col-lg-3 col-sm-6 col-12">
        <div class="form-group">
        <label>Fault Category</label>
        <select class="select">
            <option>Select</option>
            <option>CLL Systems</option>
            <option>Axperien</option>
        
            </select>
        </div>
        </div>
        
        <div class="col-lg-12">
        <div class="form-group">
        <label>Image</label>
        <div class="image-upload">
        <input type="file">
        <div class="image-uploads">
        <img src="assets/img/icons/upload.svg" alt="img">
        <h4>Drag and drop a file to upload</h4>
        </div>
        </div>
        </div>
        </div>
        
        <div class="col-lg-12">
            <div class="form-group">
            <label>Remarks</label>
            <textarea class="form-contol"></textarea>
            </div>
        </div>
        
        
        <div class="col-lg-12">
        <a href="javascript:void(0);" class="btn btn-submit me-2">Submit</a>
        <a href="userlist.html" class="btn btn-cancel">Cancel</a>
        </div>
        </div>
        </div>
        </div>
        
        </div>
        </div>
        </div>



<script src="assets/js/jquery-3.6.0.min.js"></script>

<script src="assets/js/feather.min.js"></script>

<script src="assets/js/jquery.slimscroll.min.js"></script>

<script src="assets/js/jquery.dataTables.min.js"></script>
<script src="assets/js/dataTables.bootstrap4.min.js"></script>

<script src="assets/js/bootstrap.bundle.min.js"></script>

<script src="assets/plugins/select2/js/select2.min.js"></script>

<script src="assets/plugins/sweetalert/sweetalert2.all.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalerts.min.js"></script>

<script src="assets/js/script.js"></script>
</body>
</html>