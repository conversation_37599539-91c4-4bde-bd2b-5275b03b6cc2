<?php

/**
 * <PERSON><PERSON><PERSON>ail<PERSON> - <PERSON>HP email transport unit tests.
 * PHP version 5.5.
 *
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR>
 * @copyright 2012 - 2020 <PERSON>
 * @copyright 2004 - 2009 <PERSON>
 * @license   http://www.gnu.org/copyleft/lesser.html GNU Lesser General Public License
 */

namespace PHPMailer\Test\PHPMailer;

use PHPMailer\Test\SendTestCase;

/**
 * Test CRAM-MD5 authentication functionality.
 */
final class AuthCRAMMD5Test extends SendTestCase
{
    /**
     * Test CRAM-MD5 authentication.
     * Needs a connection to a server that supports this auth mechanism, so commented out by default.
     */
    public function testAuthCRAMMD5()
    {
        $this->markTestIncomplete(
            'Test needs a connection to a server supporting the CRAMMD5 auth mechanism.'
        );

        $this->Mail->Host = 'hostname';
        $this->Mail->Port = 587;
        $this->Mail->SMTPAuth = true;
        $this->Mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
        $this->Mail->AuthType = 'CRAM-MD5';
        $this->Mail->Username = 'username';
        $this->Mail->Password = 'password';
        $this->Mail->Body = 'Test body';
        $this->Mail->Subject .= ': Auth CRAM-MD5';
        $this->Mail->From = '<EMAIL>';
        $this->Mail->Sender = '<EMAIL>';
        $this->Mail->clearAllRecipients();
        $this->Mail->addAddress('<EMAIL>');
        //self::assertTrue($this->mail->send(), $this->mail->ErrorInfo);
    }
}
