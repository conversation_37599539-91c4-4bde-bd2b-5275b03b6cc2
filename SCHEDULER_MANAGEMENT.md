# Email Scheduler Management Guide

This guide explains how to start, check, stop, and clean up the automated email scheduler for onboarding and IT support notifications.

## 🚀 Quick Start Commands (Windows/XAMPP)

### ▶️ Start the Scheduler
```powershell
C:\xampp\php\php.exe start_scheduler.php
```
*Alternative: Double-click `start_scheduler.bat` file*

### 🔍 Check Status
```powershell
C:\xampp\php\php.exe check_scheduler.php
```

### ⏹️ Stop the Scheduler
```powershell
C:\xampp\php\php.exe stop_scheduler.php
```

### 🧹 Clean Up (if scheduler crashed)
```powershell
# Remove stale PID file
del scheduler.pid

# Remove stop signal file (if exists)
del stop_scheduler.txt

# Clear old logs (optional)
del logs\scheduler_output.log
del logs\auto_runner.log

# Check status after cleanup
C:\xampp\php\php.exe check_scheduler.php
```

---

## 🐧 Linux/Unix Commands

### ▶️ Start the Scheduler
```bash
php start_scheduler.php
```

### 🔍 Check Status
```bash
php check_scheduler.php
```

### ⏹️ Stop the Scheduler
```bash
php stop_scheduler.php
```

### 🧹 Clean Up (if scheduler crashed)
```bash
# Remove stale PID file
rm -f scheduler.pid

# Remove stop signal file (if exists)
rm -f stop_scheduler.txt

# Clear old logs (optional)
rm -f logs/scheduler_output.log
rm -f logs/auto_runner.log

# Check status after cleanup
php check_scheduler.php
```

---

## 📋 Understanding the Status Check

When you run the status check, you'll see:

### ✅ Healthy Scheduler
```
✓ Email Scheduler is RUNNING (PID: 1234)
✓ Database connection successful
✓ Onboarding Config: Enabled, Time: 12:30
✓ IT Support Config: Time: 13:30
```

### ❌ Stopped/Crashed Scheduler
```
✗ Email Scheduler is NOT RUNNING
⚠ Stale PID file exists - scheduler may have crashed
```

### 📊 Daily Status
```
✓ Onboarding Reminder: Sent today at 12:30
○ IT Support Notification: Not sent yet today
```

---

## 🔧 Troubleshooting

### Problem: "php is not recognized"
**Solution:** Use full PHP path
```powershell
C:\xampp\php\php.exe instead of php
```

### Problem: Scheduler keeps stopping
**Solutions:**
1. Check logs: `logs/email_scheduler.log`
2. Clean up stale files: `del scheduler.pid`
3. Run manually: `C:\xampp\php\php.exe email_scheduler.php`

### Problem: Emails not sending
**Solutions:**
1. Test manually: `C:\xampp\php\php.exe auto_email_runner.php`
2. Check email configuration in database
3. Verify MAILER setup

### Problem: "Stale PID file exists"
**Solution:** Clean up and restart
```powershell
del scheduler.pid
C:\xampp\php\php.exe start_scheduler.php
```

---

## 📁 Important Files

| File | Purpose |
|------|---------|
| `start_scheduler.php` | Starts the background scheduler |
| `stop_scheduler.php` | Stops the running scheduler |
| `check_scheduler.php` | Shows current status |
| `email_scheduler.php` | Main scheduler daemon |
| `auto_email_runner.php` | Email sending logic |
| `start_scheduler.bat` | Windows batch file alternative |
| `scheduler.pid` | Process ID file (auto-created) |
| `stop_scheduler.txt` | Stop signal file |

---

## 📊 Log Files

| Log File | Contains |
|----------|----------|
| `logs/email_scheduler.log` | Main scheduler activity |
| `logs/auto_runner.log` | Email sending details |
| `logs/scheduler_output.log` | Background process output |

---

## ⏰ How It Works

1. **Scheduler starts** and creates PID file
2. **Checks every minute** for scheduled times
3. **Sends emails** at configured times (once per day)
4. **Creates lock files** to prevent duplicates
5. **Continues running** until stopped

### Current Schedule
- **Onboarding emails**: 12:30 daily
- **IT support emails**: 13:30 daily

---

## 🚨 Emergency Stop

If the scheduler is stuck or misbehaving:

```powershell
# Force stop all PHP processes (use with caution)
taskkill /F /IM php.exe

# Clean up files
del scheduler.pid
del stop_scheduler.txt

# Check status
C:\xampp\php\php.exe check_scheduler.php
```

---

## ✅ Best Practices

1. **Always check status** before starting
2. **Clean up stale files** if scheduler crashed
3. **Monitor logs** for issues
4. **Test manually** if emails aren't sending
5. **Use full PHP path** on Windows/XAMPP
