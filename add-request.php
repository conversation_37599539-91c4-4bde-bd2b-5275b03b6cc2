<?php
require("database.php");
use <PERSON><PERSON><PERSON>ailer\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\SMTP;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\Exception;

if(isset($_SESSION["user"])){
    $email=$_SESSION["user"];
    $acc_id=$_SESSION["acc_id"];

    if (isset($_POST["submit"])) {
            $successMessages = array();
            $company_name = $_POST["company_name"];
            $company_category = $_POST["company_category"];
            $collection_date = $_POST["collection_date"];
            $returning_date = $_POST["returning_date"];
            $status = $_POST["status"];
            $remark = $_POST["remark"];
            $acc_id=$_SESSION["acc_id"];
            date_default_timezone_set('Asia/Kuala_Lumpur');
            $request_time = date('Y-m-d H:i:s');

            function getRequestId($n, $conn) {
                $char = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
                $is_unique = false;
                while (!$is_unique) {
                    $request_id = '';
                    for ($i = 0; $i < $n; $i++) {
                        $index = rand(0, strlen($char) - 1);
                        $request_id .= $char[$index];
                    }
                    $sql = "SELECT COUNT(*) AS count FROM `request-list` WHERE `request-id` = ?";
                    $stmt = mysqli_prepare($conn, $sql);
                    mysqli_stmt_bind_param($stmt, "s", $request_id);
                    mysqli_stmt_execute($stmt);
                    mysqli_stmt_bind_result($stmt, $count);
                    mysqli_stmt_fetch($stmt);
                    mysqli_stmt_close($stmt);
                    if ($count === 0) {
                        $is_unique = true;
                    }
                }
                return $request_id;
            }
            
            $request_id = getRequestId(10, $conn);


        // Check if inventory_id and qty are set and not empty
        if(isset($_POST["inventory_id"]) && isset($_POST["qty"]) && !empty($_POST["inventory_id"]) && !empty($_POST["qty"])) {
            // Retrieve inventory_id and qty arrays
            $inventory_ids = $_POST["inventory_id"];
            $qtys = $_POST["qty"];
            // Loop through each inventory_id and qty
            for($i = 0; $i < count($inventory_ids); $i++) {
                $inventory_id = $inventory_ids[$i];
                $qty = $qtys[$i];

                $sql10 = "SELECT * FROM `inventory-list` where `inventory-id`= '$inventory_id' ";
                $result10 = mysqli_query($conn, $sql10);
                $row10 = mysqli_fetch_assoc($result10);

                $inventory_qty=$row10['quantity'];

                if($qty>$inventory_qty){
                    echo "<script>alert('Insufficient stock quantity for {$row10['product-name']}. This particular product will not be recorded in the request. Other products will be added successfully.');</script>";
                }else{
                $sql = "INSERT INTO `request-list` (`request-id`, `inventory-id`, `acc_id`, `company_name`, `company_category` , `request_date`, `collection_date`, `return_date`, `quantity`, `request_status`, `request_remark`) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                $stmt = mysqli_prepare($conn, $sql);
                mysqli_stmt_bind_param($stmt, "ssssssssiis", $request_id, $inventory_id, $acc_id, $company_name, $company_category,  $request_time, $collection_date, $returning_date, $qty, $status, $remark);
                
                if (mysqli_stmt_execute($stmt)) {
                    $successMessages[] = "All the request has been added successfully, pending admin approval.";
                } else {
                    echo "<script>alert('Error: " . mysqli_error($conn) . "'); window.location='addbrand.php';</script>";
                }
                mysqli_stmt_close($stmt);
                }
            }
            function notifyAdminByEmail($conn) {
    
                $sql11 = "SELECT useracc.email
                FROM `role-list`
                INNER JOIN `useracc` ON `role-list`.`role-id` = `useracc`.`role_id`
                WHERE `role-list`.`request_smtp` = '1' or `role-list`.`request_all` = '1'";
                
                $result11 = mysqli_query($conn, $sql11);
                if ($result11->num_rows > 0) {
                    // Array to hold email addresses
                    $emails = array();
                    
                    // Fetching email addresses
                    while ($row11 = mysqli_fetch_assoc($result11)) {
                        $emails[] = $row11['email'];
                    }
                    
                    require 'MAILER/vendor/autoload.php';
                    
                    //Create an instance; passing `true` enables exceptions
                    $mail = new PHPMailer(true);
                    
                    try {
                        
                    //  $mail->SMTPDebug = SMTP::DEBUG_SERVER;                      //Enable verbose debug output
                    
                        $mail->isSMTP();    
                    
                    //SMTPOptions for Office 365
                        $mail->SMTPOptions = array(
                        'ssl' => array(
                            'verify_peer' => false,
                            'verify_peer_name' => false,
                            'allow_self_signed' => true
                        ));                                                             //Send using SMTP
                    
                    // Office 365 SMTP HOST
                       $mail->Host       = 'smtp.office365.com';
                       $mail->SMTPAuth   = true;                                      //Enable SMTP authentication
                       $mail->Username   = '<EMAIL>';                      //SMTP username
                       $mail->Password   = 'HG00a09l';                         //SMTP password
                       $mail->Port       = 587;                                        //TCP port to connect to; use 587 if 
                    
                    //Set From Email ID and NAME
                        $mail->setFrom('<EMAIL>', 'CIS');
                        
                    //Recipients
                    
                    foreach ($emails as $email) {
                        $mail->addAddress($email, 'Employee');
                    }             //Add a recipient To
                        // $mail->addCC('<EMAIL>');                              //Add a recipient Cc
                        // $mail->addBCC('<EMAIL>');                            //Add a recipient Bcc
                        $mail->SMTPSecure = 'tls';                                      //SMTPS uses TLS cryptographic protocols for improved security
                    
                        //Attachments
                    //    $mail->addAttachment('/var/tmp/file.tar.gz');                 //Add attachments
                    //    $mail->addAttachment('gmail.png', 'new.jpg');                 //Add attachment to replace attachment name
                    
                        //Content
                        $mail->isHTML(true);                                           //Set email format to HTML
                        $mail->Subject = "CIS - New Request Notification";                         //Subject Email 
                        $mail->Body  =  '<html><body>';                                //Email Body
                        $mail->Body .="
                        <table border='0'>
                                            
                                            <body style='font-family: 'Arial', sans-serif; background-color: #f4f4f4; margin: 0; padding: 0;'>
                                            <div class='container' style='max-width: 600px; margin: 0 auto; padding: 20px; background-color: #ffffff; box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);'>
                                                <div class='otp-container' style='background-color: #f2f2f2; padding: 15px; margin-top: 20px; text-align: center;'>
                                                    <p style='color: #555555;'>Dear management,</p>
                                                    <p style='color: #555555;'>There is a new request. Please take necessary action.</p>
                                                   
                                                    <p style='color: #555555;'><a href='https://www.cllsystems.com' style='text-decoration:none; color: #555555;'><b>CLL Systems Sdn Bhd</b></a>
                                                        </p>
                                                </div>
                                            </div>
                                        </body>
                                            </table>
                        ";                          
                        $mail->Body .= '</html></body>';
                        
                    if ($mail->send()) {                                       
                        header("location: ./add-request.php");
                    }else{
                       echo "Message could not be sent. Mailer Error: {$mail->ErrorInfo} <br> Mailer Debug:".$mail->SMTPDebug = SMTP::DEBUG_SERVER;
                    }
                    
                    } catch (Exception $e) {
                        echo "Message could not be sent. Mailer Error: {$mail->ErrorInfo}";
                    }
                }
            }
            
            $sqlCheckDuplicates = "SELECT COUNT(*) AS count FROM `request-list` WHERE `request-id` = ? AND `acc_id` = ?";
            $stmtCheckDuplicates = mysqli_prepare($conn, $sqlCheckDuplicates);
            mysqli_stmt_bind_param($stmtCheckDuplicates, "ss", $request_id, $acc_id);
            mysqli_stmt_execute($stmtCheckDuplicates);
            mysqli_stmt_bind_result($stmtCheckDuplicates, $duplicateCount);
            mysqli_stmt_fetch($stmtCheckDuplicates);
            mysqli_stmt_close($stmtCheckDuplicates);

            // If there are multiple inserts with the same request-id and acc-id, insert only one row into notification
            if ($duplicateCount >= 1) { // Changed condition to handle multiple duplicates
                // Insert into notification table
                $sqlInsertNotification = "INSERT INTO `notification` (`acc_id`, `request_id`, `timestamp`) VALUES (?, ?, ?)";
                $stmtInsertNotification = mysqli_prepare($conn, $sqlInsertNotification);
                mysqli_stmt_bind_param($stmtInsertNotification, "sss", $acc_id, $request_id, $request_time);
                if (mysqli_stmt_execute($stmtInsertNotification)) {
                    notifyAdminByEmail($conn);
                } else {
                    echo "<script>alert('Error inserting notification: " . mysqli_error($conn) . "');</script>";
                }
                mysqli_stmt_close($stmtInsertNotification);
            }
            
        } else {
            // Handle the case where inventory_id or qty is not set or empty
            echo "<script>alert('No inventory selected');</script>";
        }

        if (!empty($successMessages)) {
            // Combine the messages into a single string
            $successMessage = implode("\n", $successMessages);
            // Display the combined success message
            echo "<script>alert('$successMessage');</script>";
        } else {
            // If no success messages were generated, display a generic message
            echo "<script>alert('No products were successfully added.');</script>";
        }
    }
?>
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0">
<meta name="description" content="CLL Warehouse Inventory System">
<meta name="author" content="Powered by CLL Systems Sdn Bhd">
<meta name="robots" content="noindex, nofollow">
<title>CLLXWARE-Add Inventory Request</title>

<link rel='icon' type='image/x-icon' href='https://www.cllsystems.com/wp-content/themes/cllsystems/img/favicon.png'>

<link rel="stylesheet" href="assets/css/bootstrap.min.css">

<link rel="stylesheet" href="assets/css/bootstrap-datetimepicker.min.css">

<link rel="stylesheet" href="assets/css/animate.css">

<link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">

<link rel="stylesheet" href="assets/css/dataTables.bootstrap4.min.css">

<link rel="stylesheet" href="assets/plugins/fontawesome/css/fontawesome.min.css">
<link rel="stylesheet" href="assets/plugins/fontawesome/css/all.min.css">

<link rel="stylesheet" href="assets/css/style.css">
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
<style>
    <style>
    #product-list {
      width: 100%;
      max-width: 600px;
      border: 1px solid #ccc;
      padding: 10px;
      margin-bottom: 10px;
    }

    #product-list ul {
      list-style-type: none;
      padding: 0;
    }

    #product-list li {
      cursor: pointer;
      padding: 5px;
      margin-bottom: 5px;
      border: 1px solid #ccc;
    }

    #product-list li:hover {
      background-color: #eee;
    }
      .delete-set {
        display: inline-block;
        margin-left: 5px; /* Adjust as needed */
    }
    </style>
</head>
<body>
<div id="global-loader">
<div class="whirly-loader"> </div>
</div>

<div class="main-wrapper">

<?php 
$role_id = $_SESSION["role_id"];
$sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
$result20 = mysqli_query($conn, $sql20);

if ($result20) {
    $row20 = mysqli_fetch_assoc($result20);
    if ($row20) {
       $create = $row20['request_create'];
       $all = $row20['request_all'];
       if($create != '1' and $all !='1'){
        header("location: ./index.php");
       }
    } else {
        echo "<script>alert('Role data not found')</script>";
    }
} else {
    echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
}
include("header.php");?>
</div>

<div class="page-wrapper">
<div class="content">
<div class="page-header">
<div class="page-title">
<h4>Add Request</h4>
<h6>Add your new request</h6>
</div>
</div>
<div class="card">
<div class="card-body">
<form action='#' method='post'>
<div class="row">
<div class="col-lg-3 col-sm-6 col-12">
<div class="form-group">
<label>Company Name</label>
<input type="text" name="company_name" required>
</div>
</div>
<div class="col-lg-3 col-sm-6 col-12">
<div class="form-group">
<label>Company Category</label>
<input type="text" name="company_category" required>
</div>
</div>
<div class="col-lg-3 col-sm-6 col-12">
<div class="form-group">
<label>Collection Date</label>
<div class="input-groupicon">
<input type="text" placeholder="Choose Date" name="collection_date" class="datetimepicker" required>
<a class="addonset">
<img src="assets/img/icons/calendars.svg" alt="img">
</a>
</div>
</div>
</div>
<div class="col-lg-3 col-sm-6 col-12">
<div class="form-group">
<label>Returning Date</label>
<div class="input-groupicon">
<input type="text" placeholder="Choose Date" class="datetimepicker" name="returning_date" required>
<a class="addonset">
<img src="assets/img/icons/calendars.svg" alt="img">
</a>
</div>
</div>
</div>

<div class="col-lg-12 col-sm-6 col-12">
<div class="form-group">
<label>Product Model</label>
<div class="input-groupicon">
<input type="text" placeholder="Please type product model and select..." id="product-name">
<div id="product-list"></div>
</div>
</div>
</div>
</div>
<div class="row">
<div class="table-responsive mb-3">
<table class="table">
<thead>
<tr>
<th>No</th>
<th>Product Model</th>
<th>Product Part Number</th>
<th>Product Serial Number</th>
<th>QTY</th>
<th>Brand</th>
<th>Category</th>
<th>Location</th>
<th>Description</th>
<th>Action</th>
</tr>
</thead>
<tbody>

</tbody>
</table>
</div>
</div>
<div class="row">
<div class="col-lg-3 col-sm-6 col-12">
<div class="form-group">
<label>Status</label>
<select class="select" name="status" required>
<option value=''>Choose Status</option>
<option value='0' >Borrowed</option>
<option value='1'>Permanent</option>
</select>
</div>
</div>
<div class="col-lg-3 col-sm-6 col-12">
<div class="form-group">
<label>Remark</label>
<input type="text" name="remark">
</div>
</div>
<div class="row">
<div class="col-lg-12">
<input type='submit'  class='btn btn-submit me-2' name='submit' value='Submit'>
<a href='requestlist.php' class='btn btn-cancel'>Cancel</a>
</div>
</form>
</div>
</div>
</div>
</div>
</div>
</div>

<script>
  $(document).ready(function() {
    $('#product-name').on('keyup', function() {
        var product_name = $(this).val();

        if (product_name.length >= 3) {
            $.ajax({
                url: 'search-product.php',
                method: 'GET',
                data: {
                    product_name: product_name
                },
                success: function(data) {
                    $('#product-list').html(data);
                }
            });
        } else {
            $('#product-list').html('');
        }
    });

    $(document).on('click', '#product-list li', function() {
    var inventory_id = $(this).data('inventory-id');

    // Ajax call to get product details
    $.ajax({
        url: 'get-product-details.php',
        method: 'GET',
        data: {
            inventory_id: inventory_id // Send inventory ID instead of product name
        },
        success: function(data) {
            // Check if the response contains an error
            if (data.error) {
                console.error('Error:', data.error);
                return;
            }

            // Parse the product details from the response
            var product_details = data.product_details;

            // Update the table with product details
            var table = $('table');
            var rowCount = table.find('tbody tr').length;
            var newRow = '<tr>' +
                '<td>' + (rowCount + 1) + '</td>' +
                '<td>' + product_details['product-name'] + '</td>' +
                '<td>' + product_details['part-number'] + '</td>' +
                '<td>' + product_details['serial-number'] + '</td>' +
                '<td><input type="number" value="1" name="qty[]" style="width: 30px;"></td>' +
                '<td>' + product_details['brand_name'] + '</td>' +
                '<td>' + product_details['category_name'] + '</td>' +
                '<td> Room ' + product_details['location-id'] + '</td>' +
                '<td>' + product_details['des'] + '</td>' +
                '<td>' +
                '<input type="hidden" class="inventory-id" name="inventory_id[]" value="' + product_details['inventory-id'] + '">' +
                '<a href="javascript:void(0);" class="delete-set"><img src="assets/img/icons/delete.svg" alt="svg"></a>' +
                '</td>' +
                '</tr>';

            table.find('tbody').append(newRow);
        },
        error: function(xhr, status, error) {
            console.error('Error:', xhr.responseText);
            alert('An error occurred while fetching product details');
        }
    });
});



    // Delete row when delete button is clicked
    $(document).on('click', '.delete-set', function() {
    var row = $(this).closest('tr');
    row.remove();

    // Update hidden input fields
    updateHiddenInputs();

    // Renumber rows
    $('table tbody tr').each(function(index) {
        $(this).find('td:first').text(index + 1);
    });
});

function updateHiddenInputs() {
    var inventoryIds = [];
    var quantities = [];

    // Loop through each row in the table
    $('table tbody tr').each(function(index) {
        var inventoryId = $(this).find('.inventory-id').val();
        var quantity = $(this).find('input[name="qty[]"]').val();

        // Push inventory ID and quantity to arrays
        inventoryIds.push(inventoryId);
        quantities.push(quantity);
    });

    // Update hidden input fields with the latest data
    $('input[name="inventory_id[]"]').each(function(index) {
        $(this).val(inventoryIds[index]);
    });

    $('input[name="qty[]"]').each(function(index) {
        $(this).val(quantities[index]);
    });
}

});

  </script>
<script src="assets/js/jquery-3.6.0.min.js"></script>

<script src="assets/js/feather.min.js"></script>

<script src="assets/js/jquery.slimscroll.min.js"></script>

<script src="assets/js/jquery.dataTables.min.js"></script>
<script src="assets/js/dataTables.bootstrap4.min.js"></script>

<script src="assets/js/bootstrap.bundle.min.js"></script>

<script src="assets/plugins/select2/js/select2.min.js"></script>

<script src="assets/js/moment.min.js"></script>
<script src="assets/js/bootstrap-datetimepicker.min.js"></script>

<script src="assets/plugins/sweetalert/sweetalert2.all.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalerts.min.js"></script>

<script src="assets/js/script.js"></script>
</body>
</html>
<?php
}else{
    header("location: ./signin.php");
}
?>