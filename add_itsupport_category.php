<?php 
require("database.php");

if(isset($_SESSION["user"])) {
    $email = $_SESSION["user"];
    $acc_id = $_SESSION["acc_id"];

    // Fetch user details
    $sql4 = "SELECT * FROM `user-list` WHERE `acc_id` = ?"; 
    $stmt = mysqli_prepare($conn, $sql4);
    mysqli_stmt_bind_param($stmt, "s", $acc_id);
    mysqli_stmt_execute($stmt);
    $result4 = mysqli_stmt_get_result($stmt);

    if ($row4 = mysqli_fetch_assoc($result4)) { 
        $fullname = $row4['name']; 
        $email2 = $row4['email']; 
        if (is_null($fullname) || trim($fullname) === "") {
            $fullname = $email2; 
        }
    } else {
        echo "No record found!";
        exit;
    }

    // Form submission logic
    if (isset($_POST["submit"])) {
        $itsupport_inq = $_POST["itsupport_inq"];
        $itsupport_category = $_POST["itsupport_category"];
        $des = $_POST["des"];
    
        $random_string = uniqid("CLL-", true); 
        $shuffled_string = str_shuffle($random_string);
        $itsupport_category_id = substr($shuffled_string, 0, 10);
    
        // Check for duplicate itsupport_category_id
        $check_sql = "SELECT * FROM `itsupport_category_list` WHERE `itsupport_category_id` = ?";
        $check_stmt = mysqli_prepare($conn, $check_sql);
        mysqli_stmt_bind_param($check_stmt, "s", $itsupport_category_id);
        mysqli_stmt_execute($check_stmt);
        mysqli_stmt_store_result($check_stmt);
    
        if (mysqli_stmt_num_rows($check_stmt) > 0) {
            echo "<script>alert('IT support category already exists.'); window.location='add_itsupport_category.php';</script>";
        } else {
            $sql = "INSERT INTO `itsupport_category_list` (`itsupport_inq_id`, `itsupport_category_id`, `itsupport_category`, `des`) VALUES ( ?, ?, ?, ?)";
            $stmt = mysqli_prepare($conn, $sql);
            mysqli_stmt_bind_param($stmt, "ssss", $itsupport_inq, $itsupport_category_id, $itsupport_category, $des);
    
            if (mysqli_stmt_execute($stmt)) {
                echo "<script>alert('The IT support category has been added.'); window.location='add_itsupport_category.php';</script>";
            } else {
                echo "<script>alert('Error: " . mysqli_error($conn) . "'); window.location='add_itsupport_category.php';</script>";
            }
            mysqli_stmt_close($stmt);
        }
    
        mysqli_stmt_close($check_stmt);
    }
?>
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0">
<meta name="description" content="CLL Ticketing System">
<meta name="author" content="Powered by CLL Systems Sdn Bhd">
<meta name="robots" content="noindex, nofollow">
<title>CLLXWARE-Add New IT Support - Brand</title>

<link rel='icon' type='image/x-icon' href='https://www.cllsystems.com/wp-content/themes/cllsystems/img/favicon.png'>
<link rel="stylesheet" href="assets/css/bootstrap.min.css">
<link rel="stylesheet" href="assets/css/animate.css">
<link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">
<link rel="stylesheet" href="assets/css/dataTables.bootstrap4.min.css">
<link rel="stylesheet" href="assets/plugins/fontawesome/css/fontawesome.min.css">
<link rel="stylesheet" href="assets/plugins/fontawesome/css/all.min.css">
<link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
<div id="global-loader">
    <div class="whirly-loader"></div>
</div>

<div class="main-wrapper">
<?php 
$role_id = $_SESSION["role_id"];
$sql20 = "SELECT * FROM `role-list` WHERE `role-id` = ?";
$stmt20 = mysqli_prepare($conn, $sql20);
mysqli_stmt_bind_param($stmt20, "s", $role_id);
mysqli_stmt_execute($stmt20);
$result20 = mysqli_stmt_get_result($stmt20);

if ($row20 = mysqli_fetch_assoc($result20)) {
    $all = $row20['ticket_all'];
    if ($all != '1') {
        header("location: ./index.php");
        exit;
    }
} else {
    echo "<script>alert('Role data not found')</script>";
}

mysqli_stmt_close($stmt20);
include("header.php");
?>
</div>

<div class="page-wrapper">
    <div class="content">
        <div class="page-header">
            <div class="page-title">
                <h4>Add IT Support - Brand</h4>
                <h6>Create New IT Support - Brand</h6>
            </div>
        </div>

        <div class="card">
            <div class="card-body">
                <div class="row">
                    <form action="#" method="post">
                        <div class="col-lg-3 col-sm-6 col-12">
                            <div class="form-group">
                            <?php
                                $sql = "SELECT * FROM `itsupportinq_list`";
                                $result = mysqli_query($conn, $sql);
                                ?>
                            <label>IT Support - Inquiry Category</label>
                            <select class="select" name="itsupport_inq" required>
                                <option value='' disabled selected>Choose Inquiry</option>
                                <?php
                                    while ($row = mysqli_fetch_assoc($result)) {
                                        echo "<option value='" . $row['itsupport_inq_id'] . "'>" . $row['itsupport_inq'] . "</option>";
                                    }
                                    ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-lg-3 col-sm-6 col-12">
                            <div class="form-group">
                                <label>IT Support - Brand</label>
                                <input type="text" name="itsupport_category" required class="form-control">
                            </div>
                        </div>
                        <div class="col-lg-12">
                            <div class="form-group">
                                <label>Description</label>
                                <textarea class="form-control" name="des"></textarea>
                            </div>
                        </div>
                        <div class="col-lg-12">
                            <input type="submit" class="btn btn-submit me-2" name="submit" value="Add">
                            <a href="itsupport_category_list.php" class="btn btn-cancel">Cancel</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="assets/js/jquery-3.6.0.min.js"></script>
<script src="assets/js/feather.min.js"></script>
<script src="assets/js/jquery.slimscroll.min.js"></script>
<script src="assets/js/jquery.dataTables.min.js"></script>
<script src="assets/js/dataTables.bootstrap4.min.js"></script>
<script src="assets/js/bootstrap.bundle.min.js"></script>
<script src="assets/plugins/select2/js/select2.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalert2.all.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalerts.min.js"></script>
<script src="assets/js/script.js"></script>
</body>
</html>
<?php
} else {
    header("location: ./signin.php");
    exit;
}
?>
