<?php 
require("database.php");
if(isset($_SESSION["user"])){
    $email = $_SESSION["user"];
    $acc_id = $_SESSION["acc_id"];

    // Fetch user details
    $sql4 = "SELECT * FROM `user-list` WHERE `acc_id` = ?"; 
    $stmt = mysqli_prepare($conn, $sql4);
    mysqli_stmt_bind_param($stmt, "s", $acc_id);
    mysqli_stmt_execute($stmt);
    $result4 = mysqli_stmt_get_result($stmt);
  
    if ($row4 = mysqli_fetch_assoc($result4)) { 
        $fullname = $row4['name']; 
        $email2 = $row4['email']; 
        if (is_null($fullname) || trim($fullname) === "") {
            $fullname = $email2; 
        }
    } else {
        echo "No record found!";
    }

    // Handle the form submission
    if (isset($_POST["submit"])) {
        $manager_name = $_POST["manager_name"];
        $email_address = $_POST["email_address"];
        $department_id = $_POST["department_id"]; // New department ID

        // Create unique ID for the manager (if necessary)
        $random_string = uniqid("MAN-", true); 
        $shuffled_string = str_shuffle($random_string);
        $manager_id = substr($shuffled_string, 0, 10);

        // Check if the manager already exists
        $check_sql = "SELECT * FROM `manager_list` WHERE `email_address` = ?";
        $check_stmt = mysqli_prepare($conn, $check_sql);
        mysqli_stmt_bind_param($check_stmt, "s", $email_address);
        mysqli_stmt_execute($check_stmt);
        mysqli_stmt_store_result($check_stmt);
        
        if (mysqli_stmt_num_rows($check_stmt) > 0) {
            echo "<script>alert('Manager with this email already exists.'); window.location='add_manager.php';</script>";
        } else {
            // Insert the new manager
            $sql = "INSERT INTO `manager_list` (`manager_id`, `manager_name`, `email_address`, `department_id`) VALUES (?, ?, ?, ?)";
            $stmt = mysqli_prepare($conn, $sql);
            mysqli_stmt_bind_param($stmt, "ssss", $manager_id, $manager_name, $email_address, $department_id);
                
            if (mysqli_stmt_execute($stmt)) {
                echo "<script>alert('The manager has been added.'); window.location='manager_list.php';</script>";
            } else {
                echo "<script>alert('Error: " . mysqli_error($conn) . "'); window.location='add_manager.php';</script>";
            }
            mysqli_stmt_close($stmt);
        }
        
        mysqli_stmt_close($check_stmt);
    }

    // Fetch department options
    $sql_departments = "SELECT * FROM `department_list`";
    $result_departments = mysqli_query($conn, $sql_departments);
?>
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0">
<meta name="description" content="CLL Ticketing System">
<meta name="author" content="Powered by CLL Systems Sdn Bhd">
<meta name="robots" content="noindex, nofollow">
<title>CLLXWARE - Add Manager</title>

<link rel='icon' type='image/x-icon' href='https://www.cllsystems.com/wp-content/themes/cllsystems/img/favicon.png'>

<link rel="stylesheet" href="assets/css/bootstrap.min.css">
<link rel="stylesheet" href="assets/css/animate.css">
<link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">
<link rel="stylesheet" href="assets/css/dataTables.bootstrap4.min.css">
<link rel="stylesheet" href="assets/plugins/fontawesome/css/fontawesome.min.css">
<link rel="stylesheet" href="assets/plugins/fontawesome/css/all.min.css">
<link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
<div id="global-loader">
    <div class="whirly-loader"></div>
</div>

<div class="main-wrapper">

<?php 
$role_id = $_SESSION["role_id"];
$sql20 = "SELECT * FROM `role-list` WHERE `role-id` = ?";
$stmt20 = mysqli_prepare($conn, $sql20);
mysqli_stmt_bind_param($stmt20, "s", $role_id);
mysqli_stmt_execute($stmt20);
$result20 = mysqli_stmt_get_result($stmt20);

if ($row20 = mysqli_fetch_assoc($result20)) {
    $all = $row20['offboarding_all'];
    if ($all != '1') {
        header("location: ./index.php");
        exit;
    }
} else {
    echo "<script>alert('Role data not found')</script>";
    exit;
}

mysqli_stmt_close($stmt20);
include("header.php");
?>

</div>

<div class="page-wrapper">
    <div class="content">
        <div class="page-header">
            <div class="page-title">
                <h4>Add Manager</h4>
                <h6>Create New Manager</h6>
            </div>
        </div>

        <div class="card">
            <div class="card-body">
                <div class="row">
                    <form action="#" method="post">
                        <div class="col-lg-3 col-sm-6 col-12">
                            <div class="form-group">
                                <label>Manager Name</label>
                                <input type="text" name="manager_name" required class="form-control">
                            </div>
                        </div>
                        <div class="col-lg-3 col-sm-6 col-12">
                            <div class="form-group">
                                <label>Department</label>
                                <select name="department_id" class="form-control select" required>
                                    <option value="" disabled selected>Select Department</option>
                                    <?php while ($row_dept = mysqli_fetch_assoc($result_departments)) { ?>
                                        <option value="<?php echo $row_dept['department_id']; ?>"><?php echo $row_dept['department_name']; ?></option>
                                    <?php } ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-lg-3 col-sm-6 col-12">
                            <div class="form-group">
                                <label>Email Address</label>
                                <input type="email" name="email_address" required class="form-control">
                            </div>
                        </div>
                        <div class="col-lg-12">
                            <input type='submit' class='btn btn-submit me-2' name='submit' value='Add'>
                            <a href="manager_list.php" class="btn btn-cancel">Cancel</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

    </div>
</div>
</div>

<script src="assets/js/jquery-3.6.0.min.js"></script>
<script src="assets/js/feather.min.js"></script>
<script src="assets/js/jquery.slimscroll.min.js"></script>
<script src="assets/js/jquery.dataTables.min.js"></script>
<script src="assets/js/dataTables.bootstrap4.min.js"></script>
<script src="assets/js/bootstrap.bundle.min.js"></script>
<script src="assets/plugins/select2/js/select2.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalert2.all.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalerts.min.js"></script>
<script src="assets/js/script.js"></script>
</body>
</html>
<?php
}else{
    header("location: ./signin.php");
}
?>
