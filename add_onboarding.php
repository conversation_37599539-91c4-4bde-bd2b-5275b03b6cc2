<?php
require("database.php");

if (isset($_SESSION["user"])) {
    $email = $_SESSION["user"];
    $acc_id = $_SESSION["acc_id"];

    // Fetch user details
    $sql4 = "SELECT * FROM `user-list` WHERE `acc_id` = ?";
    $stmt = mysqli_prepare($conn, $sql4);
    mysqli_stmt_bind_param($stmt, "s", $acc_id);
    mysqli_stmt_execute($stmt);
    $result4 = mysqli_stmt_get_result($stmt);

    if ($row4 = mysqli_fetch_assoc($result4)) {
        $fullname = $row4['name'];
        $email2 = $row4['email'];

        // Use email if full name is empty
        if (is_null($fullname) || trim($fullname) === "") {
            $fullname = $email2;
        }
    } else {
        echo "No record found!";
    }

    // Handle form submission
    if (isset($_POST["submit"])) {
        // Get form input data
        $full_name = $_POST["full_name"];
        $employee_id = $_POST["employee_id"];
        $preferred_name = $_POST["preferred_name"];
        $department = $_POST["department"];
        $ic_number = $_POST["ic_number"];
        $gender = $_POST["gender"];
        $job_title = $_POST["job_title"];
        $personal_email = $_POST["personal_email"];
        $address = $_POST["address"];
        $phone_number = $_POST["phone_number"];
        $company_email = $_POST["company_email"];
        $reporting_manager = $_POST["reporting_manager"];
        $onboarding_date = $_POST["onboarding_date"];
        $office_name = $_POST["office_name"];
        // Handle personal image upload
        $personal_image = '';
        if (isset($_FILES['personal_image']) && $_FILES['personal_image']['error'] == 0) {
            $target_dir = "uploads/onboarding_images/";
            if (!file_exists($target_dir)) {
                mkdir($target_dir, 0777, true);
            }
            $file_extension = pathinfo($_FILES['personal_image']['name'], PATHINFO_EXTENSION);
            $unique_filename = uniqid() . '.' . $file_extension;
            $target_file = $target_dir . $unique_filename;
            if (move_uploaded_file($_FILES['personal_image']['tmp_name'], $target_file)) {
                $personal_image = $target_file;
            }
        }





        // Generate unique onboarding ID
        $random_string = uniqid("ONB-", true);
        $shuffled_string = str_shuffle($random_string);
        $onboarding_id = substr($shuffled_string, 0, 10);

        // Check if employee_id already exists
        $check_sql = "SELECT employee_id FROM `onboarding_list` WHERE `employee_id` = ?";
        $check_stmt = mysqli_prepare($conn, $check_sql);
        mysqli_stmt_bind_param($check_stmt, "s", $employee_id);
        mysqli_stmt_execute($check_stmt);
        $check_result = mysqli_stmt_get_result($check_stmt);

        if (mysqli_num_rows($check_result) > 0) {
            mysqli_stmt_close($check_stmt);
            echo "<script>alert('Error: Employee ID \"$employee_id\" already exists. Please use a different Employee ID.'); window.location='add_onboarding.php';</script>";
            exit;
        }
        mysqli_stmt_close($check_stmt);

        // Get IDs for department, manager, and office
        $department_id = $_POST['department'] ?? '';
        $manager_id = $_POST['reporting_manager'] ?? '';
        $office_id = $_POST['office_name'] ?? '';

        // Insert onboarding data into the database
        $sql = "INSERT INTO `onboarding_list` (`onboarding_id`, `full_name`, `employee_id`, `preferred_name`, `department_id`, `ic_number`, `gender`, `job_title`, `personal_email`, `address`, `phone_number`, `company_email`, `manager_id`, `onboarding_date`, `office_id`, `personal_image`)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, "ssssssssssssssss", $onboarding_id, $full_name, $employee_id, $preferred_name, $department_id, $ic_number, $gender, $job_title, $personal_email, $address, $phone_number, $company_email, $manager_id, $onboarding_date, $office_id, $personal_image);

        if (mysqli_stmt_execute($stmt)) {
            // Ensure optional columns exist then update token/recipients
            ensure_onboarding_extra_columns($conn);

            // Create a setup token for company email/password setup
            $setup_token = bin2hex(random_bytes(16));
            $upd = mysqli_prepare($conn, "UPDATE `onboarding_list` SET `setup_token` = ?, `company_email_set` = 0 WHERE `onboarding_id` = ?");
            mysqli_stmt_bind_param($upd, "ss", $setup_token, $onboarding_id);
            mysqli_stmt_execute($upd);
            mysqli_stmt_close($upd);

            // Step 1: Send email to CTO for company email setup
            send_setup_link_email($onboarding_id, $full_name, $employee_id, $setup_token, $conn);

            // Step 1: Notify HR about new onboarding created
            notify_hr_onboarding_created($onboarding_id, $conn);

            echo "<script>alert('The onboarding has been added. Notification emails sent.'); window.location='onboarding_list.php';</script>";
        } else {
            echo "<script>alert('Error: " . mysqli_error($conn) . "'); window.location='add_onboarding.php';</script>";
        }

        mysqli_stmt_close($stmt);
    }
} else {
    header("location: ./signin.php");
}

// Ensures extra columns we need exist
function ensure_onboarding_extra_columns($conn){
    $cols = [
        'company_email_password' => "ALTER TABLE `onboarding_list` ADD COLUMN `company_email_password` varchar(255) NULL",
        'company_email_set' => "ALTER TABLE `onboarding_list` ADD COLUMN `company_email_set` TINYINT(1) NOT NULL DEFAULT 0",
        'setup_token' => "ALTER TABLE `onboarding_list` ADD COLUMN `setup_token` varchar(255) NULL",

        'department_id' => "ALTER TABLE `onboarding_list` ADD COLUMN `department_id` varchar(255) NULL",
        'manager_id' => "ALTER TABLE `onboarding_list` ADD COLUMN `manager_id` varchar(255) NULL",
        'office_id' => "ALTER TABLE `onboarding_list` ADD COLUMN `office_id` varchar(255) NULL"
    ];
    foreach($cols as $name=>$sql){
        $q = mysqli_prepare($conn, "SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME='onboarding_list' AND COLUMN_NAME=?");
        mysqli_stmt_bind_param($q, "s", $name);
        mysqli_stmt_execute($q);
        mysqli_stmt_bind_result($q, $cnt);
        mysqli_stmt_fetch($q);
        mysqli_stmt_close($q);
        if((int)$cnt === 0){ @mysqli_query($conn, $sql); }
    }
}

// Email helpers
function send_setup_link_email($onboarding_id, $full_name, $employee_id, $token, $conn){
    require_once 'MAILER/vendor/autoload.php';
    $link = (isset($_SERVER['HTTP_HOST']) ? ( (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://') . $_SERVER['HTTP_HOST'] . dirname($_SERVER['PHP_SELF']) ) : 'https://cis.cllsystems.com:9443/staging') . "/setup_company_account.php?onboarding_id=".urlencode($onboarding_id)."&token=".urlencode($token);
    $to = '<EMAIL>';
    $mail = new PHPMailer\PHPMailer\PHPMailer(true);
    try{
        $mail->isSMTP();
        $mail->Host='smtp.office365.com';
        $mail->SMTPAuth=true;
        $mail->Username='<EMAIL>';
        $mail->Password='&[i3F6}0hOw6';
        $mail->SMTPSecure='tls';
        $mail->Port=587;
        $mail->setFrom('<EMAIL>','CLLXWARE');
        // Get CTO email configuration
        $cto_config = mysqli_query($conn, "SELECT * FROM `onboarding_email_config` WHERE `config_type` = 'cto_request'");
        $cto_data = mysqli_fetch_assoc($cto_config);

        $cto_emails = $cto_data ? array_filter(array_map('trim', explode("\n", $cto_data['email_addresses']))) : ['<EMAIL>'];
        $email_subject = $cto_data['email_subject'] ?? 'New Employee Email Setup Required';
        $email_template = $cto_data['email_template'] ?? 'Please use the link below to set company email and password for: {employee_name}\n\n{setup_link}';

        // Get employee record with related names for template variables
        $stmt = mysqli_prepare($conn, "
            SELECT ol.*,
                   dl.department_name,
                   ml.manager_name,
                   ofl.office_name as office_name_actual
            FROM `onboarding_list` ol
            LEFT JOIN `department_list` dl ON ol.department_id = dl.department_id
            LEFT JOIN `manager_list` ml ON ol.manager_id = ml.manager_id
            LEFT JOIN `office_list` ofl ON ol.office_id = ofl.office_id
            WHERE ol.onboarding_id = ?
        ");
        mysqli_stmt_bind_param($stmt, 's', $onboarding_id);
        mysqli_stmt_execute($stmt);
        $res = mysqli_stmt_get_result($stmt);
        $rec = mysqli_fetch_assoc($res);
        mysqli_stmt_close($stmt);

        if ($rec) {
            // Replace subject placeholder
            $email_subject = str_replace('{employee_name}', $rec['full_name'], $email_subject);

            // Replace template variables with actual names from related tables
            $email_body = str_replace([
                '{employee_name}', '{employee_id}', '{department}', '{designation}',
                '{onboarding_date}', '{personal_email}', '{phone_number}',
                '{reporting_manager}', '{office_name}', '{setup_link}'
            ], [
                $rec['full_name'], $rec['employee_id'], $rec['department_name'] ?? '', $rec['job_title'],
                $rec['onboarding_date'], $rec['personal_email'], $rec['phone_number'],
                $rec['manager_name'] ?? '', $rec['office_name_actual'] ?? '', $link
            ], $email_template);
        } else {
            $email_subject = str_replace('{employee_name}', $full_name, $email_subject);
            $email_body = 'Please use the link below to set company email and password for: '.htmlspecialchars($full_name).'\n\n'.$link;
        }

        foreach($cto_emails as $email) {
            $mail->addAddress($email);
        }

        $mail->isHTML(true);
        $mail->Subject = $email_subject;
        $mail->Body = nl2br(htmlspecialchars($email_body));
        $mail->send();
    }catch(Exception $e){ /* ignore for now */ }
}



// Step 1: Notify HR about new onboarding created
function notify_hr_onboarding_created($onboarding_id, $conn) {
    // Get employee record with related names
    $stmt = mysqli_prepare($conn, "
        SELECT ol.*,
               dl.department_name,
               ml.manager_name,
               ofl.office_name as office_name_actual
        FROM `onboarding_list` ol
        LEFT JOIN `department_list` dl ON ol.department_id = dl.department_id
        LEFT JOIN `manager_list` ml ON ol.manager_id = ml.manager_id
        LEFT JOIN `office_list` ofl ON ol.office_id = ofl.office_id
        WHERE ol.onboarding_id = ?
    ");
    mysqli_stmt_bind_param($stmt, 's', $onboarding_id);
    mysqli_stmt_execute($stmt);
    $res = mysqli_stmt_get_result($stmt);
    $rec = mysqli_fetch_assoc($res);
    mysqli_stmt_close($stmt);

    if (!$rec) return;

    // Get HR email configuration
    $hr_config = mysqli_query($conn, "SELECT * FROM `onboarding_email_config` WHERE `config_type` = 'hr_admin'");
    $hr_data = mysqli_fetch_assoc($hr_config);

    $hr_emails = $hr_data ? array_filter(array_map('trim', explode("\n", $hr_data['email_addresses']))) : ['<EMAIL>'];

    $workflow_link = (isset($_SERVER['HTTP_HOST']) ? ( (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://') . $_SERVER['HTTP_HOST'] . dirname($_SERVER['PHP_SELF']) ) : 'https://cis.cllsystems.com:9443/staging');
    $workflow_link = rtrim($workflow_link,'/')."/onboarding_workflow.php?id=".urlencode($rec['onboarding_id']);

    $email_subject = 'New Employee Onboarding Created - ' . $rec['full_name'];
    $email_body = "Dear HR Team,\n\n";
    $email_body .= "A new employee onboarding has been created:\n\n";
    $email_body .= "Employee Information:\n";
    $email_body .= "- Name: " . $rec['full_name'] . "\n";
    $email_body .= "- Employee ID: " . $rec['employee_id'] . "\n";
    $email_body .= "- Department: " . ($rec['department_name'] ?? 'Not specified') . "\n";
    $email_body .= "- Designation: " . $rec['job_title'] . "\n";
    $email_body .= "- Reporting Manager: " . ($rec['manager_name'] ?? 'Not specified') . "\n";
    $email_body .= "- Office: " . ($rec['office_name_actual'] ?? 'Not specified') . "\n";
    $email_body .= "- Onboarding Date: " . $rec['onboarding_date'] . "\n";
    $email_body .= "- Personal Email: " . $rec['personal_email'] . "\n\n";
    $email_body .= "Next Step: CTO will be notified to set up company email.\n\n";
    $email_body .= "Track Progress: " . $workflow_link . "\n\n";
    $email_body .= "Best regards,\nSystem Administrator";

    $mail = new PHPMailer\PHPMailer\PHPMailer(true);
    try{
        $mail->isSMTP();
        $mail->Host='smtp.office365.com';
        $mail->SMTPAuth=true;
        $mail->Username='<EMAIL>';
        $mail->Password='&[i3F6}0hOw6';
        $mail->SMTPSecure='tls';
        $mail->Port=587;
        $mail->setFrom('<EMAIL>','CLLXWARE');

        foreach($hr_emails as $email) {
            $mail->addAddress($email);
        }

        $mail->isHTML(false);
        $mail->Subject = $email_subject;
        $mail->Body = $email_body;
        $mail->send();
    }catch(Exception $e){ /* ignore for now */ }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0">
    <meta name="description" content="Onboarding System">
    <meta name="author" content="Powered by CLL Systems Sdn Bhd">
    <meta name="robots" content="noindex, nofollow">
    <title>CLLXWARE - Onboarding Form</title>

    <!-- Favicon -->
    <link rel='icon' type='image/x-icon' href='https://www.cllsystems.com/wp-content/themes/cllsystems/img/favicon.png'>
    <!-- Stylesheets -->
    <link rel="stylesheet" href="assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="assets/css/animate.css">
    <link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">
    <link rel="stylesheet" href="assets/css/dataTables.bootstrap4.min.css">
    <link rel="stylesheet" href="assets/plugins/fontawesome/css/fontawesome.min.css">
    <link rel="stylesheet" href="assets/plugins/fontawesome/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">

    <style>
        .form-group { position: relative; }
        /* Remove excessive left padding so text aligns flush left */
        .form-group input,
        .form-group textarea,
        .form-group select { padding-left: 12px; }
        .card { border: 1px solid #f0f0f0; padding: 20px; box-shadow: 0 4px 8px rgba(0,0,0,0.1); margin-bottom: 20px; }
        .btn-primary { background-color: #007bff; border-color: #007bff; }
        .file-name {
            margin-top: 8px;
            padding: 6px 10px;
            font-size: 13px;
            color: #28a745;
            background-color: #f8f9fa;
            border: 1px solid #d4edda;
            border-radius: 4px;
            font-weight: 500;
        }
    </style>
</head>
<body>

<div class="main-wrapper">
    <?php include("header.php"); ?>

    <div class="page-wrapper">
        <div class="content">
            <div class="page-header">
                <div class="page-title form-header text-start">
                    <h4>New Employee Onboarding</h4>
                    <p>Please help us prepare for your arrival by reviewing and addressing the questions below.</p>
                </div>
            </div>

            <form action="#" method="post" enctype="multipart/form-data">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <!-- Full Name -->
                            <div class="col-lg-6 col-sm-6 col-12">
                                <div class="form-group">
                                    <label>Full Name *</label>
                                    <input type="text" name="full_name" required class="form-control" placeholder="Enter full name">
                                </div>
                            </div>

                            <!-- Employee ID -->
                            <div class="col-lg-6 col-sm-6 col-12">
                                <div class="form-group">
                                    <label>Employee ID *</label>
                                    <input type="text" name="employee_id" required class="form-control" placeholder="Enter employee ID">
                                </div>
                            </div>

                            <!-- Preferred Name -->
                            <div class="col-lg-6 col-sm-6 col-12">
                                <div class="form-group">
                                    <label>Preferred Name</label>
                                    <input type="text" name="preferred_name" class="form-control" placeholder="Enter preferred name (optional)">
                                </div>
                            </div>

                            <!-- Department -->
                            <div class="col-lg-6 col-sm-6 col-12">
                                <div class="form-group">
                                    <label>Department *</label>
                                    <select class="select" name="department" required>
                                        <option value="" disabled selected>Choose Department</option>
                                        <?php
                                        $dept_sql = "SELECT * FROM `department_list` ORDER BY `department_name` ASC";
                                        $dept_result = mysqli_query($conn, $dept_sql);
                                        if ($dept_result && mysqli_num_rows($dept_result) > 0) {
                                            while ($dept_row = mysqli_fetch_assoc($dept_result)) {
                                                echo "<option value='" . $dept_row['department_id'] . "'>" . $dept_row['department_name'] . "</option>";
                                            }
                                        }
                                        ?>
                                    </select>
                                </div>
                            </div>

                            <!-- IC Number -->
                            <div class="col-lg-6 col-sm-6 col-12">
                                <div class="form-group">
                                    <label>IC Number *</label>
                                    <input type="text" name="ic_number" required class="form-control" placeholder="Enter IC number">
                                </div>
                            </div>

                            <!-- Gender -->
                            <div class="col-lg-6 col-sm-6 col-12">
                                <div class="form-group">
                                    <label>Gender *</label>
                                    <select class="select" name="gender" required>
                                        <option value="" disabled selected>Choose Gender</option>
                                        <option value="Male">Male</option>
                                        <option value="Female">Female</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Designation -->
                            <div class="col-lg-6 col-sm-6 col-12">
                                <div class="form-group">
                                    <label>Designation *</label>
                                    <input type="text" name="job_title" required class="form-control" placeholder="Enter designation">
                                </div>
                            </div>

                            <!-- Personal Email -->
                            <div class="col-lg-6 col-sm-6 col-12">
                                <div class="form-group">
                                    <label>Personal Email *</label>
                                    <input type="email" name="personal_email" required class="form-control" placeholder="Enter personal email">
                                </div>
                            </div>

                            <!-- Address -->
                            <div class="col-lg-12">
                                <div class="form-group">
                                    <label>Address</label>
                                    <textarea class="form-control" name="address" rows="3" placeholder="Enter address"></textarea>
                                </div>
                            </div>

                            <!-- Phone Number -->
                            <div class="col-lg-6 col-sm-6 col-12">
                                <div class="form-group">
                                    <label>Phone Number *</label>
                                    <input type="text" name="phone_number" required class="form-control" placeholder="Enter phone number">
                                </div>
                            </div>

                            <!-- Company Email -->
                            <div class="col-lg-6 col-sm-6 col-12">
                                <div class="form-group">
                                    <label>Company Email</label>
                                    <input type="email" name="company_email" class="form-control" placeholder="Enter company email">
                                </div>
                            </div>

                            <!-- Reporting Manager -->
                            <div class="col-lg-6 col-sm-6 col-12">
                                <div class="form-group">
                                    <label>Reporting Manager</label>
                                    <select class="select" name="reporting_manager">
                                        <option value="" disabled selected>Choose Manager</option>
                                        <?php
                                        $manager_sql = "SELECT * FROM `manager_list` ORDER BY `manager_name` ASC";
                                        $manager_result = mysqli_query($conn, $manager_sql);
                                        if ($manager_result && mysqli_num_rows($manager_result) > 0) {
                                            while ($manager_row = mysqli_fetch_assoc($manager_result)) {
                                                echo "<option value='" . $manager_row['manager_id'] . "'>" . $manager_row['manager_name'] . "</option>";
                                            }
                                        }
                                        ?>
                                    </select>
                                </div>
                            </div>

                            <!-- On Boarding Date -->
                            <div class="col-lg-6 col-sm-6 col-12">
                                <div class="form-group">
                                    <label>On Boarding Date *</label>
                                    <input type="date" name="onboarding_date" required class="form-control" placeholder="Select onboarding date">
                                </div>
                            </div>

                            <!-- Office Name -->
                            <div class="col-lg-6 col-sm-6 col-12">
                                <div class="form-group">
                                    <label>Office Name</label>
                                    <select class="select" name="office_name">
                                        <option value="" disabled selected>Choose Office</option>
                                        <?php
                                        $office_sql = "SELECT * FROM `office_list` ORDER BY `office_name` ASC";
                                        $office_result = mysqli_query($conn, $office_sql);
                                        if ($office_result && mysqli_num_rows($office_result) > 0) {
                                            while ($office_row = mysqli_fetch_assoc($office_result)) {
                                                echo "<option value='" . $office_row['office_id'] . "'>" . $office_row['office_name'] . "</option>";
                                            }
                                        }
                                        ?>
                                    </select>
                                </div>
                            </div>

                            <!-- Personal Image -->
                            <div class="col-lg-6 col-sm-6 col-12">
                                <div class="form-group">
                                    <label>Personal Image</label>
                                    <div class="image-upload">
                                        <input type="file" id="personal_image" name="personal_image" accept="image/*">
                                        <div class="image-uploads">
                                            <img src="assets/img/icons/upload.svg" alt="img">
                                            <h4>Drag and drop a file to upload</h4>
                                        </div>
                                        <div id="personal_image_name" class="file-name" style="display:none; margin-top: 10px; padding: 8px; background: #e8f5e8; border: 1px solid #4caf50; border-radius: 4px; color: #2e7d32; font-weight: 500;"></div>
                                    </div>
                                </div>
                            </div>

                            <!-- Submit Button -->
                            <div class="col-lg-12">
                                <input type="submit" class="btn btn-submit me-2" name="submit" value="Submit">
                                <a href="onboarding_list.php" class="btn btn-cancel">Cancel</a>
                            </div>
                        </div>
                    </div>
                </div>
            </form>

        </div>
    </div>
</div>

<!-- Scripts -->
<script src="assets/js/jquery-3.6.0.min.js"></script>
<script src="assets/js/feather.min.js"></script>
<script src="assets/js/jquery.slimscroll.min.js"></script>
<script src="assets/js/jquery.dataTables.min.js"></script>
<script src="assets/js/dataTables.bootstrap4.min.js"></script>
<script src="assets/js/bootstrap.bundle.min.js"></script>
<script src="assets/plugins/select2/js/select2.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalert2.all.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalerts.min.js"></script>
    <script>
    // Wait for DOM to be fully loaded
    document.addEventListener('DOMContentLoaded', function() {

    // Check for duplicate Employee ID
    function checkEmployeeId(employeeId) {
        if (!employeeId.trim()) return true; // Allow empty for now, server will validate

        const xhr = new XMLHttpRequest();
        xhr.open('POST', 'check_employee_id.php', false); // Synchronous for form validation
        xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
        xhr.send('employee_id=' + encodeURIComponent(employeeId));

        if (xhr.status === 200) {
            const response = JSON.parse(xhr.responseText);
            return !response.exists; // Return true if NOT exists (available)
        }
        return true; // Allow if check fails
    }

    // Form validation before submit
    const formElement = document.querySelector('form');
    if (formElement) {
        formElement.addEventListener('submit', function(e) {
            const employeeIdInput = document.getElementById('employee_id');
            if (employeeIdInput) {
                const employeeId = employeeIdInput.value.trim();

                if (employeeId && !checkEmployeeId(employeeId)) {
                    e.preventDefault();
                    Swal.fire({
                        title: 'Duplicate Employee ID',
                        text: 'Employee ID "' + employeeId + '" already exists. Please use a different Employee ID.',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                    employeeIdInput.focus();
                    return false;
                }
            }
        });
    }

    // Real-time check when user types Employee ID
    const employeeIdInput = document.getElementById('employee_id');
    if (employeeIdInput) {
        employeeIdInput.addEventListener('blur', function() {
            const employeeId = this.value.trim();
            if (employeeId && !checkEmployeeId(employeeId)) {
                this.style.borderColor = '#dc3545';
                this.style.backgroundColor = '#fff5f5';

                // Show warning message
                let warningMsg = this.parentNode.querySelector('.employee-id-warning');
                if (!warningMsg) {
                    warningMsg = document.createElement('small');
                    warningMsg.className = 'employee-id-warning text-danger';
                    this.parentNode.appendChild(warningMsg);
                }
                warningMsg.textContent = 'Employee ID already exists!';
            } else {
                this.style.borderColor = '';
                this.style.backgroundColor = '';

                // Remove warning message
                const warningMsg = this.parentNode.querySelector('.employee-id-warning');
                if (warningMsg) {
                    warningMsg.remove();
                }
            }
        });
    }

    // Personal image upload functionality
    (function(){
        const input = document.getElementById('personal_image');
        const nameBox = document.getElementById('personal_image_name');
        if(input && nameBox){
            input.addEventListener('change', function(){
                if(this.files && this.files.length > 0){
                    nameBox.textContent = this.files[0].name;
                    nameBox.style.display = 'block';
                } else {
                    nameBox.textContent = '';
                    nameBox.style.display = 'none';
                }
            });
        }
    })();

    }); // End DOMContentLoaded
    </script>

<script src="assets/js/script.js"></script>

</body>
</html>
