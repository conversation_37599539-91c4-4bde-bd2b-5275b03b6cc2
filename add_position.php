<?php 
require("database.php");
if(isset($_SESSION["user"])){
    $email = $_SESSION["user"];
    $acc_id = $_SESSION["acc_id"];

    // Fetch user details
    $sql4 = "SELECT * FROM `user-list` WHERE `acc_id` = ?"; 
    $stmt = mysqli_prepare($conn, $sql4);
    mysqli_stmt_bind_param($stmt, "s", $acc_id);
    mysqli_stmt_execute($stmt);
    $result4 = mysqli_stmt_get_result($stmt);
  
    if ($row4 = mysqli_fetch_assoc($result4)) { 
        $fullname = $row4['name']; 
        $email2 = $row4['email']; 
        if (is_null($fullname) || trim($fullname) === "") {
            $fullname = $email2; 
        }
    } else {
        echo "No record found!";
    }

    // Fetch departments for dropdown
    $result_departments = mysqli_query($conn, "SELECT * FROM `department_list` ORDER BY department_name");

    // Handle the form submission
    if (isset($_POST["submit"])) {
        $position_name = $_POST["position_name"];
        $description = $_POST["description"];
        $department_id = $_POST["department_id"];

        // Create unique ID for the position
        $random_string = uniqid("POS-", true); 
        $shuffled_string = str_shuffle($random_string);
        $position_id = substr($shuffled_string, 0, 10);

        // Check if the position already exists in the same department
        $check_sql = "SELECT * FROM `position_list` WHERE `position_name` = ? AND `department_id` = ?";
        $check_stmt = mysqli_prepare($conn, $check_sql);
        mysqli_stmt_bind_param($check_stmt, "ss", $position_name, $department_id);
        mysqli_stmt_execute($check_stmt);
        mysqli_stmt_store_result($check_stmt);
        
        if (mysqli_stmt_num_rows($check_stmt) > 0) {
            echo "<script>alert('Position already exists in this department.'); window.location='add_position.php';</script>";
        } else {
            // Insert the new position
            $sql = "INSERT INTO `position_list` (`position_id`, `position_name`, `description`, `department_id`) VALUES (?, ?, ?, ?)";
            $stmt = mysqli_prepare($conn, $sql);
            mysqli_stmt_bind_param($stmt, "ssss", $position_id, $position_name, $description, $department_id);
                
            if (mysqli_stmt_execute($stmt)) {
                echo "<script>alert('The position has been added.'); window.location='position_list.php';</script>";
            } else {
                echo "<script>alert('Error: " . mysqli_error($conn) . "'); window.location='add_position.php';</script>";
            }
            mysqli_stmt_close($stmt);
        }
        
        mysqli_stmt_close($check_stmt);
    }
?>
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0">
<meta name="description" content="CLL Ticketing System">
<meta name="author" content="Powered by CLL Systems Sdn Bhd">
<meta name="robots" content="noindex, nofollow">
<title>CLLXWARE - Add Position</title>

<link rel='icon' type='image/x-icon' href='https://www.cllsystems.com/wp-content/themes/cllsystems/img/favicon.png'>

<link rel="stylesheet" href="assets/css/bootstrap.min.css">
<link rel="stylesheet" href="assets/css/animate.css">
<link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">
<link rel="stylesheet" href="assets/css/dataTables.bootstrap4.min.css">
<link rel="stylesheet" href="assets/plugins/fontawesome/css/fontawesome.min.css">
<link rel="stylesheet" href="assets/plugins/fontawesome/css/all.min.css">
<link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
<div id="global-loader">
    <div class="whirly-loader"></div>
</div>

<div class="main-wrapper">
<?php include("header.php"); ?>
</div>

<div class="page-wrapper">
    <div class="content">
        <div class="page-header">
            <div class="page-title">
                <h4>Add Position</h4>
                <h6>Create New Position</h6>
            </div>
        </div>

        <div class="card">
            <div class="card-body">
                <div class="row">
                    <form action="#" method="post" id="positionForm">
                        <div class="col-lg-3 col-sm-6 col-12">
                            <select name="department_id" id="departmentSelect" class="form-control select" required>
                                <option value="" disabled selected>Select Department</option>
                                <?php while ($row_dept = mysqli_fetch_assoc($result_departments)): ?>
                                    <option value="<?php echo htmlspecialchars($row_dept['department_id']); ?>">
                                        <?php echo htmlspecialchars($row_dept['department_name']); ?>
                                    </option>
                                <?php endwhile; ?>
                            </select>
                        </div>
                        
                        <div id="positionDetails" style="display: none;">
                            <div class="col-lg-3 col-sm-6 col-12">
                                <div class="form-group">
                                    <label>Position Name *</label>
                                    <input type="text" name="position_name" required class="form-control">
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <div class="form-group">
                                    <label>Description</label>
                                    <textarea class="form-control" name="description"></textarea>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <input type='submit' class='btn btn-submit me-2' name='submit' value='Add'>
                                <a href="position_list.php" class="btn btn-cancel">Cancel</a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="assets/js/jquery-3.6.0.min.js"></script>
<script src="assets/js/feather.min.js"></script>
<script src="assets/js/jquery.slimscroll.min.js"></script>
<script src="assets/js/jquery.dataTables.min.js"></script>
<script src="assets/js/dataTables.bootstrap4.min.js"></script>
<script src="assets/js/bootstrap.bundle.min.js"></script>
<script src="assets/plugins/select2/js/select2.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalert2.all.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalerts.min.js"></script>
<script src="assets/js/script.js"></script>

<script>
$(document).ready(function() {
    $('.select').select2({
        placeholder: "Select Department",
        allowClear: true
    });

    $('#departmentSelect').change(function() {
        if ($(this).val()) {
            $('#positionDetails').slideDown();
        } else {
            $('#positionDetails').slideUp();
        }
    });
});
</script>
</body>
</html>
<?php
}else{
    header("location: ./signin.php");
}
?>
