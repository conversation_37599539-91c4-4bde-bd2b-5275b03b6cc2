<?php
require("database.php");
if(isset($_SESSION["user"])){
    $email=$_SESSION["user"];
    $acc_id = $_SESSION["acc_id"];
    $sql4 = "SELECT * FROM `user-list` WHERE `acc_id` = ?"; 
    $stmt = mysqli_prepare($conn, $sql4);
    mysqli_stmt_bind_param($stmt, "s", $acc_id);
    mysqli_stmt_execute($stmt);
    $result4 = mysqli_stmt_get_result($stmt);

    if ($row4 = mysqli_fetch_assoc($result4)) { 
        $fullname = $row4['name']; 
        $email2 = $row4['email']; 
        if (is_null($fullname) || trim($fullname) === "") {
            $fullname = $email2; 
        }
    } else {
        echo "No record found!";
    }
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0">
    <meta name="description" content="CLL Ticketing System">
    <meta name="author" content="Powered by CLL Systems Sdn Bhd">
    <meta name="robots" content="noindex, nofollow">
    <title>CLLXWARE-Submit Request</title>

    <link rel="shortcut icon" type="image/x-icon" href="https://www.cllsystems.com/wp-content/themes/cllsystems/img/favicon.png">
    <link rel="stylesheet" href="assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="assets/css/animate.css">
    <link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">
    <link rel="stylesheet" href="assets/css/dataTables.bootstrap4.min.css">
    <link rel="stylesheet" href="assets/plugins/fontawesome/css/fontawesome.min.css">
    <link rel="stylesheet" href="assets/plugins/fontawesome/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <script src="assets/js/jquery-3.6.0.min.js"></script>
</head>
<body>
    <div class="main-wrapper">
        <?php 
            $role_id = $_SESSION["role_id"];
            $sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
            $result20 = mysqli_query($conn, $sql20);

            if ($result20) {
                $row20 = mysqli_fetch_assoc($result20);
                if ($row20) {
                    $create = $row20['user'] == '1' || $row20['hr'] == '1' || $row20['finance'] == '1' || $row20['it_support'] == '1' || $row20['anonymous'] == '1';
                    $all = $row20['section_all'] == '1';

                    if (!$create && !$all) {
                        header("location: ./index.php");
                        exit();
                    }
                } else {
                    echo "<script>alert('Role data not found')</script>";
                }
            } else {
                echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
            }
            include("header.php");
        ?>

        <div class="page-wrapper">
            <div class="content">
                <div class="page-header">
                    <div class="page-title">
                        <h4>Submit Request</h4>
                        <h6>Submission</h6>
                    </div>
                </div>
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-lg-3 col-sm-6 col-12">
                                <div class="form-group">
                                    <label for="section">Section:</label>
                                    <select id="section" name="section" class="select">
                                        <option value="" disabled selected>Choose Section</option>
                                        <option value="assetsFaulty">Assets Faulty</option>
                                        <option value="hrInquiry">HR Inquiry</option>
                                        <option value="finance">Finance</option>
                                        <option value="procurement">Procurement</option>
                                        <option value="itsupport">IT Support</option>
                                        <option value="anonymousInquiry">Anonymous Inquiry</option>                                       
                                    </select>
                                </div>
                            </div>
                        </div>

                        <form id="assetsFaultyForm" action="submit_form.php" method="POST" enctype="multipart/form-data" style="display: none;">
                            <div class="form-group">
                                <div class="page-header">
                                    <div class="page-title">
                                        <h4>Assets Faulty</h4>
                                        <h6>Please submit a report for any faulty assets in the office.</h6>
                                    </div>
                                </div>
                                <?php
                                    $sql = "SELECT * FROM `office_list`";
                                    $result = mysqli_query($conn, $sql);
                                ?>
                                <label for="office">Office</label>
                                <select class="select" name="office" id="office" required>
                                    <option value="" disabled selected>Choose Office</option>
                                    <?php
                                        while ($row = mysqli_fetch_assoc($result)) {
                                            echo "<option value='" . $row['office_id'] . "'>" . $row['office_name'] . "</option>";
                                        }
                                    ?>
                                </select>
                            </div>

                            <div class="row">
                                <div class="col-lg-4">
                                    <div class="form-group">
                                        <label for="office_area">Office Area</label>
                                        <select class="select" name="office_area" id="office_area" required>
                                            <option value="" disabled selected>Choose office area</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="col-lg-3 col-sm-6 col-12">
                                    <div class="form-group">
                                        <label for="office_category">Category</label>
                                        <select class="select" name="office_category" id="office_category" required>
                                            <option value="" disabled selected>Choose category</option>
                                        </select>                                           
                                    </div>
                                </div>
                                
                                <div class="col-lg-3 col-sm-6 col-12">
                                    <div class="form-group">
                                        <label for="fault_category">Fault Category</label>
                                        <select class="select" name="fault_category" id="fault_category" required>
                                            <option value="" disabled selected>Choose Fault Category</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="col-lg-12">
                                    <div class="form-group">
                                        <label for="priority_level">Priority Level</label>
                                        <select class="select" name="priority_level" id="priority_level_assetsFaulty" required>
                                            <option value="" disabled selected>Choose Priority Level</option>
                                            <option value="High">High</option>
                                            <option value="Medium">Medium</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="col-lg-12">
                                    <div class="form-group">
                                        <label for="remarks">Remarks</label>
                                        <textarea class="form-control" name="remarks" id="remarks"></textarea>
                                    </div>
                                </div>
                                
                                <div class="col-lg-12">
                                    <div class="form-group">
                                        <label>Images</label>
                                        <div class="image-upload">
                                            <input type="file" name="image[]" id="imageAssetsFaulty" multiple onchange="previewImages(event, 'uploadedImagesAssetsFaulty', 'imageUploadsAssetsFaulty')">
                                            <div class="image-uploads" id="imageUploadsAssetsFaulty">
                                                <img src="assets/img/icons/upload.svg" alt="img">
                                                <h4>Drag and drop files to upload</h4>
                                            </div>
                                            <div id="uploadedImagesAssetsFaulty" style="margin-top: 10px;"></div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-lg-12">
                                    <input type="submit" class="btn btn-submit me-2" name="submit_assetsfaulty" value="Submit">
                                    <a href="section_list_user.php" class="btn btn-cancel">Cancel</a>
                                </div>
                            </div>
                        </form>

                        <form id="hrInquiryForm" action="submit_form.php" method="POST" enctype="multipart/form-data" style="display: none;">
                            <div class="col-lg-12">
                                <div class="form-group">
                                    <div class="page-header">
                                        <div class="page-title">
                                            <h4>HR Inquiry</h4>
                                            <h6>Report HR related issues.</h6>
                                        </div>
                                    </div>
                                    <?php
                                        $sql8 = "SELECT * FROM `hrinq_list`";
                                        $result8 = mysqli_query($conn, $sql8);
                                    ?>
                                    <label>HR Inquiry</label>
                                    <select class="select" name="hrinquiry" id="hrinquiry" required>
                                        <option value="" disabled selected>Choose HR Inquiry</option>
                                        <?php
                                            while ($row8 = mysqli_fetch_assoc($result8)) {
                                                echo "<option value='" . $row8['hr_inq_id'] . "'>" . $row8['hr_inq'] . "</option>";
                                            }
                                        ?>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-lg-12">
                                <div class="form-group">
                                    <label for="priority_level">Priority Level</label>
                                    <select class="select" name="priority_level" id="priority_level_hr" required>
                                        <option value="" disabled selected>Choose Priority Level</option>
                                        <option value="High">High</option>
                                        <option value="Medium">Medium</option>
                                    </select>
                                </div>
                            </div>

                            <div class="col-lg-12">
                                <div class="form-group">
                                    <label>Description</label>
                                    <textarea class="form-control" name="des" required></textarea>
                                </div>
                            </div>

                            <div class="col-lg-12">
                                <div class="form-group">
                                    <label>Images</label>
                                    <div class="image-upload">
                                        <input type="file" name="image[]" id="imageHRInquiry" multiple onchange="previewImages(event, 'uploadedImagesHRInquiry', 'imageUploadsHRInquiry')">
                                        <div class="image-uploads" id="imageUploadsHRInquiry">
                                            <img src="assets/img/icons/upload.svg" alt="img">
                                            <h4>Drag and drop files to upload</h4>
                                        </div>
                                        <div id="uploadedImagesHRInquiry" style="margin-top: 10px;"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-12">
                                <div class="form-group">
                                    <label>Remarks</label>
                                    <textarea class="form-control" name="remarks"></textarea>
                                </div>
                            </div>

                            <div class="col-lg-12">
                                <input type="submit" class="btn btn-submit me-2" name="submit_hrinquiry" value="Submit">
                                <a href="section_list_user.php" class="btn btn-cancel">Cancel</a>
                            </div>
                        </form>

                        <form id="financeForm" action="submit_form.php" method="POST" enctype="multipart/form-data" style="display: none;">
                            <div class="form-group">
                                <div class="page-header">
                                    <div class="page-title">
                                        <h4>Finance</h4>
                                        <h6>Please generate a report of financial assets.</h6>
                                    </div>
                                </div>
                                <?php
                                    $sql5 = "SELECT * FROM `financeinq_list`";
                                    $result5 = mysqli_query($conn, $sql5);
                                ?>
                                <select class="select" name="financecategory" id="financecategory" required>
                                    <option value="" disabled selected>Choose Category</option>
                                    <?php
                                        while ($row5 = mysqli_fetch_assoc($result5)) {
                                            echo "<option value='" . $row5['finance_inq_id'] . "'>" . $row5['finance_inq'] . "</option>";
                                        }
                                    ?>
                                </select>
                            </div>

                            <div class="col-lg-12">
                                <div class="form-group">
                                    <label for="priority_level">Priority Level</label>
                                    <select class="select" name="priority_level" id="priority_level_finance" required>
                                        <option value="" disabled selected>Choose Priority Level</option>
                                        <option value="High">High</option>
                                        <option value="Medium">Medium</option>
                                    </select>
                                </div>
                            </div>

                            <div class="col-lg-12">
                                <div class="form-group">
                                    <label>Description</label>
                                    <textarea class="form-control" name="des" required></textarea>
                                </div>
                            </div>

                            <div class="col-lg-12">
                                <div class="form-group">
                                    <label>Images</label>
                                    <div class="image-upload">
                                        <input type="file" name="image[]" id="imageFinance" multiple onchange="previewImages(event, 'uploadedImagesFinance', 'imageUploadsFinance')">
                                        <div class="image-uploads" id="imageUploadsFinance">
                                            <img src="assets/img/icons/upload.svg" alt="img">
                                            <h4>Drag and drop files to upload</h4>
                                        </div>
                                        <div id="uploadedImagesFinance" style="margin-top: 10px;"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-12">
                                <div class="form-group">
                                    <label>Remarks</label>
                                    <textarea class="form-control" name="remarks"></textarea>
                                </div>
                            </div>

                            <div class="col-lg-12">
                                <input type="submit" class="btn btn-submit me-2" name="submit_finance" value="Submit">
                                <a href="section_list_user.php" class="btn btn-cancel">Cancel</a>
                            </div>
                        </form>

                        <form id="procurementForm" action="submit_form.php" method="POST" enctype="multipart/form-data" style="display: none;">
                            <div class="col-lg-12">
                                <div class="form-group">
                                    <div class="page-header">
                                        <div class="page-title">
                                            <h4>Procurement</h4>
                                            <h6>Please generate a report of the procurement assets.</h6>
                                        </div>
                                    </div>
                                    <label>Procurement Inquiry</label>
                                    <textarea class="form-control" name="procurementinquiry" required></textarea>
                                </div>
                            </div>

                            <div class="col-lg-12">
                                <div class="form-group">
                                    <label for="priority_level">Priority Level</label>
                                    <select class="select" name="priority_level" id="priority_level_procurement" required>
                                        <option value="" disabled selected>Choose Priority Level</option>
                                        <option value="High">High</option>
                                        <option value="Medium">Medium</option>
                                    </select>
                                </div>
                            </div>

                            <div class="col-lg-12">
                                <div class="form-group">
                                    <label>Images</label>
                                    <div class="image-upload">
                                        <input type="file" name="image[]" id="imageProcurement" multiple onchange="previewImages(event, 'uploadedImagesProcurement', 'imageUploadsProcurement')">
                                        <div class="image-uploads" id="imageUploadsProcurement">
                                            <img src="assets/img/icons/upload.svg" alt="img">
                                            <h4>Drag and drop files to upload</h4>
                                        </div>
                                        <div id="uploadedImagesProcurement" style="margin-top: 10px;"></div>
                                    </div>
                                </div>
                            </div>
    
                            <div class="col-lg-12">
                                <div class="form-group">
                                    <label>Remarks</label>
                                    <textarea class="form-control" name="remarks"></textarea>
                                </div>
                            </div>

                            <div class="col-lg-12">
                                <input type="submit" class="btn btn-submit me-2" name="submit_procurement" value="Submit">
                                <a href="section_list_user.php" class="btn btn-cancel">Cancel</a>
                            </div>
                        </form>

                        <form id="itsupportForm" action="submit_form.php" method="POST" enctype="multipart/form-data" style="display: none;">
                            <div class="col-lg-12">                             
                                <div class="form-group">
                                    <div class="page-header">
                                        <div class="page-title">
                                            <h4>IT Support</h4>
                                            <h6>Please generate a report of the IT support assets.</h6>
                                        </div>
                                    </div>
                                    <?php
                                        $sql6 = "SELECT * FROM `itsupportinq_list`";
                                        $result6 = mysqli_query($conn, $sql6);
                                        $sql7 = "SELECT * FROM `brand-list`";
                                        $result7 = mysqli_query($conn, $sql7);
                                    ?>
                                    <label>IT Support Inquiry</label>
                                    <select class="select" name="itsupportinquiry" id="itsupportinquiry" required>
                                        <option value="" disabled selected>Choose Inquiry</option>
                                        <?php
                                            while ($row6 = mysqli_fetch_assoc($result6)) {
                                                echo "<option value='" . $row6['itsupport_inq_id'] . "'>" . $row6['itsupport_inq'] . "</option>";
                                            }
                                        ?>
                                    </select>
                                </div>
                            </div>
    
                            <div class="row">
                                <div class="col-lg-3 col-sm-6 col-12" >
                                    <div class="form-group">
                                        <label for="itsupportcategory">Category</label>
                                        <select class="select" name="itsupportcategory" id="itsupportcategory" required>
                                            <option value="" disabled selected>Choose Category</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="col-lg-3 col-sm-6 col-12" >
                                    <div class="form-group">
                                        <label for="brandcategory">Brand Category</label>
                                        <select class="select" name="brandcategory" id="brandcategory" required>
                                            <option value="" disabled selected>Choose Brand Category</option>
                                            <?php
                                                while ($row7 = mysqli_fetch_assoc($result7)) {
                                                    echo "<option value='" . $row7['brand-id'] . "'>" . $row7['brand-name'] . "</option>";
                                                }
                                            ?>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-12">
                                <div class="form-group">
                                    <label for="priority_level">Priority Level</label>
                                    <select class="select" name="priority_level" id="priority_level_itsupport" required>
                                        <option value="" disabled selected>Choose Priority Level</option>
                                        <option value="High">High</option>
                                        <option value="Medium">Medium</option>
                                    </select>
                                </div>
                            </div>

                            <div class="col-lg-12">
                                <div class="form-group">
                                    <label>Description</label>
                                    <textarea class="form-control" name="des" required></textarea>
                                </div>
                            </div>

                            <div class="col-lg-12">
                                <div class="form-group">
                                    <label>Images</label>
                                    <div class="image-upload">
                                        <input type="file" name="image[]" id="imageITSupport" multiple onchange="previewImages(event, 'uploadedImagesITSupport', 'imageUploadsITSupport')">
                                        <div class="image-uploads" id="imageUploadsITSupport">
                                            <img src="assets/img/icons/upload.svg" alt="img">
                                            <h4>Drag and drop files to upload</h4>
                                        </div>
                                        <div id="uploadedImagesITSupport" style="margin-top: 10px;"></div>
                                    </div>
                                </div>
                            </div>
    
                            <div class="col-lg-12">
                                <div class="form-group">
                                    <label>Remarks</label>
                                    <textarea class="form-control" name="remarks"></textarea>
                                </div>
                            </div>

                            <div class="col-lg-12">
                                <input type="submit" class="btn btn-submit me-2" name="submit_itsupport" value="Submit">
                                <a href="section_list_user.php" class="btn btn-cancel">Cancel</a>
                            </div>
                        </form>

                        <form id="anonymousinquiryForm" action="submit_form.php" method="POST" enctype="multipart/form-data" style="display: none;">
                            <div class="col-lg-12">
                                <div class="form-group">
                                    <div class="page-header">
                                        <div class="page-title">
                                            <h4>Anonymous Inquiry</h4>
                                            <h6>Report assets for anonymous inquiries.</h6>
                                        </div>
                                    </div>
                                    <label>Anonymous Inquiry</label>
                                    <textarea class="form-control" name="anonymousinquiry" required></textarea>
                                </div>
                            </div>
    
                            <div class="col-lg-12">
                                <div class="form-group">
                                    <label>Images</label>
                                    <div class="image-upload">
                                        <input type="file" name="image[]" id="imageAnonymousInquiry" multiple onchange="previewImages(event, 'uploadedImagesAnonymousInquiry', 'imageUploadsAnonymousInquiry')">
                                        <div class="image-uploads" id="imageUploadsAnonymousInquiry">
                                            <img src="assets/img/icons/upload.svg" alt="img">
                                            <h4>Drag and drop files to upload</h4>
                                        </div>
                                        <div id="uploadedImagesAnonymousInquiry" style="margin-top: 10px;"></div>
                                    </div>
                                </div>
                            </div>
    
                            <div class="col-lg-12">
                                <div class="form-group">
                                    <label>Remarks</label>
                                    <textarea class="form-control" name="remarks"></textarea>
                                </div>
                            </div>

                            <div class="col-lg-12">
                                <input type="submit" class="btn btn-submit me-2" name="submit_anonymousinquiry" value="Submit">
                                <a href="section_list_user.php" class="btn btn-cancel">Cancel</a>
                            </div>
                        </form>

                    </div>
                </div>
            </div>
        </div>
    </div>


    <script>
        document.getElementById("section").addEventListener("change", function() {
            var selectedOption = this.value;
            console.log("Selected section:", selectedOption);

            var sections = ["assetsFaultyForm", "hrInquiryForm", "financeForm", "procurementForm", "itsupportForm", "anonymousinquiryForm"];

            sections.forEach(function(section) {
                document.getElementById(section).style.display = "none";
            });

            if (selectedOption) {
                document.getElementById(selectedOption + "Form").style.display = "block";
            }
        });

        function previewImages(event, uploadedImagesContainerId, imageUploadsContainerId) {
            var files = event.target.files;
            var uploadedImagesContainer = document.getElementById(uploadedImagesContainerId);
            var imageUploadsContainer = document.getElementById(imageUploadsContainerId);
            uploadedImagesContainer.innerHTML = ''; // Clear any existing images

            var maxTotalSize = 20 * 1024 * 1024; // 20 MB in bytes
            var totalSize = 0;

            if (files.length > 0) {
                imageUploadsContainer.style.display = 'none';
            } else {
                imageUploadsContainer.style.display = 'block';
            }

            for (var i = 0; i < files.length; i++) {
                totalSize += files[i].size;
                if (totalSize > maxTotalSize) {
                    alert('Total size of selected files exceeds the 20MB limit.');
                    event.target.value = ''; // Clear the input
                    uploadedImagesContainer.innerHTML = ''; // Clear the preview container
                    imageUploadsContainer.style.display = 'block';
                    return;
                }
                var reader = new FileReader();
                reader.onload = (function(f) {
                    return function(e) {
                        var linkElement = document.createElement('a');
                        linkElement.href = e.target.result;
                        linkElement.download = f.name;
                        linkElement.textContent = f.name;
                        linkElement.style.display = 'block';
                        uploadedImagesContainer.appendChild(linkElement);
                    };
                })(files[i]);
                reader.readAsDataURL(files[i]);
            }
        }

        $(document).ready(function() {
            $('#section').change(function() {
                var selectedSection = $(this).val();
                $('#assetsFaultyForm').toggle(selectedSection === 'assetsFaulty');
                $('#hrInquiryForm').toggle(selectedSection === 'hrInquiry');
                $('#financeForm').toggle(selectedSection === 'finance');
                $('#procurementForm').toggle(selectedSection === 'procurement');
                $('#itsupportForm').toggle(selectedSection === 'itsupport');
                $('#anonymousinquiryForm').toggle(selectedSection === 'anonymousInquiry');
            });

            function fetchOptions(endpoint, payload, targetElementId, defaultOption) {
                $('#' + targetElementId).html('<option value="" disabled selected>' + defaultOption + '</option>');
                $.post(endpoint, payload, function(data) {
                    $('#' + targetElementId).html(data);
                }).fail(function() {
                    $('#' + targetElementId).html('<option value="" disabled selected>' + defaultOption + '</option>');
                });
            }

            $('#office').change(function() {
                var officeId = $(this).val();
                $('#office_area').html('<option value="" disabled selected>Choose office area</option>');
                $('#office_category').html('<option value="" disabled selected>Choose category</option>');
                $('#fault_category').html('<option value="" disabled selected>Choose Fault Category</option>');

                if (officeId) {
                    fetchOptions('fetch_office_area.php', { office_id: officeId }, 'office_area', 'Choose office area');
                    fetchOptions('fetch_fault_category.php', { office_id: officeId }, 'fault_category', 'Choose Fault Category');
                }
            });

            $('#office_area').change(function() {
                var officeAreaId = $(this).val();
                $('#office_category').html('<option value="" disabled selected>Choose category</option>');

                if (officeAreaId) {
                    fetchOptions('fetch_office_category.php', { office_area_id: officeAreaId }, 'office_category', 'Choose category');
                }
            });

            $('#itsupportinquiry').change(function() {
                var itsupportInquiryId = $(this).val();
                $('#itsupportcategory').html('<option value="" disabled selected>Choose Category</option>');

                if (itsupportInquiryId) {
                    fetchOptions('fetch_itsupport_category.php', { itsupport_inq_id: itsupportInquiryId }, 'itsupportcategory', 'Choose Category');
                }
            });
        });
    </script>

    <script src="assets/js/feather.min.js"></script>
    <script src="assets/js/jquery.slimscroll.min.js"></script>
    <script src="assets/js/jquery.dataTables.min.js"></script>
    <script src="assets/js/dataTables.bootstrap4.min.js"></script>
    <script src="assets/js/bootstrap.bundle.min.js"></script>
    <script src="assets/plugins/select2/js/select2.min.js"></script>
    <script src="assets/plugins/sweetalert/sweetalert2.all.min.js"></script>
    <script src="assets/plugins/sweetalert/sweetalerts.min.js"></script>
    <script src="assets/js/script.js"></script>
</body>
</html>
<?php
}else{
    header("location: ./signin.php");
}
?>
