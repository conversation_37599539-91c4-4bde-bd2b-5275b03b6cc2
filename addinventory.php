<?php 
require("database.php");
if(isset($_SESSION["user"])){
  $email=$_SESSION["user"];
  $acc_id = $_SESSION["acc_id"];
  $sql4 = "SELECT * FROM `user-list` WHERE `acc_id` = ?"; 
    $stmt = mysqli_prepare($conn, $sql4);
    mysqli_stmt_bind_param($stmt, "s", $acc_id);
    mysqli_stmt_execute($stmt);
    $result4 = mysqli_stmt_get_result($stmt);

    if ($row4 = mysqli_fetch_assoc($result4)) { 
        $fullname = $row4['name']; 
        $email2 = $row4['email']; 
        if (is_null($fullname) || trim($fullname) === "") {
          $fullname = $email2; 
      }
      } else {
        echo"No record found!";
      }
if (isset($_POST["submit"])) {
  $name= $_POST["name"];
  $brand= $_POST["brand"];
  $category = $_POST["category"];
  $location = $_POST["location"];
  $part_number = $_POST["part_number"];
  $serial_number = $_POST["serial_number"];
  $qty = $_POST["qty"];
  $ownership = $_POST["ownership"];
  $des = $_POST["des"];
  $status = $_POST["status"];
  $remark = $_POST["remark"];

  function getInventoryId($n, $conn) {
    $char = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $is_unique = false;

    while (!$is_unique) {
        // Generate a random inventory ID
        $inventory_id = '';
        for ($i = 0; $i < $n; $i++) {
            $index = rand(0, strlen($char) - 1);
            $inventory_id .= $char[$index];
        }

        // Check if the generated ID already exists in the inventory-list table
        $sql = "SELECT COUNT(*) AS count FROM `inventory-list` WHERE `inventory-id` = ?";
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, "s", $inventory_id);
        mysqli_stmt_execute($stmt);
        mysqli_stmt_bind_result($stmt, $count);
        mysqli_stmt_fetch($stmt);
        mysqli_stmt_close($stmt);

        
        if ($count === 0) {
            $is_unique = true;
        }
    }

    return $inventory_id;
}

$inventory_id = getInventoryId(10, $conn);

  $sql = "INSERT INTO `inventory-list` (`product-name`, `brand-id`, `category-id`, `location-id`, `part-number`, `serial-number`, `quantity`, `ownership`, `des`, `status`, `remark`, `created`, `inventory-id`) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
  $stmt = mysqli_prepare($conn, $sql);
  mysqli_stmt_bind_param($stmt, "ssssssississs", $name, $brand, $category, $location, $part_number, $serial_number, $qty, $ownership, $des, $status, $remark, $fullname, $inventory_id);
  
  if (mysqli_stmt_execute($stmt)) {
      echo "<script>alert('The inventory item has been added.'); window.location='addinventory.php';</script>";
  } else {
      echo "<script>alert('Error: " . mysqli_error($conn) . "'); window.location='addinventory.php';</script>";
  }
  mysqli_stmt_close($stmt);
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0">
<meta name="description" content="CLL Warehouse Inventory System">
<meta name="author" content="Powered by CLL Systems Sdn Bhd">
<meta name="robots" content="noindex, nofollow">
<title>CLLXWARE-Add New Inventory</title>

<link rel='icon' type='image/x-icon' href='https://www.cllsystems.com/wp-content/themes/cllsystems/img/favicon.png'>

<link rel="stylesheet" href="assets/css/bootstrap.min.css">

<link rel="stylesheet" href="assets/css/animate.css">

<link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">

<link rel="stylesheet" href="assets/css/dataTables.bootstrap4.min.css">

<link rel="stylesheet" href="assets/plugins/fontawesome/css/fontawesome.min.css">
<link rel="stylesheet" href="assets/plugins/fontawesome/css/all.min.css">

<link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
<div id="global-loader">
<div class="whirly-loader"> </div>
</div>
<div class="main-wrapper">

<?php 
$role_id = $_SESSION["role_id"];
$sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
$result20 = mysqli_query($conn, $sql20);

if ($result20) {
    $row20 = mysqli_fetch_assoc($result20);
    if ($row20) {
       $create = $row20['inventory_create'];
       $all = $row20['inventory_all'];
       if($create != '1' and $all !='1'){
        header("location: ./index.php");
       }
    } else {
        echo "<script>alert('Role data not found')</script>";
    }
} else {
    echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
}
include("header.php");?>
</div>

<div class="page-wrapper">
<div class="content">
<div class="page-header">
<div class="page-title">
<h4>Inventory Add</h4>
<h6>Create new data </h6>
</div>
</div>
<form action='#' method='post'>
<div class="card">
<div class="card-body">
<div class="row">
<?php
$sql = "SELECT * FROM `brand-list` WHERE `hide` !='1' ";
$result = mysqli_query($conn, $sql);

$sql2 = "SELECT * FROM `category-list` WHERE `hide` !='1'";
$result2 = mysqli_query($conn, $sql2);

$sql3 = "SELECT * FROM `location-list`";
$result3 = mysqli_query($conn, $sql3);
echo"

<div class='col-lg-3 col-sm-6 col-12'>
<div class='form-group'>
<label>Product Model</label>
<input type='text' name='name' required>
</div>
</div>";?>
<div class='col-lg-3 col-sm-6 col-12'>
    <div class='form-group'>
        <label>Brand</label>
        <select class='select' name='brand' required>
            <option value='' disabled selected>Choose Brand</option>
            <?php
           while ($row = mysqli_fetch_assoc($result)) {
            echo "<option value='" . $row['brand-id'] . "'>" . $row['brand-name'] . "</option>";
        }
        
            ?>
        </select>
    </div>
</div>

<div class='col-lg-3 col-sm-6 col-12'>
<div class='form-group'>
<label>Category</label>
<select class='select' name='category' required>
<option value='' disabled selected>Choose Category</option>
<?php
           while ($row2 = mysqli_fetch_assoc($result2)) {
            echo "<option value='" . $row2['category-id'] . "'>" . $row2['category-name'] . "</option>";
        }
        
            ?>
</select>
</div>
</div>
<div class='col-lg-3 col-sm-6 col-12'>
<div class='form-group'>
<label>Location</label>
<select class='select' name='location' required>
<option value='' disabled selected>Choose Location</option>
<?php
           while ($row3 = mysqli_fetch_assoc($result3)) {
            echo "<option value='" . $row3['room'] . " | " . $row3['rack'] . "'>Room: " . $row3['room'] . " | Rack: " . $row3['rack'] . "</option>";
        }
        
            ?>
</select>
</div>
</div><?php 
echo"
<div class='col-lg-3 col-sm-6 col-12'>
<div class='form-group'>
<label>Product Part Number</label>
<input type='text' name='part_number'>
</div>
</div>
<div class='col-lg-3 col-sm-6 col-12'>
<div class='form-group'>
<label>Product Serial Number</label>
<input type='text' name='serial_number'>
</div>
</div>
<div class='col-lg-3 col-sm-6 col-12'>
<div class='form-group'>
<label>Quantity</label>
<input type='text' pattern='[0-9]*' name='qty' required>
</div>
</div>
<div class='col-lg-3 col-sm-6 col-12'>
<div class='form-group'>
<label>Ownership</label>
<input type='text' name='ownership'>
</div>
</div>
<div class='col-lg-12'>
<div class='form-group'>
<label>Description</label>
<textarea class='form-control' name='des' ></textarea>
</div>
</div>
<div class='col-lg-3 col-sm-6 col-12'>
  <div class='form-group'>
    <label>Status</label>
    <select class='select' id='statusSelect' name='status' required>
      <option value='' disabled selected>Choose Status</option>
      <option value='1'>Available</option>
    </select>
  </div>
</div>

<div class='col-lg-3 col-sm-6 col-12'>
  <div class='form-group' id='remarkColumn' >
    <label>Remark</label>
    <input type='text' name='remark'>
  </div>
</div>

<div class='col-lg-12'>
<input type='submit' class='btn btn-submit me-2' name='submit' value='Submit'>
<a href='inventorylist.php' class='btn btn-cancel'>Cancel</a>
</div>

";
?>
</div>
</div>
</div>
</form>
</div>
</div>
</div>

<script src="assets/js/jquery-3.6.0.min.js"></script>

<script src="assets/js/feather.min.js"></script>

<script src="assets/js/jquery.slimscroll.min.js"></script>

<script src="assets/js/jquery.dataTables.min.js"></script>
<script src="assets/js/dataTables.bootstrap4.min.js"></script>

<script src="assets/js/bootstrap.bundle.min.js"></script>

<script src="assets/plugins/select2/js/select2.min.js"></script>

<script src="assets/plugins/sweetalert/sweetalert2.all.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalerts.min.js"></script>

<script src="assets/js/script.js"></script>
</body>
</html>
<?php
}else{
    header("location: ./signin.php");
}
?>