<?php
require("database.php");
if (isset($_SESSION["user"])) {
    $email = $_SESSION["user"];

    if (isset($_POST["submit"])) {
        // Retrieve form data
        $name = $_POST["name"];
        $email = $_POST["email"];
        $designation = $_POST["designation"];
        $department = $_POST["department"];
        $role = $_POST["role"];
        $des = $_POST["des"];

        // Check if the email already exists
        $emailExists = false;

        $checkEmailQuery = "SELECT COUNT(*) AS count FROM `user-list` WHERE email = ?";
        $checkEmailStmt = mysqli_prepare($conn, $checkEmailQuery);
        mysqli_stmt_bind_param($checkEmailStmt, "s", $email);
        mysqli_stmt_execute($checkEmailStmt);
        mysqli_stmt_bind_result($checkEmailStmt, $emailCount);
        mysqli_stmt_fetch($checkEmailStmt);
        mysqli_stmt_close($checkEmailStmt);

        if ($emailCount > 0) {
            // Email exists in user-list table
            $emailExists = true;
        } else {
            // Check in useracc table
            $checkEmailQuery = "SELECT COUNT(*) AS count FROM `useracc` WHERE email = ?";
            $checkEmailStmt = mysqli_prepare($conn, $checkEmailQuery);
            mysqli_stmt_bind_param($checkEmailStmt, "s", $email);
            mysqli_stmt_execute($checkEmailStmt);
            mysqli_stmt_bind_result($checkEmailStmt, $emailCount);
            mysqli_stmt_fetch($checkEmailStmt);
            mysqli_stmt_close($checkEmailStmt);

            if ($emailCount > 0) {
                // Email exists in useracc table
                $emailExists = true;
            }
        }

        if ($emailExists) {
            // Email already exists, show error message
            echo "<script>alert('Error: Email already exists.'); window.location='adduser.php';</script>";
        } else {
            // Generate unique acc_id
            function getAccountId($length, $conn) {
                $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
                $accId = '';
                $maxTries = 10;
                $tryCount = 0;

                do {
                    $accId = '';
                    for ($i = 0; $i < $length; $i++) {
                        $accId .= $characters[rand(0, strlen($characters) - 1)];
                    }

                    $checkQuery = "SELECT COUNT(*) AS count FROM `user-list` WHERE acc_id = ?";
                    $checkStmt = mysqli_prepare($conn, $checkQuery);
                    mysqli_stmt_bind_param($checkStmt, "s", $accId);
                    mysqli_stmt_execute($checkStmt);
                    mysqli_stmt_bind_result($checkStmt, $count);
                    mysqli_stmt_fetch($checkStmt);
                    mysqli_stmt_close($checkStmt);

                    $tryCount++;
                } while ($count > 0 && $tryCount < $maxTries);

                return $accId;
            }

            $accId = getAccountId(10, $conn);

            // Insert into useracc table
            $userAccQuery = "INSERT INTO `useracc` (email, acc_id, role_id, verified) VALUES (?, ?, ?, 1)";
            $userAccStmt = mysqli_prepare($conn, $userAccQuery);
            mysqli_stmt_bind_param($userAccStmt, "sss", $email, $accId, $role);
            $userAccResult = mysqli_stmt_execute($userAccStmt);

            // Insert into user-list table
            $userListQuery = "INSERT INTO `user-list` (acc_id, email, name, designation, department, description) VALUES (?, ?, ?, ?, ?, ?)";
            $userListStmt = mysqli_prepare($conn, $userListQuery);
            mysqli_stmt_bind_param($userListStmt, "ssssss", $accId, $email, $name, $designation, $department, $des);
            $userListResult = mysqli_stmt_execute($userListStmt);

            if ($userAccResult && $userListResult) {
                echo "<script>alert('The new account has been added.'); window.location='userlist.php';</script>";
            } else {
                echo "<script>alert('Error: " . mysqli_error($conn) . "'); window.location='adduser.php';</script>";
            }

            mysqli_stmt_close($userAccStmt);
            mysqli_stmt_close($userListStmt);
        }
    }
} else {
    header("location: ./signin.php");
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0">
<meta name="description" content="CLL Warehouse Inventory System">
<meta name="author" content="Powered by CLL Systems Sdn Bhd">
<meta name="robots" content="noindex, nofollow">
<title>CLLXWARE-Add User</title>

<link rel='icon' type='image/x-icon' href='https://www.cllsystems.com/wp-content/themes/cllsystems/img/favicon.png'>
<link rel="stylesheet" href="assets/css/bootstrap.min.css">
<link rel="stylesheet" href="assets/css/animate.css">
<link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">
<link rel="stylesheet" href="assets/css/dataTables.bootstrap4.min.css">
<link rel="stylesheet" href="assets/plugins/fontawesome/css/fontawesome.min.css">
<link rel="stylesheet" href="assets/plugins/fontawesome/css/all.min.css">
<link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
<div id="global-loader">
<div class="whirly-loader"> </div>
</div>

<div class="main-wrapper">
<?php 
$role_id = $_SESSION["role_id"];
$sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
$result20 = mysqli_query($conn, $sql20);

if ($result20) {
    $row20 = mysqli_fetch_assoc($result20);
    if ($row20) {
       $create = $row20['user_create'];
       $all = $row20['user_all'];
       if($create != '1' and $all !='1'){
        header("location: ./index.php");
       }
    } else {
        echo "<script>alert('Role data not found')</script>";
    }
} else {
    echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
}
include("header.php");?>
</div>

<div class="page-wrapper">
<div class="content">
<div class="page-header">
<div class="page-title">
<h4>User Management</h4>
<h6>Add New User Profile</h6>
</div>
</div>

<?php
$sql6 = "SELECT * FROM `role-list`";
$result6 = mysqli_query($conn, $sql6);
?>

<form action="#" method="post">
<div class="card">
<div class="card-body">
<div class="row">
<div class="col-lg-3 col-sm-6 col-12">
<div class="form-group">
<label>User Full Name</label>
<input type="text" name="name" required>
</div>
</div>
<div class="col-lg-3 col-sm-6 col-12">
<div class="form-group">
<label>Email</label>
<input type="text" name="email" required>
</div>
</div>
<div class="col-lg-3 col-sm-6 col-12">
<div class="form-group">
<label>Designation</label>
<input type="text" name="designation" required>
</div>
</div>
<div class="col-lg-3 col-sm-6 col-12">
<div class="form-group">
<label>Department</label>
<select class="select" name="department" required>
<option value="" disabled selected>Choose Department</option>
<option value="Admin Department">Admin Department</option>
<option value="Finance Department">Finance Department</option>
<option value="Sales & Marketing">Sales & Marketing</option>
<option value="Technical Department">Technical Department</option>
<option value="Software Team">Software Team</option>
<option value="Network & Security Department">Network & Security Department</option>
</select>
</div>
</div>
<div class="col-lg-3 col-sm-6 col-12">
<div class="form-group">
<label>Role</label>
<select class="select" name="role" required>
<option value="" selected disabled>Choose User Role</option>
<?php
while ($row6 = mysqli_fetch_assoc($result6)) {
    // Check if $row6['hide'] is not equal to 1
    if ($row6['hide'] != 1) {
        // Display the option only if $row6['hide'] is not equal to 1
        echo "<option value='" . $row6['role-id'] . "'>" . $row6['role'] . "</option>";
    }
}
?>
</select>
</div>
</div>

<div class="col-lg-12">
<div class="form-group">
<label>Description</label>
<textarea class="form-control" name="des"></textarea>
</div>
</div>

<div class="col-lg-12">
<input type="submit" class="btn btn-submit me-2" name="submit" value="Submit">
<a href="userlist.php" class="btn btn-cancel">Cancel</a>
</div>
</div>
</div>
</div>
</form>

</div>
</div>
</div>

<script>
document.querySelector('[name="email"]').addEventListener('input', function(event) {
    var emailInput = event.target;
    var emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    var emailHelp = document.getElementById('emailHelp');
    
    if (emailPattern.test(emailInput.value)) {
        emailInput.setCustomValidity('');
        emailHelp.textContent = 'Please enter a valid email address.';
        emailHelp.style.color = '';
    } else {
        emailInput.setCustomValidity('Invalid email address');
        emailHelp.textContent = 'Please enter a valid email address.';
        emailHelp.style.color = 'red';
    }
});
</script>

<script src="assets/js/jquery-3.6.0.min.js"></script>
<script src="assets/js/feather.min.js"></script>
<script src="assets/js/jquery.slimscroll.min.js"></script>
<script src="assets/js/jquery.dataTables.min.js"></script>
<script src="assets/js/dataTables.bootstrap4.min.js"></script>
<script src="assets/js/bootstrap.bundle.min.js"></script>
<script src="assets/plugins/select2/js/select2.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalert2.all.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalerts.min.js"></script>
<script src="assets/js/script.js"></script>
</body>
</html>
