<?php
/**
 * Auto Email Runner - Enhanced version with direct database integration
 * Run this via email_scheduler.php daily at configured times
 *
 * This script handles:
 * 1. Onboarding reminders from onboarding_cron_config table
 * 2. IT support notifications from onboarding_email_config table
 *
 * Note: Time checking and daily scheduling is handled by email_scheduler.php
 */

// Set timezone
date_default_timezone_set('Asia/Kuala_Lumpur');

// Suppress session warnings when running from command line
if (php_sapi_name() === 'cli') {
    // Running from command line - suppress all output during database include
    $old_error_reporting = error_reporting(E_ALL & ~E_WARNING);
    ob_start();
    require_once __DIR__ . '/database.php';
    ob_end_clean();
    error_reporting($old_error_reporting);
} else {
    // Running from web browser - normal include
    require_once __DIR__ . '/database.php';
}
require_once __DIR__ . '/MAILER/vendor/autoload.php';

use PHPMail<PERSON>\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

// Create logs directory
if (!file_exists(__DIR__ . '/logs')) {
    mkdir(__DIR__ . '/logs', 0755, true);
}

// Log function
if (!function_exists('log_runner')) {
    function log_runner($message) {
        $timestamp = date('Y-m-d H:i:s');
        $log_entry = "[$timestamp] $message" . PHP_EOL;
        file_put_contents(__DIR__ . '/logs/auto_runner.log', $log_entry, FILE_APPEND | LOCK_EX);
        echo $log_entry;
    }
}

/**
 * Clean up old lock files (older than 2 days)
 */
function cleanup_old_lock_files() {
    $logs_dir = __DIR__ . '/logs/';
    $files = glob($logs_dir . '*_reminder_*.lock') + glob($logs_dir . '*_notification_*.lock');
    $cutoff_time = time() - (2 * 24 * 60 * 60); // 2 days ago

    foreach ($files as $file) {
        if (file_exists($file) && filemtime($file) < $cutoff_time) {
            unlink($file);
            log_runner("Cleaned up old lock file: " . basename($file));
        }
    }
}

/**
 * Check onboarding_cron_config table and send reminders if time matches
 */
if (!function_exists('check_and_send_onboarding_reminders')) {
function check_and_send_onboarding_reminders($conn) {
    log_runner("Checking onboarding reminders...");

    // Check daily lock file to prevent duplicate sends
    $today = date('Y-m-d');
    $lock_file = __DIR__ . '/logs/onboarding_reminder_' . $today . '.lock';

    if (file_exists($lock_file)) {
        log_runner("Onboarding reminder already sent today ($today). Skipping to prevent duplicates.");
        return;
    }

    // Get cron configuration for onboarding reminders
    $config_query = "SELECT * FROM `onboarding_cron_config` WHERE `config_type` = 'cron_reminder' AND `is_enabled` = 1";
    $config_result = mysqli_query($conn, $config_query);
    $config = mysqli_fetch_assoc($config_result);

    if (!$config) {
        log_runner("Onboarding cron job is disabled or not configured. Skipping.");
        return;
    }

    log_runner("Processing onboarding reminders for $today...");
    
    // Get today's onboarding records
    $today = date('Y-m-d');
    $query = "
        SELECT ol.*,
               dl.department_name,
               ml.manager_name,
               ofl.office_name as office_name_actual
        FROM `onboarding_list` ol
        LEFT JOIN `department_list` dl ON ol.department_id = dl.department_id
        LEFT JOIN `manager_list` ml ON ol.manager_id = ml.manager_id
        LEFT JOIN `office_list` ofl ON ol.office_id = ofl.office_id
        WHERE DATE(ol.onboarding_date) = ?
        ORDER BY ol.onboarding_date ASC
    ";
    
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 's', $today);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    $onboarding_records = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $onboarding_records[] = $row;
    }
    mysqli_stmt_close($stmt);
    
    if (empty($onboarding_records)) {
        log_runner("No onboarding records found for today ($today). Skipping onboarding reminders.");
        return;
    }
    
    log_runner("Found " . count($onboarding_records) . " onboarding records for today");

    // Send general onboarding reminder
    send_general_onboarding_reminder($onboarding_records, $config);
    log_runner("Sent general onboarding reminder to configured recipients");

    // Create daily lock file to prevent duplicate sends
    file_put_contents($lock_file, date('Y-m-d H:i:s') . " - Onboarding reminder sent successfully\n");
    log_runner("Created daily lock file to prevent duplicate sends: " . basename($lock_file));
}
}

/**
 * Check onboarding_email_config table and send IT support notifications if time matches
 */
if (!function_exists('check_and_send_it_support_notifications')) {
function check_and_send_it_support_notifications($conn) {
    log_runner("Checking IT support notifications...");

    // Check daily lock file to prevent duplicate sends
    $today = date('Y-m-d');
    $lock_file = __DIR__ . '/logs/it_support_notification_' . $today . '.lock';

    if (file_exists($lock_file)) {
        log_runner("IT support notification already sent today ($today). Skipping to prevent duplicates.");
        return;
    }

    // Get IT support configuration
    $config_query = "SELECT * FROM `onboarding_email_config` WHERE `config_type` = 'it_support'";
    $config_result = mysqli_query($conn, $config_query);
    $config = mysqli_fetch_assoc($config_result);

    if (!$config || empty($config['cron_time'])) {
        log_runner("IT support reminder time is not configured. Skipping.");
        return;
    }

    log_runner("Processing IT support notifications for $today...");
    
    // Get today's onboarding records with IT assignments
    $today = date('Y-m-d');
    $query = "
        SELECT ol.*,
               dl.department_name,
               ml.manager_name,
               ofl.office_name as office_name_actual
        FROM `onboarding_list` ol
        LEFT JOIN `department_list` dl ON ol.department_id = dl.department_id
        LEFT JOIN `manager_list` ml ON ol.manager_id = ml.manager_id
        LEFT JOIN `office_list` ofl ON ol.office_id = ofl.office_id
        WHERE DATE(ol.onboarding_date) = ?
        AND (ol.it_primary_email IS NOT NULL OR ol.it_backup_email IS NOT NULL)
        ORDER BY ol.onboarding_date ASC
    ";
    
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 's', $today);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    $onboarding_records = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $onboarding_records[] = $row;
    }
    mysqli_stmt_close($stmt);
    
    if (empty($onboarding_records)) {
        log_runner("No onboarding records with IT assignments found for today ($today). Skipping IT notifications.");
        return;
    }
    
    log_runner("Found " . count($onboarding_records) . " onboarding records with IT assignments for today");

    // Send IT support notifications
    send_it_support_notifications($onboarding_records, $config);
    log_runner("Sent IT support notifications to assigned members");

    // Create daily lock file to prevent duplicate sends
    file_put_contents($lock_file, date('Y-m-d H:i:s') . " - IT support notification sent successfully\n");
    log_runner("Created daily lock file to prevent duplicate sends: " . basename($lock_file));
}
}

/**
 * Send general onboarding reminder to configured email addresses
 */
if (!function_exists('send_general_onboarding_reminder')) {
function send_general_onboarding_reminder($records, $config) {
    $email_addresses = array_filter(array_map('trim', explode("\n", $config['email_addresses'])));

    if (empty($email_addresses)) {
        log_runner('No email addresses configured for general reminder');
        return;
    }

    // Send one email per employee with placeholders replaced
    foreach ($records as $record) {
        try {
            $mail = new PHPMailer(true);
            $mail->isSMTP();
            $mail->Host = 'smtp.office365.com';
            $mail->SMTPAuth = true;
            $mail->Username = '<EMAIL>';
            $mail->Password = '&[i3F6}0hOw6';
            $mail->SMTPSecure = 'tls';
            $mail->Port = 587;
            $mail->setFrom('<EMAIL>', 'CLLXWARE');

            foreach ($email_addresses as $email) {
                $mail->addAddress($email);
            }

            $mail->isHTML(true);

            // Subject with employee name
            $subject = $config['email_subject'] ?? 'Onboarding Reminder - New Joiner: {employee_name}';
            $subject = str_replace('{employee_name}', $record['full_name'], $subject);
            $mail->Subject = $subject;

            // Build body from template with placeholders
            $email_template = $config['email_template'] ?? '';
            $email_body = str_replace('{employee_name}', htmlspecialchars($record['full_name']), $email_template);
            $email_body = str_replace('{employee_id}', htmlspecialchars($record['employee_id']), $email_body);
            $email_body = str_replace('{department}', htmlspecialchars($record['department_name'] ?? 'Not specified'), $email_body);
            $email_body = str_replace('{designation}', htmlspecialchars($record['job_title']), $email_body);
            $email_body = str_replace('{onboarding_date}', htmlspecialchars($record['onboarding_date']), $email_body);
            $email_body = str_replace('{reporting_manager}', htmlspecialchars($record['manager_name'] ?? 'Not assigned'), $email_body);
            $email_body = str_replace('{office_name}', htmlspecialchars($record['office_name_actual'] ?? 'Not specified'), $email_body);
            $email_body = str_replace('{company_email}', htmlspecialchars($record['company_email'] ?? 'Not set'), $email_body);

            // Personal image: embed if exists, otherwise remove the line
            if (!empty($record['personal_image']) && file_exists($record['personal_image'])) {
                // Use embedded image for better email client compatibility
                $image_path = $record['personal_image'];
                $cid = 'employee_photo_' . $record['id'];

                // Add embedded attachment
                $mail->addEmbeddedImage($image_path, $cid, basename($image_path));

                // Use CID reference in email body with explicit dimensions for Outlook compatibility
                $email_body = str_replace(
                    '{personal_image}',
                    '<br><img src="cid:' . $cid . '" alt="Employee Photo" width="80" height="80" style="width:80px;height:80px;border-radius:50%;margin-top:8px;border:2px solid #e0e0e0;object-fit:cover;display:block;">',
                    $email_body
                );
            } else {
                // Remove the personal image line completely if no image
                $email_body = preg_replace('/.*Personal Image.*{personal_image}.*\n?/', '', $email_body);
                $email_body = str_replace('{personal_image}', '', $email_body);
            }

            // Also allow list-style template variables
            $onboarding_list = "- " . $record['full_name'] . " (" . $record['employee_id'] . ") - " . ($record['department_name'] ?? 'Not specified');
            $email_body = str_replace('{onboarding_list}', $onboarding_list, $email_body);
            $email_body = str_replace('{current_date}', date('Y-m-d'), $email_body);
            $email_body = str_replace('{total_count}', count($records), $email_body);

            $mail->Body = nl2br($email_body);
            $mail->send();
            log_runner("Onboarding reminder sent for " . $record['full_name'] . " to: " . implode(', ', $email_addresses));
        } catch (Exception $e) {
            log_runner('General onboarding reminder email error: ' . $e->getMessage());
        }
    }
}
}

/**
 * Send IT support notifications to primary and backup IT members
 */
if (!function_exists('send_it_support_notifications')) {
function send_it_support_notifications($records, $config) {
    foreach ($records as $record) {
        $it_emails = [];

        // Collect IT emails for this record
        if (!empty($record['it_primary_email'])) {
            $it_emails[] = $record['it_primary_email'];
        }
        if (!empty($record['it_backup_email'])) {
            $it_emails[] = $record['it_backup_email'];
        }

        if (empty($it_emails)) {
            log_runner("No IT emails assigned for " . $record['full_name'] . ". Skipping.");
            continue;
        }

        // Generate workflow link
        $base_url = (isset($_SERVER['HTTP_HOST']) ?
            ((isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://') .
             $_SERVER['HTTP_HOST'] . dirname($_SERVER['PHP_SELF'])) :
            'https://cis.cllsystems.com:9443/staging');
        $workflow_link = rtrim($base_url, '/') . "/onboarding_workflow.php?id=" . urlencode($record['onboarding_id']);

        // Use the template from database or default
        $email_template = $config['email_template'] ?? 'Dear IT Support Team,

A new employee requires laptop setup:

Employee Information:
- Name: {employee_name}
- Employee ID: {employee_id}
- Department: {department}
- Designation: {designation}
- Onboarding Date: {onboarding_date}
- Reporting Manager: {reporting_manager}
- Office Location: {office_name}

Please click the link below to manage the onboarding workflow:
{workflow_link}

Priority: Primary contacts should handle this request. Backup contacts are CC\'d for awareness.

Best regards,
HR Team';

        // Replace template variables
        $email_body = str_replace('{employee_name}', htmlspecialchars($record['full_name']), $email_template);
        $email_body = str_replace('{employee_id}', htmlspecialchars($record['employee_id']), $email_body);
        $email_body = str_replace('{department}', htmlspecialchars($record['department_name'] ?? 'Not specified'), $email_body);
        $email_body = str_replace('{designation}', htmlspecialchars($record['job_title']), $email_body);
        $email_body = str_replace('{onboarding_date}', htmlspecialchars($record['onboarding_date']), $email_body);
        $email_body = str_replace('{reporting_manager}', htmlspecialchars($record['manager_name'] ?? 'Not assigned'), $email_body);
        $email_body = str_replace('{office_name}', htmlspecialchars($record['office_name_actual'] ?? 'Not specified'), $email_body);
        $email_body = str_replace('{workflow_link}', $workflow_link, $email_body);

        // Send email to each IT member
        foreach ($it_emails as $it_email) {
            try {
                $mail = new PHPMailer(true);
                $mail->isSMTP();
                $mail->Host = 'smtp.office365.com';
                $mail->SMTPAuth = true;
                $mail->Username = '<EMAIL>';
                $mail->Password = '&[i3F6}0hOw6';
                $mail->SMTPSecure = 'tls';
                $mail->Port = 587;
                $mail->setFrom('<EMAIL>', 'CLLXWARE IT Assignment');

                $mail->addAddress($it_email);

                // Subject with employee name
                $subject = $config['email_subject'] ?? 'Laptop Setup Required - New Joiner: {employee_name}';
                $subject = str_replace('{employee_name}', $record['full_name'], $subject);
                $mail->Subject = $subject;
                $mail->isHTML(true);

                // Compute completion link per recipient
                $completion_token = hash('sha256', $record['onboarding_id'] . $it_email . 'IT_COMPLETION_2024');
                $completion_link = rtrim($base_url, '/') . "/it_completion.php?id=" . urlencode($record['onboarding_id']) . "&email=" . urlencode($it_email) . "&token=" . urlencode($completion_token);

                // Build body with all placeholders
                $email_template_body = $email_template;
                $email_body = str_replace('{employee_name}', htmlspecialchars($record['full_name']), $email_template_body);
                $email_body = str_replace('{employee_id}', htmlspecialchars($record['employee_id']), $email_body);
                $email_body = str_replace('{department}', htmlspecialchars($record['department_name'] ?? 'Not specified'), $email_body);
                $email_body = str_replace('{designation}', htmlspecialchars($record['job_title']), $email_body);
                $email_body = str_replace('{onboarding_date}', htmlspecialchars($record['onboarding_date']), $email_body);
                $email_body = str_replace('{reporting_manager}', htmlspecialchars($record['manager_name'] ?? 'Not assigned'), $email_body);
                $email_body = str_replace('{office_name}', htmlspecialchars($record['office_name_actual'] ?? 'Not specified'), $email_body);
                $email_body = str_replace('{company_email}', htmlspecialchars($record['company_email'] ?? 'Not set'), $email_body);
                $email_body = str_replace('{company_password}', htmlspecialchars(base64_decode($record['company_email_password'] ?? '')), $email_body);
                $email_body = str_replace('{workflow_link}', $workflow_link, $email_body);
                $email_body = str_replace('{completion_link}', $completion_link, $email_body);

                $mail->Body = nl2br($email_body);

                if ($mail->send()) {
                    log_runner("IT assignment email sent to " . $it_email . " for " . $record['full_name']);
                } else {
                    log_runner("Failed to send IT assignment email to " . $it_email . ": " . $mail->ErrorInfo);
                }

            } catch (Exception $e) {
                log_runner("Error sending email to " . $it_email . ": " . $e->getMessage());
            }
        }
    }
}
}

// Execute the email runner
log_runner("Auto Email Runner Started");

try {
    // Clean up old lock files first
    cleanup_old_lock_files();

    // Determine which tasks to run (default to both if not specified)
    $run_types = $GLOBALS['RUN_TYPES'] ?? ['onboarding', 'it_support'];

    // Check and send onboarding reminders if requested
    if (in_array('onboarding', $run_types)) {
        check_and_send_onboarding_reminders($conn);
    } else {
        log_runner("Skipping onboarding reminders (not scheduled for this timeslot)");
    }

    // Check and send IT support notifications if requested
    if (in_array('it_support', $run_types)) {
        check_and_send_it_support_notifications($conn);
    } else {
        log_runner("Skipping IT support notifications (not scheduled for this timeslot)");
    }

    log_runner("Auto Email Runner Completed Successfully");

} catch (Exception $e) {
    log_runner("Error: " . $e->getMessage());
} catch (Error $e) {
    log_runner("Fatal Error: " . $e->getMessage());
}

?>
