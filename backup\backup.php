<?php
require("../database.php");
use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\SMTP;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\Exception;
include_once(dirname(__FILE__) . '/Mysqldump/Mysqldump.php');
    $dump = new Ifsnop\Mysqldump\Mysqldump('mysql:host=localhost;dbname=cll-inventory', 'root', '');
    date_default_timezone_set('Asia/Kuala_Lumpur');
    $malaysiaTimestamp = time();
    $formattedMalaysiaTimestamp = date('Y-m-d_H-i-s', $malaysiaTimestamp);
    $dump->start("sql/$formattedMalaysiaTimestamp.sql");


        $sql11 = "SELECT useracc.email
                FROM `role-list`
                INNER JOIN `useracc` ON `role-list`.`role-id` = `useracc`.`role_id`
                WHERE `role-list`.`backup` = '1'";
         $result11 = mysqli_query($conn, $sql11);
         if ($result11->num_rows > 0) {
             // Array to hold email addresses
             $emails = array();
             
             // Fetching email addresses
             while ($row11 = mysqli_fetch_assoc($result11)) {
                 $emails[] = $row11['email'];
             }
    require '../MAILER/vendor/autoload.php';
                        
    //Create an instance; passing `true` enables exceptions
    $mail = new PHPMailer(true);
    
    try {
        
    //  $mail->SMTPDebug = SMTP::DEBUG_SERVER;                      //Enable verbose debug output
    
        $mail->isSMTP();    
    
    //SMTPOptions for Office 365
        $mail->SMTPOptions = array(
        'ssl' => array(
            'verify_peer' => false,
            'verify_peer_name' => false,
            'allow_self_signed' => true
        ));                                                             //Send using SMTP
    
    // Office 365 SMTP HOST
       $mail->Host       = 'smtp.office365.com';
       $mail->SMTPAuth   = true;                                      //Enable SMTP authentication
       $mail->Username   = '<EMAIL>';                      //SMTP username
       $mail->Password   = '&[i3F6}0hOw6';                         //SMTP password
       $mail->Port       = 587;                                        //TCP port to connect to; use 587 if 
    
    //Set From Email ID and NAME
        $mail->setFrom('<EMAIL>', 'CIS');
        
    //Recipients
    
    foreach ($emails as $email) {
        $mail->addAddress($email, 'Admin');
    }               //Add a recipient To
        // $mail->addCC('<EMAIL>');                              //Add a recipient Cc
        // $mail->addBCC('<EMAIL>');                            //Add a recipient Bcc
        $mail->SMTPSecure = 'tls';                                      //SMTPS uses TLS cryptographic protocols for improved security
    
        //Attachments
        $mail->addAttachment("sql/$formattedMalaysiaTimestamp.sql");                 //Add attachments
    //    $mail->addAttachment('gmail.png', 'new.jpg');                 //Add attachment to replace attachment name
    
        //Content
        $mail->isHTML(true);                                           //Set email format to HTML
        $mail->Subject = "CIS - Daily Backup";                         //Subject Email 
        $mail->Body  =  '<html><body>';                                //Email Body
        $mail->Body .="
        <table border='0'>
                            
                            <body style='font-family: 'Arial', sans-serif; background-color: #f4f4f4; margin: 0; padding: 0;'>
                            <div class='container' style='max-width: 600px; margin: 0 auto; padding: 20px; background-color: #ffffff; box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);'>
                               
                                <div class='otp-container' style='background-color: #f2f2f2; padding: 15px; margin-top: 20px; text-align: center;'>
                                    <p style='color: #555555;'>Dear Master Admin,</p>
                                    <p style='color: #555555;'>The CLL Inventory System database daily backup as attached below. <br> Backup Date: $formattedMalaysiaTimestamp <br> The next backup will be auto generated next day.</p>
                                    <p style='color: #555555;'><a href='https://www.cllsystems.com' style='text-decoration:none; color: #555555;'><b>CLL Systems Sdn Bhd</b></a>
                                        </p>
                                </div>
                            </div>
                        </body>
                            </table>
        ";                          
        $mail->Body .= '</html></body>';
        
    if ($mail->send()) {                                       
       
    }else{
       echo "Message could not be sent. Mailer Error: {$mail->ErrorInfo} <br> Mailer Debug:".$mail->SMTPDebug = SMTP::DEBUG_SERVER;
    }
    
    } catch (Exception $e) {
        echo "Message could not be sent. Mailer Error: {$mail->ErrorInfo}";
    }
}

?>