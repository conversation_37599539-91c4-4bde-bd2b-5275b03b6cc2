-- mysqldump-php https://github.com/ifsnop/mysqldump-php
--
-- Host: localhost	Database: cll-inventory
-- ------------------------------------------------------
-- Server version 	10.4.32-MariaDB
-- Date: Tue, 26 Mar 2024 17:09:33 +0800

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40101 SET @OLD_AUTOCOMMIT=@@AUTOCOMMIT */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `brand-list`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `brand-list` (
  `id` int(255) NOT NULL AUTO_INCREMENT,
  `brand-name` varchar(255) NOT NULL,
  `brand-id` varchar(255) NOT NULL,
  `des` varchar(999) NOT NULL,
  `created` varchar(255) NOT NULL,
  `hide` int(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `brand-list`
--

LOCK TABLES `brand-list` WRITE;
/*!40000 ALTER TABLE `brand-list` DISABLE KEYS */;
SET autocommit=0;
INSERT INTO `brand-list` VALUES (1,'Cisco','3271959233','CSV import added this Brand ID','Alex Ang',0),(2,'Juniper Networks','3107439717','CSV import added this Brand ID','Alex Ang',0),(3,'APC','9223873625','CSV import added this Brand ID','Alex Ang',0),(4,'Aruba Networks','4268328558','CSV import added this Brand ID','Alex Ang',0),(5,'Belkin','4174530348','CSV import added this Brand ID','Alex Ang',0),(6,'Fortinet','1634112121','CSV import added this Brand ID','Alex Ang',0),(7,'Panduit','2070235736','CSV import added this Brand ID','Alex Ang',0),(8,'Tripp Lite','0742870078','CSV import added this Brand ID','Alex Ang',0),(9,'Corning','0244180542','CSV import added this Brand ID','Alex Ang',0),(10,'Dell EMC','3501829193','CSV import added this Brand ID','Alex Ang',0),(11,'Synology','5021987615','CSV import added this Brand ID','Alex Ang',0),(12,'IOGEAR','8131462793','CSV import added this Brand ID','Alex Ang',0),(13,'HPE','0295776285','CSV import added this Brand ID','Alex Ang',0),(14,'HPE2','6346260691','CSV import added this Brand ID','Ang Teck Yan',0);
/*!40000 ALTER TABLE `brand-list` ENABLE KEYS */;
UNLOCK TABLES;
COMMIT;

-- Dumped table `brand-list` with 14 row(s)
--

--
-- Table structure for table `category-list`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `category-list` (
  `id` int(255) NOT NULL AUTO_INCREMENT,
  `category-id` varchar(255) NOT NULL,
  `category-name` varchar(255) NOT NULL,
  `des` varchar(999) NOT NULL,
  `created` varchar(255) NOT NULL,
  `hide` int(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `category-list`
--

LOCK TABLES `category-list` WRITE;
/*!40000 ALTER TABLE `category-list` DISABLE KEYS */;
SET autocommit=0;
INSERT INTO `category-list` VALUES (1,'7577505059','Infrastructure','CSV import added this Category ID','Alex Ang',1),(2,'5501310663','Cat6','CSV import added this Category ID','Alex Ang',0),(3,'8848462118','Router','CSV import added this Category ID','Alex Ang',0),(4,'3431257563','NAS','CSV import added this Category ID','Alex Ang',0),(5,'0992204067','Switch','CSV import added this Category ID','Alex Ang',0),(6,'3758016038','Switch2','CSV import added this Category ID','Ang Teck Yan',0),(7,'3386758409','Power Supply','CSV import added this Category ID','Alex Ang',0);
/*!40000 ALTER TABLE `category-list` ENABLE KEYS */;
UNLOCK TABLES;
COMMIT;

-- Dumped table `category-list` with 7 row(s)
--

--
-- Table structure for table `inventory-list`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `inventory-list` (
  `id` int(255) NOT NULL AUTO_INCREMENT,
  `product-name` varchar(255) NOT NULL,
  `brand-id` varchar(255) NOT NULL,
  `category-id` varchar(255) NOT NULL,
  `location-id` varchar(255) NOT NULL,
  `part-number` varchar(255) NOT NULL,
  `serial-number` varchar(255) NOT NULL,
  `quantity` int(255) NOT NULL,
  `ownership` varchar(255) NOT NULL,
  `des` varchar(999) NOT NULL,
  `status` int(255) NOT NULL,
  `remark` varchar(255) NOT NULL,
  `created` varchar(255) NOT NULL,
  `inventory-id` varchar(255) NOT NULL,
  `reserve_qty` int(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=125 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `inventory-list`
--

LOCK TABLES `inventory-list` WRITE;
/*!40000 ALTER TABLE `inventory-list` DISABLE KEYS */;
SET autocommit=0;
INSERT INTO `inventory-list` VALUES (1,'Server Rack','3271959233','7577505059','001 | 01','SR-001','CISCOSR001',2,'Owned','42U server rack with locking doors',1,'','Alex Ang','N7Uo5Mw21H',0),(2,'Network Switch','3107439717','7577505059','001 | 02','NS-002','JNWSW002',3,'Leased','24-port gigabit Ethernet switch',1,'','Alex Ang','njqTK6QVV4',0),(3,'UPS (Uninterruptible Power Supply)','9223873625','7577505059','001 | 03','UPS-003','APCUPS003',1,'Owned','1500VA UPS for power backup',1,'','Alex Ang','jIFeG2AYq1',0),(4,'Wireless Access Point','4268328558','7577505059','001 | 01','WAP-004','ARUBAWAP004',5,'Owned','Dual-band Wi-Fi access point',1,'','Alex Ang','9082JYHfKl',0),(5,'Ethernet Cable (Cat6)','4174530348','5501310663','001 | 03','EC-005','BLKEC005',100,'Owned','5-foot Cat6 Ethernet cable',1,'','Alex Ang','zZIcL4CFxz',0),(6,'Firewall Appliance','1634112121','7577505059','001 | 01','FA-006','FORTFA006',2,'Owned','Next-generation firewall appliance',1,'','Alex Ang','7ixWLmQnus',0),(7,'Patch Panel','2070235736','7577505059','001 | 01','PP-007','PANDPP007',1,'Owned','24-port patch panel for Ethernet connections',1,'','Alex Ang','jK6pZtQMVD',0),(8,'Power Distribution Unit (PDU)','0742870078','7577505059','001 | 02','PDU-008','TRIPDU008',3,'Leased','20-outlet rackmount PDU',1,'','Alex Ang','2CWLetXUyY',0),(9,'Fiber Optic Cable','0244180542','7577505059','001 | 04','FOC-009','CORNFOC009',50,'Owned','Single-mode fiber optic cable (10 meters)',1,'','Alex Ang','1P4sG4L0Ri',0),(10,'Server','3501829193','7577505059','001 | 01','SVR-010','DELLSVR010',4,'Leased','Dell PowerEdge R740 rack server',1,'','Alex Ang','FRLwrwQnK7',0),(11,'Router','3271959233','8848462118','001 | 04','RTR-011','CISRTR011',2,'Owned','Cisco ISR 4000 series router',1,'','Alex Ang','QL3kWYKXSR',0),(12,'Power Cable','9223873625','7577505059','001 | 05','PC-012','APCCAB012',50,'Owned','6-foot power cable with IEC C13 connector',1,'','Alex Ang','nrRET67GRU',0),(13,'Network Attached Storage (NAS)','5021987615','3431257563','001 | 02','NAS-013','SYNNAS013',1,'Owned','Synology DiskStation DS220+ NAS',1,'','Alex Ang','Fs54y1LIzN',0),(14,'KVM Switch','8131462793','7577505059','001 | 05','KVM-014','IOGKVM014',1,'Owned','4-port USB VGA KVM switch',1,'','Alex Ang','cVeW9jBHRm',0),(15,'Ethernet Switch Module','0295776285','7577505059','Vespa Room |  01','ESM-015','HPEESM015',3,'Owned','8-port Gigabit Ethernet switch module for HP ProCurve switch',1,'','Alex Ang','Os4ao9YOK9',0),(114,'Aruba X372 12VDC 250W PS','0295776285','3386758409','Vespa Room | 01','JL085A','TH33GZ8VHZ',1,'Genting Malaysia Berhad','SO-004531 CCTV - Aruba Switches',1,'','Alex Ang','GkOOHk4pRz',0),(115,'Aruba X371 12VDC 250W PS','0295776285','3386758409','Vespa Room | 01','JL085A','TH33GZ8ZL4',1,'Genting Malaysia Berhad','SO-004531 CCTV - Aruba Switches',1,'','Alex Ang','z7I5nDWdzd',0),(116,'Aruba X371 12VDC 250W PS','0295776285','3386758409','Vespa Room | 01','JL085A','TH35GZ90X6',1,'Genting Malaysia Berhad','SO-004531 CCTV - Aruba Switches',1,'','Alex Ang','8UjO3t3UmJ',0),(117,'Aruba X371 12VDC 250W PS','0295776285','3386758409','Vespa Room | 01','JL085A','TH35GZ90GK',1,'Genting Malaysia Berhad','SO-004531 CCTV - Aruba Switches',1,'','Alex Ang','kjwbYtevPB',0),(118,'Aruba X371 12VDC 250W PS','0295776285','3386758409','Vespa Room | 01','JL085A','TH35GZ90FH',1,'Genting Malaysia Berhad','SO-004531 CCTV - Aruba Switches',1,'','Alex Ang','WnNnzVHtWL',0),(119,'Aruba X371 12VDC 250W PS','0295776285','3386758409','Vespa Room | 01','JL085A','TH35GZ9109',1,'Genting Malaysia Berhad','SO-004531 CCTV - Aruba Switches',1,'','Alex Ang','YOksPCHiWp',0),(120,'test','3271959233','5501310663','001 | 01','-','-',2,'test','',1,'','Alex Ang','qosGBJJeeQ',0),(121,'test name','3107439717','8848462118','001 | 02','-','-',2,'test','',1,'','Ang Teck Yan','ErnZl8yd6G',0),(122,'test5','6346260691','3758016038','Test Room 01 | 1','test','test',2,'test','test',1,'','Ang Teck Yan','emMGDg9Sec',0),(123,'test6','6346260691','3758016038','Test Room 01 | 1','test','test',2,'test','test',1,'','Ang Teck Yan','qLHbSDRYZT',0),(124,'test2','3271959233','8848462118','001 | 02','test','test',2,'test','1',1,'','Ang Teck Yan','vseTWlUTOg',0);
/*!40000 ALTER TABLE `inventory-list` ENABLE KEYS */;
UNLOCK TABLES;
COMMIT;

-- Dumped table `inventory-list` with 26 row(s)
--

--
-- Table structure for table `location-list`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `location-list` (
  `id` int(255) NOT NULL AUTO_INCREMENT,
  `room` varchar(255) NOT NULL,
  `rack` varchar(255) NOT NULL,
  `des` varchar(255) NOT NULL,
  `created` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=28 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `location-list`
--

LOCK TABLES `location-list` WRITE;
/*!40000 ALTER TABLE `location-list` DISABLE KEYS */;
SET autocommit=0;
INSERT INTO `location-list` VALUES (1,'001','01','CSV import added this Location ID','Alex Ang'),(2,'001','02','CSV import added this Location ID','Alex Ang'),(3,'001','03','CSV import added this Location ID','Alex Ang'),(4,'001','04','CSV import added this Location ID','Alex Ang'),(5,'001','05','CSV import added this Location ID','Alex Ang'),(6,'001','06','CSV import added this Location ID','Alex Ang'),(25,'Vespa Room','01','CSV import added this Location ID','Alex Ang'),(26,'Vespa Room','01','CSV import added this Location ID','Alex Ang'),(27,'Test Room 01','1','CSV import added this Location ID','Ang Teck Yan');
/*!40000 ALTER TABLE `location-list` ENABLE KEYS */;
UNLOCK TABLES;
COMMIT;

-- Dumped table `location-list` with 9 row(s)
--

--
-- Table structure for table `notification`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `notification` (
  `id` int(255) NOT NULL AUTO_INCREMENT,
  `acc_id` varchar(255) NOT NULL,
  `request_id` varchar(255) NOT NULL,
  `timestamp` varchar(255) NOT NULL,
  `read_msg` int(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=24 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `notification`
--

LOCK TABLES `notification` WRITE;
/*!40000 ALTER TABLE `notification` DISABLE KEYS */;
SET autocommit=0;
INSERT INTO `notification` VALUES (9,'cIvf72h28P','6z6Tr9xaMU','2024-03-14 12:55:22',1),(10,'cIvf72h28P','n67FGV4UeB','2024-03-14 12:57:04',1),(11,'cIvf72h28P','UBmh2QGWAd','2024-03-14 12:58:01',1),(12,'cIvf72h28P','9oVbtewTZD','2024-03-14 13:00:19',1),(13,'cIvf72h28P','4Mq4SMZ0QV','2024-03-14 13:01:23',1),(14,'cIvf72h28P','4QLN5rA8ni','2024-03-14 13:03:00',1),(15,'cIvf72h28P','sYKkJdzZif','2024-03-14 13:04:29',1),(16,'cIvf72h28P','BQW79LkZz7','2024-03-14 13:06:27',1),(17,'cIvf72h28P','ASds1Lntk2','2024-03-14 13:07:54',1),(18,'cIvf72h28P','uBvfAaveqm','2024-03-14 13:19:05',0),(19,'cIvf72h28P','DtxXBuunf2','2024-03-14 13:34:20',0),(20,'cIvf72h28P','KJhPvgJDQg','2024-03-14 13:40:01',0),(21,'cIvf72h28P','X6ChPTvQu2','2024-03-14 17:10:15',0),(22,'cIvf72h28P','R2eCYJQiYp','2024-03-14 17:12:07',0),(23,'cIvf72h28P','IwkPBMW5Mm','2024-03-14 17:13:24',0);
/*!40000 ALTER TABLE `notification` ENABLE KEYS */;
UNLOCK TABLES;
COMMIT;

-- Dumped table `notification` with 15 row(s)
--

--
-- Table structure for table `request-list`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `request-list` (
  `id` int(255) NOT NULL AUTO_INCREMENT,
  `request-id` varchar(255) NOT NULL,
  `inventory-id` varchar(255) NOT NULL,
  `acc_id` varchar(255) NOT NULL,
  `company_name` varchar(255) NOT NULL,
  `company_category` varchar(255) NOT NULL,
  `request_date` varchar(255) NOT NULL,
  `approval_date` varchar(255) NOT NULL,
  `collection_date` varchar(255) NOT NULL,
  `return_date` varchar(255) NOT NULL,
  `quantity` int(255) NOT NULL,
  `approval_status` int(255) NOT NULL,
  `approval_remark` varchar(255) NOT NULL,
  `request_status` int(255) NOT NULL,
  `request_remark` varchar(255) NOT NULL,
  `inventory_status` int(255) NOT NULL,
  `inventory_remark` varchar(255) NOT NULL,
  `approval_made_by` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=84 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `request-list`
--

LOCK TABLES `request-list` WRITE;
/*!40000 ALTER TABLE `request-list` DISABLE KEYS */;
SET autocommit=0;
INSERT INTO `request-list` VALUES (39,'6z6Tr9xaMU','zZIcL4CFxz','cIvf72h28P','test_smtp','test','2024-03-14 12:55:22','','01-03-2024','31-03-2024',2,0,'',0,'',0,'',''),(40,'6z6Tr9xaMU','1P4sG4L0Ri','cIvf72h28P','test_smtp','test','2024-03-14 12:55:22','','01-03-2024','31-03-2024',1,0,'',0,'',0,'',''),(41,'6z6Tr9xaMU','kjwbYtevPB','cIvf72h28P','test_smtp','test','2024-03-14 12:55:22','','01-03-2024','31-03-2024',1,0,'',0,'',0,'',''),(42,'6z6Tr9xaMU','WnNnzVHtWL','cIvf72h28P','test_smtp','test','2024-03-14 12:55:22','','01-03-2024','31-03-2024',1,0,'',0,'',0,'',''),(43,'n67FGV4UeB','FRLwrwQnK7','cIvf72h28P','test_smtp_2','ts','2024-03-14 12:57:04','','14-03-2024','14-03-2024',2,0,'',1,'',0,'',''),(44,'UBmh2QGWAd','N7Uo5Mw21H','cIvf72h28P','test','test','2024-03-14 12:58:01','','14-03-2024','14-03-2024',1,0,'',0,'',0,'',''),(45,'9oVbtewTZD','N7Uo5Mw21H','cIvf72h28P','test_smtp2','test','2024-03-14 13:00:19','','14-03-2024','14-03-2024',1,0,'',0,'',0,'',''),(46,'4Mq4SMZ0QV','1P4sG4L0Ri','cIvf72h28P','test_smtp3','test','2024-03-14 13:01:23','','14-03-2024','14-03-2024',1,0,'',0,'',0,'',''),(47,'4Mq4SMZ0QV','zZIcL4CFxz','cIvf72h28P','test_smtp3','test','2024-03-14 13:01:23','','14-03-2024','14-03-2024',1,0,'',0,'',0,'',''),(48,'4Mq4SMZ0QV','kjwbYtevPB','cIvf72h28P','test_smtp3','test','2024-03-14 13:01:23','','14-03-2024','14-03-2024',1,0,'',0,'',0,'',''),(49,'4QLN5rA8ni','zZIcL4CFxz','cIvf72h28P','test_smtp4','test','2024-03-14 13:03:00','','14-03-2024','14-03-2024',1,0,'',0,'',0,'',''),(50,'4QLN5rA8ni','nrRET67GRU','cIvf72h28P','test_smtp4','test','2024-03-14 13:03:00','','14-03-2024','14-03-2024',1,0,'',0,'',0,'',''),(51,'4QLN5rA8ni','8UjO3t3UmJ','cIvf72h28P','test_smtp4','test','2024-03-14 13:03:00','','14-03-2024','14-03-2024',1,0,'',0,'',0,'',''),(52,'sYKkJdzZif','1P4sG4L0Ri','cIvf72h28P','test_smtp5','test','2024-03-14 13:04:29','','14-03-2024','14-03-2024',1,0,'',0,'',0,'',''),(53,'sYKkJdzZif','zZIcL4CFxz','cIvf72h28P','test_smtp5','test','2024-03-14 13:04:29','','14-03-2024','14-03-2024',1,0,'',0,'',0,'',''),(54,'sYKkJdzZif','8UjO3t3UmJ','cIvf72h28P','test_smtp5','test','2024-03-14 13:04:29','','14-03-2024','14-03-2024',1,0,'',0,'',0,'',''),(55,'BQW79LkZz7','N7Uo5Mw21H','cIvf72h28P','test_smtp6','test','2024-03-14 13:06:27','','14-03-2024','14-03-2024',1,0,'',1,'',0,'',''),(56,'BQW79LkZz7','zZIcL4CFxz','cIvf72h28P','test_smtp6','test','2024-03-14 13:06:27','','14-03-2024','14-03-2024',1,0,'',1,'',0,'',''),(57,'BQW79LkZz7','1P4sG4L0Ri','cIvf72h28P','test_smtp6','test','2024-03-14 13:06:27','','14-03-2024','14-03-2024',2,0,'',1,'',0,'',''),(58,'BQW79LkZz7','8UjO3t3UmJ','cIvf72h28P','test_smtp6','test','2024-03-14 13:06:27','','14-03-2024','14-03-2024',1,0,'',1,'',0,'',''),(59,'ASds1Lntk2','N7Uo5Mw21H','cIvf72h28P','test_smtp7','test','2024-03-14 13:07:54','','14-03-2024','14-03-2024',2,0,'',0,'',0,'',''),(60,'ASds1Lntk2','WnNnzVHtWL','cIvf72h28P','test_smtp7','test','2024-03-14 13:07:54','','14-03-2024','14-03-2024',1,0,'',0,'',0,'',''),(61,'uBvfAaveqm','zZIcL4CFxz','cIvf72h28P','test_smtp8','test','2024-03-14 13:19:05','2024-03-14 13:38:12','2024-03-14 13:38:08','2024-03-14 13:38:12',2,1,'',1,'',3,'','<EMAIL>'),(62,'uBvfAaveqm','1P4sG4L0Ri','cIvf72h28P','test_smtp8','test','2024-03-14 13:19:05','2024-03-14 13:38:12','2024-03-14 13:38:08','2024-03-14 13:38:12',1,1,'',1,'',3,'','<EMAIL>'),(63,'uBvfAaveqm','nrRET67GRU','cIvf72h28P','test_smtp8','test','2024-03-14 13:19:05','2024-03-14 13:38:12','2024-03-14 13:38:08','2024-03-14 13:38:12',2,1,'',1,'',3,'','<EMAIL>'),(64,'uBvfAaveqm','kjwbYtevPB','cIvf72h28P','test_smtp8','test','2024-03-14 13:19:05','2024-03-14 13:38:12','2024-03-14 13:38:08','2024-03-14 13:38:12',1,1,'',1,'',3,'','<EMAIL>'),(65,'DtxXBuunf2','zZIcL4CFxz','cIvf72h28P','test_smtp9','test','2024-03-14 13:34:20','2024-03-14 13:37:30','2024-03-14 13:37:27','2024-03-14 13:37:30',2,1,'',0,'',3,'','<EMAIL>'),(66,'DtxXBuunf2','1P4sG4L0Ri','cIvf72h28P','test_smtp9','test','2024-03-14 13:34:20','2024-03-14 13:37:30','2024-03-14 13:37:27','2024-03-14 13:37:30',1,1,'',0,'',3,'','<EMAIL>'),(67,'DtxXBuunf2','nrRET67GRU','cIvf72h28P','test_smtp9','test','2024-03-14 13:34:20','2024-03-14 13:37:30','2024-03-14 13:37:27','2024-03-14 13:37:30',1,1,'',0,'',3,'','<EMAIL>'),(68,'DtxXBuunf2','kjwbYtevPB','cIvf72h28P','test_smtp9','test','2024-03-14 13:34:20','2024-03-14 13:37:30','2024-03-14 13:37:27','2024-03-14 13:37:30',1,1,'',0,'',3,'','<EMAIL>'),(69,'DtxXBuunf2','WnNnzVHtWL','cIvf72h28P','test_smtp9','test','2024-03-14 13:34:20','2024-03-14 13:37:30','2024-03-14 13:37:27','2024-03-14 13:37:30',1,1,'',0,'',3,'','<EMAIL>'),(70,'KJhPvgJDQg','N7Uo5Mw21H','cIvf72h28P','test','test','2024-03-14 13:40:01','','14-03-2024','14-03-2024',2,0,'',0,'',0,'',''),(71,'KJhPvgJDQg','FRLwrwQnK7','cIvf72h28P','test','test','2024-03-14 13:40:01','','14-03-2024','14-03-2024',1,0,'',0,'',0,'',''),(72,'KJhPvgJDQg','kjwbYtevPB','cIvf72h28P','test','test','2024-03-14 13:40:01','','14-03-2024','14-03-2024',1,0,'',0,'',0,'',''),(73,'KJhPvgJDQg','YOksPCHiWp','cIvf72h28P','test','test','2024-03-14 13:40:01','','14-03-2024','14-03-2024',1,0,'',0,'',0,'',''),(74,'X6ChPTvQu2','GkOOHk4pRz','cIvf72h28P','test','test','2024-03-14 17:10:15','2024-03-14 17:11:07','2024-03-14 17:11:03','2024-03-14 17:11:07',1,1,'',1,'',3,'','<EMAIL>'),(75,'X6ChPTvQu2','8UjO3t3UmJ','cIvf72h28P','test','test','2024-03-14 17:10:15','2024-03-14 17:11:07','2024-03-14 17:11:03','2024-03-14 17:11:07',1,1,'',1,'',3,'','<EMAIL>'),(76,'X6ChPTvQu2','kjwbYtevPB','cIvf72h28P','test','test','2024-03-14 17:10:15','2024-03-14 17:11:07','2024-03-14 17:11:03','2024-03-14 17:11:07',1,1,'',1,'',3,'','<EMAIL>'),(77,'R2eCYJQiYp','N7Uo5Mw21H','cIvf72h28P','test2','test','2024-03-14 17:12:07','','14-03-2024','14-03-2024',1,0,'',0,'',0,'',''),(78,'R2eCYJQiYp','FRLwrwQnK7','cIvf72h28P','test2','test','2024-03-14 17:12:07','','14-03-2024','14-03-2024',1,0,'',0,'',0,'',''),(79,'R2eCYJQiYp','FRLwrwQnK7','cIvf72h28P','test2','test','2024-03-14 17:12:07','','14-03-2024','14-03-2024',1,0,'',0,'',0,'',''),(80,'R2eCYJQiYp','zZIcL4CFxz','cIvf72h28P','test2','test','2024-03-14 17:12:07','','14-03-2024','14-03-2024',1,0,'',0,'',0,'',''),(81,'R2eCYJQiYp','1P4sG4L0Ri','cIvf72h28P','test2','test','2024-03-14 17:12:07','','14-03-2024','14-03-2024',1,0,'',0,'',0,'',''),(82,'R2eCYJQiYp','nrRET67GRU','cIvf72h28P','test2','test','2024-03-14 17:12:07','','14-03-2024','14-03-2024',1,0,'',0,'',0,'',''),(83,'IwkPBMW5Mm','WnNnzVHtWL','cIvf72h28P','test','test','2024-03-14 17:13:24','','14-03-2024','14-03-2024',1,0,'',0,'',0,'','');
/*!40000 ALTER TABLE `request-list` ENABLE KEYS */;
UNLOCK TABLES;
COMMIT;

-- Dumped table `request-list` with 45 row(s)
--

--
-- Table structure for table `role-list`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `role-list` (
  `id` int(255) NOT NULL AUTO_INCREMENT,
  `role-id` varchar(255) NOT NULL,
  `role` varchar(255) NOT NULL,
  `role_description` varchar(255) NOT NULL,
  `inventory_view` int(255) NOT NULL,
  `inventory_create` int(255) NOT NULL,
  `inventory_edit` int(255) NOT NULL,
  `inventory_delete` int(255) NOT NULL,
  `inventory_all` int(255) NOT NULL,
  `import_create` int(255) NOT NULL,
  `category_view` int(255) NOT NULL,
  `category_create` int(255) NOT NULL,
  `category_edit` int(255) NOT NULL,
  `category_delete` int(255) NOT NULL,
  `category_all` int(255) NOT NULL,
  `brand_view` int(255) NOT NULL,
  `brand_create` int(255) NOT NULL,
  `brand_edit` int(255) NOT NULL,
  `brand_delete` int(255) NOT NULL,
  `brand_all` int(255) NOT NULL,
  `location_view` int(255) NOT NULL,
  `location_create` int(255) NOT NULL,
  `location_edit` int(255) NOT NULL,
  `location_delete` int(255) NOT NULL,
  `location_all` int(255) NOT NULL,
  `request_view` int(255) NOT NULL,
  `request_create` int(255) NOT NULL,
  `request_approval` int(255) NOT NULL,
  `request_all` int(255) NOT NULL,
  `inventory_return_view` int(255) NOT NULL,
  `inventory_return_edit` int(255) NOT NULL,
  `inventory_return_delete` int(255) NOT NULL,
  `inventory_return_all` int(255) NOT NULL,
  `user_view` int(255) NOT NULL,
  `user_create` int(255) NOT NULL,
  `user_edit` int(255) NOT NULL,
  `user_ban` int(255) NOT NULL,
  `user_delete` int(255) NOT NULL,
  `user_all` int(255) NOT NULL,
  `group_view` int(255) NOT NULL,
  `group_create` int(255) NOT NULL,
  `group_edit` int(255) NOT NULL,
  `group_delete` int(255) NOT NULL,
  `group_all` int(255) NOT NULL,
  `hide` int(255) NOT NULL,
  `request_smtp` int(11) NOT NULL,
  `backup` int(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `role-list`
--

LOCK TABLES `role-list` WRITE;
/*!40000 ALTER TABLE `role-list` DISABLE KEYS */;
SET autocommit=0;
INSERT INTO `role-list` VALUES (1,'x9Mj5GmOGz','Admin','Master Admin',0,0,0,0,1,1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1,1,1,1,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,1,0,0,0),(2,'UzZhqYA3OJ','User','Normal User',1,0,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1,1,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0),(3,'0IH26m4Tiv','Procurement','Procurement will receive an email notification when there is a request from an end user.',0,0,0,0,1,1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1,0,0,0,1,0,0,0,1,0,0,0,0,0,1,0,0,0,0,1,0,0,0),(4,'LvIDnzcptn','test','test',0,0,0,0,1,1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1,0,0,0,1,0,0,0,1,0,0,0,0,0,1,0,0,0,0,1,0,0,1);
/*!40000 ALTER TABLE `role-list` ENABLE KEYS */;
UNLOCK TABLES;
COMMIT;

-- Dumped table `role-list` with 4 row(s)
--

--
-- Table structure for table `user-list`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `user-list` (
  `id` int(255) NOT NULL AUTO_INCREMENT,
  `acc_id` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `designation` varchar(255) NOT NULL,
  `department` varchar(255) NOT NULL,
  `description` varchar(999) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user-list`
--

LOCK TABLES `user-list` WRITE;
/*!40000 ALTER TABLE `user-list` DISABLE KEYS */;
SET autocommit=0;
INSERT INTO `user-list` VALUES (1,'cIvf72h28P','<EMAIL>','Alex Ang','Software Engineer','Software Team',''),(2,'LFuUInMStl','<EMAIL>','Firdaus Baharin','Service Delivery Manager','Technical Department',''),(3,'snyajRZeBK','<EMAIL>','Alan Ang','Lead System Engineer','Technical Department',''),(4,'ed0OIAZ8ne','<EMAIL>','Jason Lim','Director, Technical','Technical Department',''),(5,'lOoY8fXCUX','<EMAIL>','KK Chong','Software Sales Director','Software Team',''),(6,'ylmh96zgPV','<EMAIL>',' Mudzaffar Haniffuddin','Senior Procurement Executive','Finance Department',''),(7,'5LKL0eS7x3','<EMAIL>','Ang Teck Yan','Software Engineer','Software Team',''),(8,'aF2ORY3PCT','<EMAIL>','Ernest Wong','Software Engineer','Software Team','');
/*!40000 ALTER TABLE `user-list` ENABLE KEYS */;
UNLOCK TABLES;
COMMIT;

-- Dumped table `user-list` with 8 row(s)
--

--
-- Table structure for table `useracc`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `useracc` (
  `id` int(255) NOT NULL AUTO_INCREMENT,
  `email` varchar(255) NOT NULL,
  `acc_id` varchar(255) NOT NULL,
  `token` varchar(255) NOT NULL,
  `otp` varchar(255) NOT NULL,
  `expired` varchar(255) NOT NULL,
  `ban` int(255) NOT NULL,
  `attempt` int(255) NOT NULL,
  `role_id` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `useracc`
--

LOCK TABLES `useracc` WRITE;
/*!40000 ALTER TABLE `useracc` DISABLE KEYS */;
SET autocommit=0;
INSERT INTO `useracc` VALUES (1,'<EMAIL>','cIvf72h28P','','','',0,0,'LvIDnzcptn'),(2,'<EMAIL>','LFuUInMStl','','','',0,0,'x9Mj5GmOGz'),(3,'<EMAIL>','snyajRZeBK','','','',0,0,'x9Mj5GmOGz'),(4,'<EMAIL>','ed0OIAZ8ne','','','',0,0,'x9Mj5GmOGz'),(5,'<EMAIL>','lOoY8fXCUX','','','',0,0,'x9Mj5GmOGz'),(6,'<EMAIL>','ylmh96zgPV','','','',0,0,'x9Mj5GmOGz'),(7,'<EMAIL>','5LKL0eS7x3','','','',0,0,'UzZhqYA3OJ'),(8,'<EMAIL>','aF2ORY3PCT','','','',1,0,'UzZhqYA3OJ');
/*!40000 ALTER TABLE `useracc` ENABLE KEYS */;
UNLOCK TABLES;
COMMIT;

-- Dumped table `useracc` with 8 row(s)
--

/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;
/*!40101 SET AUTOCOMMIT=@OLD_AUTOCOMMIT */;
/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on: Tue, 26 Mar 2024 17:09:33 +0800
