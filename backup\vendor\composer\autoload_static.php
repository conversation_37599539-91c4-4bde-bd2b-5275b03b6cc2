<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit89d5c39d972557dc137af23558d8d0c1
{
    public static $prefixLengthsPsr4 = array (
        'I' => 
        array (
            'Ifsnop\\' => 7,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'Ifsnop\\' => 
        array (
            0 => __DIR__ . '/..' . '/ifsnop/mysqldump-php/src/Ifsnop',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit89d5c39d972557dc137af23558d8d0c1::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit89d5c39d972557dc137af23558d8d0c1::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit89d5c39d972557dc137af23558d8d0c1::$classMap;

        }, null, ClassLoader::class);
    }
}
