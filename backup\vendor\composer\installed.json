{"packages": [{"name": "ifsnop/mysqldump-php", "version": "v2.12", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/ifsnop/mysqldump-php.git", "reference": "2d3a43fc0c49f23bf7dee392b0dd1f8c799f89d3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ifsnop/mysqldump-php/zipball/2d3a43fc0c49f23bf7dee392b0dd1f8c799f89d3", "reference": "2d3a43fc0c49f23bf7dee392b0dd1f8c799f89d3", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "4.8.36", "squizlabs/php_codesniffer": "1.*"}, "time": "2023-04-12T07:43:14+00:00", "type": "library", "installation-source": "source", "autoload": {"psr-4": {"Ifsnop\\": "src/Ifsnop/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-3.0-or-later"], "authors": [{"name": "<PERSON>", "homepage": "https://github.com/ifsnop", "role": "Developer"}], "description": "PHP version of mysqldump cli that comes with MySQL", "homepage": "https://github.com/ifsnop/mysqldump-php", "keywords": ["PHP7", "database", "hhvm", "ma<PERSON>b", "mysql", "mysql-backup", "mysqldump", "pdo", "php", "php5", "sql"], "support": {"issues": "https://github.com/ifsnop/mysqldump-php/issues", "source": "https://github.com/ifsnop/mysqldump-php/tree/v2.12"}, "install-path": "../ifsnop/mysqldump-php"}], "dev": true, "dev-package-names": []}