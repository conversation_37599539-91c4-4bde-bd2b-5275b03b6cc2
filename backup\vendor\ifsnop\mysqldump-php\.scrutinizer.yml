build:
    nodes:
        tests: true
        analysis:
            tests:
                override:
                    -
                        command: phpcs-run
                        use_website_config: true
                    - php-scrutinizer-run
    dependencies:
        before:
            - 'composer install --dev --prefer-source'
    # Run after dependencies
    project_setup:
        before:
            - 'tests/create_users.sh'
    environment:
        php:
            version: 5.4, 5.5, 5.6, 7.0, 7.1, 7.2, hhvm, nightly
tools:
    php_loc:
        enabled: true
        command: phploc
        excluded_dirs:
            - vendor
            - tests
    sensiolabs_security_checker: true
filter:
    excluded_paths:
        - 'vendor/'
        - 'tests/'
coding_style:
    php: {  }
imports:
    - php
checks:
    php:
        verify_property_names: true
        verify_argument_usable_as_reference: true
        verify_access_scope_valid: true
        variable_existence: true
        useless_calls: true
        use_statement_alias_conflict: true
        unused_variables: true
        unused_properties: true
        unused_parameters: true
        unused_methods: true
        unreachable_code: true
        too_many_arguments: true
        symfony_request_injection: true
        switch_fallthrough_commented: true
        sql_injection_vulnerabilities: true
        security_vulnerabilities: true
        return_in_constructor: true
        require_scope_for_methods: true
        require_php_tag_first: true
        property_assignments: true
        precedence_mistakes: true
        precedence_in_conditions: true
        parse_doc_comments: true
        parameter_non_unique: true
        overriding_private_members: true
        overriding_parameter: true
        non_commented_empty_catch_block: true
        no_trait_type_hints: true
        no_trailing_whitespace: true
        no_short_open_tag: true
        no_property_on_interface: true
        no_non_implemented_abstract_methods: true
        no_exit: true
        no_eval: true
        no_error_suppression: true
        no_debug_code: true
        missing_arguments: true
        method_calls_on_non_object: true
        instanceof_class_exists: true
        foreach_usable_as_reference: true
        foreach_traversable: true
        fix_doc_comments: true
        encourage_shallow_comparison: true
        duplication: true
        deprecated_code_usage: true
        deadlock_detection_in_loops: true
        comparison_always_same_result: true
        code_rating: true
        closure_use_not_conflicting: true
        closure_use_modifiable: true
        catch_class_exists: true
        call_to_parent_method: true
        avoid_superglobals: true
        avoid_length_functions_in_loops: true
        avoid_entity_manager_injection: true
        avoid_duplicate_types: true
        avoid_closing_tag: true
        assignment_of_null_return: true
        argument_type_checks: true
        simplify_boolean_return: true
        return_doc_comments: true
        return_doc_comment_if_not_inferrable: true
        remove_extra_empty_lines: true
        properties_in_camelcaps: true
        parameters_in_camelcaps: true
        parameter_doc_comments: true
        param_doc_comment_if_not_inferrable: true
        no_short_variable_names:
            minimum: '3'
        no_short_method_names:
            minimum: '3'
        no_long_variable_names:
            maximum: '20'
        no_goto: true
        naming_conventions:
            local_variable: '^[a-z][a-zA-Z0-9]*$'
            abstract_class_name: ^Abstract|Factory$
            utility_class_name: 'Utils?$'
            constant_name: '^[A-Z][A-Z0-9]*(?:_[A-Z0-9]+)*$'
            property_name: '^[a-z][a-zA-Z0-9]*$'
            method_name: '^(?:[a-z]|__)[a-zA-Z0-9]*$'
            parameter_name: '^[a-z][a-zA-Z0-9]*$'
            interface_name: '^[A-Z][a-zA-Z0-9]*Interface$'
            type_name: '^[A-Z][a-zA-Z0-9]*$'
            exception_name: '^[A-Z][a-zA-Z0-9]*Exception$'
            isser_method_name: '^(?:is|has|should|may|supports)'
        more_specific_types_in_doc_comments: true
        fix_use_statements:
            remove_unused: true
            preserve_multiple: false
            preserve_blanklines: false
            order_alphabetically: false
        fix_line_ending: true
        check_method_contracts:
            verify_interface_like_constraints: true
            verify_documented_constraints: true
            verify_parent_constraints: true
