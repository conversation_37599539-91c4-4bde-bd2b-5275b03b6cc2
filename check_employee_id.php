<?php
require('database.php');

// Check if user is logged in
if (!isset($_SESSION['user'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

// Get the employee ID from POST request
$employee_id = $_POST['employee_id'] ?? '';

if (empty($employee_id)) {
    echo json_encode(['exists' => false]);
    exit;
}

// Check if employee ID exists in the database
$stmt = mysqli_prepare($conn, "SELECT employee_id FROM `onboarding_list` WHERE `employee_id` = ?");
mysqli_stmt_bind_param($stmt, "s", $employee_id);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

$exists = mysqli_num_rows($result) > 0;

mysqli_stmt_close($stmt);

// Return JSON response
header('Content-Type: application/json');
echo json_encode(['exists' => $exists]);
?>
