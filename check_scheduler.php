<?php
/**
 * Scheduler Status Checker
 * Check the current status of the email scheduler
 */

// Set timezone
date_default_timezone_set('Asia/Kuala_Lumpur');

function log_message($message) {
    $timestamp = date('Y-m-d H:i:s');
    echo "[$timestamp] $message" . PHP_EOL;
}

function get_file_age($filepath) {
    if (!file_exists($filepath)) return 'N/A';
    $age_seconds = time() - filemtime($filepath);
    if ($age_seconds < 60) return $age_seconds . ' seconds ago';
    if ($age_seconds < 3600) return floor($age_seconds / 60) . ' minutes ago';
    if ($age_seconds < 86400) return floor($age_seconds / 3600) . ' hours ago';
    return floor($age_seconds / 86400) . ' days ago';
}

function is_scheduler_running() {
    if (!file_exists(__DIR__ . '/scheduler.pid')) {
        return false;
    }

    $pid = trim(file_get_contents(__DIR__ . '/scheduler.pid'));

    // On Windows, check if process exists
    if (PHP_OS_FAMILY === 'Windows') {
        $output = shell_exec("tasklist /FI \"PID eq $pid\" 2>NUL");
        return strpos($output, $pid) !== false;
    } else {
        // On Unix-like systems
        return file_exists("/proc/$pid");
    }
}

echo "=== Email Scheduler Status Check ===" . PHP_EOL;
echo "Current Time: " . date('Y-m-d H:i:s') . " (Malaysia Time)" . PHP_EOL . PHP_EOL;

// Check scheduler status
log_message("=== Scheduler Status ===");
if (is_scheduler_running()) {
    $pid = trim(file_get_contents(__DIR__ . '/scheduler.pid'));
    log_message("✓ Email Scheduler is RUNNING (PID: $pid)");

    // Check PID file age
    $pid_age = get_file_age(__DIR__ . '/scheduler.pid');
    log_message("✓ Started: $pid_age");
} else {
    log_message("✗ Email Scheduler is NOT RUNNING");
    if (file_exists(__DIR__ . '/scheduler.pid')) {
        log_message("⚠ Stale PID file exists - scheduler may have crashed");
    }
}

// Check stop signal
if (file_exists(__DIR__ . '/stop_scheduler.txt')) {
    log_message("⚠ Stop signal file exists - scheduler should be stopping");
}

// Check database connection
log_message("\n=== Database Connection ===");
try {
    // Prevent session start in database.php for status checker
    if (!defined('NO_SESSION')) define('NO_SESSION', true);
    require_once __DIR__ . '/database.php';
    if ($conn) {
        log_message("✓ Database connection successful");

        // Check onboarding config
        $config_query = "SELECT * FROM `onboarding_cron_config` WHERE `config_type` = 'cron_reminder'";
        $config_result = mysqli_query($conn, $config_query);
        $config = mysqli_fetch_assoc($config_result);

        if ($config) {
            $status = $config['is_enabled'] ? 'Enabled' : 'Disabled';
            log_message("✓ Onboarding Config: $status, Time: " . $config['cron_time']);
        } else {
            log_message("✗ No onboarding configuration found");
        }

        // Check IT support config
        $it_query = "SELECT * FROM `onboarding_email_config` WHERE `config_type` = 'it_support'";
        $it_result = mysqli_query($conn, $it_query);
        $it_config = mysqli_fetch_assoc($it_result);

        if ($it_config && !empty($it_config['cron_time'])) {
            log_message("✓ IT Support Config: Time: " . substr($it_config['cron_time'], 0, 5));
        } else {
            log_message("✗ IT Support time not configured");
        }

    } else {
        log_message("✗ Database connection failed");
    }
} catch (Exception $e) {
    log_message("✗ Database error: " . $e->getMessage());
}

// Check log files
log_message("\n=== Log Files Status ===");

$log_files = [
    'email_scheduler.log' => 'Scheduler Log',
    'auto_runner.log' => 'Email Runner Log',
    'scheduler_output.log' => 'Scheduler Output'
];

foreach ($log_files as $filename => $description) {
    $filepath = __DIR__ . '/logs/' . $filename;
    if (file_exists($filepath)) {
        $size = filesize($filepath);
        $age = get_file_age($filepath);
        log_message("✓ $description: " . number_format($size) . " bytes, updated $age");
    } else {
        log_message("✗ $description: Not found");
    }
}

// Check for lock files
log_message("\n=== Daily Lock Files ===");
$today = date('Y-m-d');
$lock_files = [
    "onboarding_reminder_$today.lock" => 'Onboarding Reminder',
    "it_support_notification_$today.lock" => 'IT Support Notification'
];

foreach ($lock_files as $filename => $description) {
    $filepath = __DIR__ . '/logs/' . $filename;
    if (file_exists($filepath)) {
        $content = trim(file_get_contents($filepath));
        log_message("✓ $description: Already sent today ($content)");
    } else {
        log_message("○ $description: Not sent yet today");
    }
}

// Show recent scheduler activity
log_message("\n=== Recent Scheduler Activity ===");
$scheduler_log = __DIR__ . '/logs/email_scheduler.log';
if (file_exists($scheduler_log)) {
    $lines = file($scheduler_log);
    $recent_lines = array_slice($lines, -10); // Last 10 lines
    foreach ($recent_lines as $line) {
        echo trim($line) . PHP_EOL;
    }
} else {
    log_message("No scheduler log found");
}

echo PHP_EOL . "=== Status Check Complete ===" . PHP_EOL;
?>