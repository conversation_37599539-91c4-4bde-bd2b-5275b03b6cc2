-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Oct 28, 2024 at 08:37 AM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `cll-inventory-staging`
--

-- --------------------------------------------------------

--
-- Table structure for table `anonymous_inquiry_list`
--

CREATE TABLE `anonymous_inquiry_list` (
  `id` int(255) NOT NULL,
  `inq_id` varchar(255) NOT NULL,
  `inquiry` text NOT NULL,
  `remarks` text NOT NULL,
  `image` varchar(255) DEFAULT NULL,
  `acc_id` varchar(255) NOT NULL,
  `token` varchar(255) NOT NULL,
  `date_submitted` datetime DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `anonymous_inquiry_list`
--

INSERT INTO `anonymous_inquiry_list` (`id`, `inq_id`, `inquiry`, `remarks`, `image`, `acc_id`, `token`, `date_submitted`) VALUES
(96, 'eVNiBOa3kI', 'Text', '', NULL, '771a1c26b3', '771a1c26b3', '2024-07-09 01:44:18'),
(97, 'wPejCWl1YE', 'Chong needs love', 'ALOT ALOT OF LOVE', 'uploads/Screenshot 2024-06-24 162552.png', '3f11b3d5b7', '3f11b3d5b7', '2024-07-09 03:22:49'),
(98, 'MiuJ09zHAb', 'Tea to share', 'Syamie and bryant fighting ', 'uploads/download (2).jpg', '3f11b3d5b7', '3f11b3d5b7', '2024-07-09 03:28:14'),
(99, 'YisehBwydR', 'hi i want to test', '', NULL, 'de1f7516b0', 'de1f7516b0', '2024-07-09 21:38:53'),
(100, '8wBmG6uN7g', 'Testing', '', NULL, '771a1c26b3', '771a1c26b3', '2024-07-18 00:49:40');

-- --------------------------------------------------------

--
-- Table structure for table `assets_faulty_list`
--

CREATE TABLE `assets_faulty_list` (
  `id` int(255) NOT NULL,
  `inq_id` varchar(255) NOT NULL,
  `office_id` varchar(255) NOT NULL,
  `office_area_id` varchar(255) NOT NULL,
  `office_category_id` varchar(255) NOT NULL,
  `fault_category_id` varchar(255) NOT NULL,
  `remarks` text NOT NULL,
  `image` varchar(255) DEFAULT NULL,
  `acc_id` varchar(255) NOT NULL,
  `date_submitted` datetime DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `assets_faulty_list`
--

INSERT INTO `assets_faulty_list` (`id`, `inq_id`, `office_id`, `office_area_id`, `office_category_id`, `fault_category_id`, `remarks`, `image`, `acc_id`, `date_submitted`) VALUES
(122, 'o4EaAw997J', 'OFF01', '2.5b5-9506', 'L6-L353536', '57ed-.61cd', 'Testing', NULL, 'Wwc27y3ybR', '2024-07-09 01:38:24'),
(123, 'XMGpIRwu02', 'OFF01', '2.5b5-9506', 'L6-L353536', '57ed-.61cd', 'Testing', NULL, 'Wwc27y3ybR', '2024-07-09 01:40:38'),
(124, 'rFU1Kng4lL', 'OFF01', 'LcC25754-7', '6cdca508La', '822L9607-4', 'Downlight burned out', 'uploads/IMG_20240709_172842.jpg', 'snyajRZeBK', '2024-07-09 02:30:31'),
(125, 'd07ewGS0Up', 'OFF01', '696.C325L7', '7327454bc6', '.6d56427LC', 'MONITORS BEHIND IS SHIT, keep blinking and disconnect', NULL, 'GMqrpu1kC5', '2024-07-09 03:32:17'),
(126, 'RpUEst7XSC', 'OFF01', '2.5b5-9506', 'L6-L353536', '57ed-.61cd', '', NULL, 'Wwc27y3ybR', '2024-07-30 01:26:33'),
(127, '6yRTN7sdNr', 'OFF01', '2.5b5-9506', 'L6-L353536', '57ed-.61cd', '', NULL, 'Wwc27y3ybR', '2024-07-30 01:31:28'),
(128, 'yVQ2AhldU3', 'OFF01', 'LcC25754-7', '6cdca508La', '822L9607-4', '2 units of light bulb have burned out.\r\nNeed to replace new one.', 'uploads/66b5de9c15a42.jpeg,uploads/66b5de9c15cdc.jpeg', 'ylmh96zgPV', '2024-08-09 02:17:16');

-- --------------------------------------------------------

--
-- Table structure for table `brand-list`
--

CREATE TABLE `brand-list` (
  `id` int(255) NOT NULL,
  `brand-name` varchar(255) NOT NULL,
  `brand-id` varchar(255) NOT NULL,
  `des` varchar(999) NOT NULL,
  `created` varchar(255) NOT NULL,
  `hide` int(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `brand-list`
--

INSERT INTO `brand-list` (`id`, `brand-name`, `brand-id`, `des`, `created`, `hide`) VALUES
(1, 'Cisco', '**********', 'CSV import added this Brand ID', 'Mudzaffar Haniffuddin', 0),
(2, 'Maipu', '3107439717', 'CSV import added this Brand ID', 'Mudzaffar Haniffuddin', 0),
(3, 'Casio', '9223873625', 'CSV import added this Brand ID', 'Mudzaffar Haniffuddin', 0),
(4, 'Zoguo', '4268328558', 'CSV import added this Brand ID', 'Mudzaffar Haniffuddin', 0),
(5, 'Anet', '4174530348', 'CSV import added this Brand ID', 'Mudzaffar Haniffuddin', 0),
(6, 'OM3', '1634112121', 'CSV import added this Brand ID', 'Mudzaffar Haniffuddin', 0),
(7, 'N/A', '2070235736', 'CSV import added this Brand ID', 'Mudzaffar Haniffuddin', 0),
(8, 'SUBTel', '0742870078', 'CSV import added this Brand ID', 'Mudzaffar Haniffuddin', 0),
(9, 'Fortinet', '0244180542', 'CSV import added this Brand ID', 'Mudzaffar Haniffuddin', 0),
(10, 'Finisar', '3501829193', 'CSV import added this Brand ID', 'Mudzaffar Haniffuddin', 0),
(11, 'Dell', '5021987615', 'CSV import added this Brand ID', 'Mudzaffar Haniffuddin', 0),
(12, 'Avago', '8131462793', 'CSV import added this Brand ID', 'Mudzaffar Haniffuddin', 0),
(13, 'HPE', '**********', 'CSV import added this Brand ID', 'Mudzaffar Haniffuddin', 0),
(14, 'JT-COM', '5e924-6L44', '', 'Mudzaffar Haniffuddin', 0),
(17, 'Huawei', '**********', 'CSV import added this Brand ID', ' Mudzaffar Haniffuddin', 0),
(18, 'ATEN', 'Ld55e6-1L0', '', ' Mudzaffar Haniffuddin', 0),
(19, 'UMS', '177042LLc0', '', ' Mudzaffar Haniffuddin', 0),
(20, 'Palo Alto', '0.70f2f-66', '', ' Mudzaffar Haniffuddin', 0),
(21, 'Sangfor', 'L08969.913', '', ' Mudzaffar Haniffuddin', 0),
(22, 'Nutanix', '28620Lc973', '', ' Mudzaffar Haniffuddin', 0),
(23, 'H3C', 'L128.5CL2c', '', ' Mudzaffar Haniffuddin', 0);

-- --------------------------------------------------------

--
-- Table structure for table `category-list`
--

CREATE TABLE `category-list` (
  `id` int(255) NOT NULL,
  `category-id` varchar(255) NOT NULL,
  `category-name` varchar(255) NOT NULL,
  `des` varchar(999) NOT NULL,
  `created` varchar(255) NOT NULL,
  `hide` int(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `category-list`
--

INSERT INTO `category-list` (`id`, `category-id`, `category-name`, `des`, `created`, `hide`) VALUES
(1, '7577505059', 'Infrastructure', '', 'Mudzaffar Haniffuddin', 0),
(2, '**********', 'Cable', '', 'Mudzaffar Haniffuddin', 0),
(3, '**********', 'Server', '', 'Mudzaffar Haniffuddin', 0),
(4, '3431257563', 'Power Supply', '', 'Mudzaffar Haniffuddin', 0),
(5, '01', 'Switch', '', 'Mudzaffar Haniffuddin', 0),
(6, '06', 'Transceiver', '', 'Mudzaffar Haniffuddin', 0),
(8, '07', 'Monitor', '', ' Mudzaffar Haniffuddin', 0),
(9, '08', 'AP (Access Point)', '', ' Mudzaffar Haniffuddin', 0),
(10, '09', 'Socket', '', ' Mudzaffar Haniffuddin', 0),
(11, '10', 'Firewall', '', ' Mudzaffar Haniffuddin', 0),
(12, '11', 'HCI', '', ' Mudzaffar Haniffuddin', 0),
(13, '12', 'Adapter', '', ' Mudzaffar Haniffuddin', 0);

-- --------------------------------------------------------

--
-- Table structure for table `fault_category_list`
--

CREATE TABLE `fault_category_list` (
  `id` int(11) NOT NULL,
  `office_id` varchar(50) NOT NULL,
  `fault_category_id` varchar(50) NOT NULL,
  `fault_category` varchar(255) NOT NULL,
  `des` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `fault_category_list`
--

INSERT INTO `fault_category_list` (`id`, `office_id`, `fault_category_id`, `fault_category`, `des`) VALUES
(1, 'OFF01', '57ed-.61cd', 'Table', ''),
(2, 'OFF01', '7f5743L796', 'Chair', ''),
(3, 'OFF01', 'd6d0.c64-4', 'Toilet Bowl', ''),
(4, 'OFF01', '7L14.19a75', 'Power Extension', ''),
(5, 'OFF01', 'L.C6edd683', 'Power Socket', ''),
(6, 'OFF01', '.32235L86f', 'Water Purifier', ''),
(7, 'OFF01', '.6d56427LC', 'TV', ''),
(8, 'OFF01', '822L9607-4', 'Light Bulb', ''),
(9, 'OFF01', 'C6.6662605', 'Air Conditioner', ''),
(10, 'OFF01', '166da63C56', 'Coffee Machine', ''),
(11, 'OFF01', '4610491L62', 'Fridge', ''),
(12, 'OFF01', '-c2621Lc6e', 'Door Access', ''),
(13, 'OFF01', '5-7936.816', 'Ceiling Door', ''),
(14, 'OFF01', '83CL0669b0', 'Window', ''),
(15, 'OFF01', '6d54236LC3', 'Wall', ''),
(16, 'OFF01', 'C6977891.6', 'Other', '');

-- --------------------------------------------------------

--
-- Table structure for table `financeinq_list`
--

CREATE TABLE `financeinq_list` (
  `id` int(11) NOT NULL,
  `finance_inq_id` varchar(50) NOT NULL,
  `finance_inq` varchar(255) NOT NULL,
  `des` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `financeinq_list`
--

INSERT INTO `financeinq_list` (`id`, `finance_inq_id`, `finance_inq`, `des`) VALUES
(1, 'FNC01', 'Payroll Inquiry', ''),
(2, 'FNC02', 'Claim Inquiry', '');

-- --------------------------------------------------------

--
-- Table structure for table `finance_list`
--

CREATE TABLE `finance_list` (
  `id` int(255) NOT NULL,
  `inq_id` varchar(255) NOT NULL,
  `finance_inq_id` varchar(255) NOT NULL,
  `des` text NOT NULL,
  `remarks` text NOT NULL,
  `image` varchar(255) DEFAULT NULL,
  `acc_id` varchar(255) NOT NULL,
  `date_submitted` datetime DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `finance_list`
--

INSERT INTO `finance_list` (`id`, `inq_id`, `finance_inq_id`, `des`, `remarks`, `image`, `acc_id`, `date_submitted`) VALUES
(82, 'FSJkXFzxkC', 'FNC02', 'Current medical claim is RM80 or RM100?', '', NULL, 'snyajRZeBK', '2024-07-09 03:09:58');

-- --------------------------------------------------------

--
-- Table structure for table `hrinq_list`
--

CREATE TABLE `hrinq_list` (
  `id` varchar(255) NOT NULL,
  `hr_inq_id` varchar(255) NOT NULL,
  `hr_inq` varchar(255) NOT NULL,
  `des` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `hrinq_list`
--

INSERT INTO `hrinq_list` (`id`, `hr_inq_id`, `hr_inq`, `des`) VALUES
('669897b4f2542', '361897C64f', 'General Inquiry', '');

-- --------------------------------------------------------

--
-- Table structure for table `hr_list`
--

CREATE TABLE `hr_list` (
  `id` int(11) NOT NULL,
  `inq_id` varchar(255) NOT NULL,
  `hr_inq_id` varchar(255) NOT NULL,
  `des` text DEFAULT NULL,
  `remarks` text DEFAULT NULL,
  `image` text DEFAULT NULL,
  `acc_id` varchar(255) NOT NULL,
  `date_submitted` datetime DEFAULT current_timestamp(),
  `priority_level` varchar(50) NOT NULL DEFAULT 'Medium'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `inventory-list`
--

CREATE TABLE `inventory-list` (
  `id` int(255) NOT NULL,
  `product-name` varchar(255) NOT NULL,
  `brand-id` varchar(255) NOT NULL,
  `category-id` varchar(255) NOT NULL,
  `location-id` varchar(255) NOT NULL,
  `part-number` varchar(255) NOT NULL,
  `serial-number` varchar(255) NOT NULL,
  `quantity` int(255) NOT NULL,
  `ownership` varchar(255) NOT NULL,
  `des` varchar(999) NOT NULL,
  `status` int(255) NOT NULL,
  `remark` varchar(255) NOT NULL,
  `created` varchar(255) NOT NULL,
  `inventory-id` varchar(255) NOT NULL,
  `reserve_qty` int(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `inventory-list`
--

INSERT INTO `inventory-list` (`id`, `product-name`, `brand-id`, `category-id`, `location-id`, `part-number`, `serial-number`, `quantity`, `ownership`, `des`, `status`, `remark`, `created`, `inventory-id`, `reserve_qty`) VALUES
(1, 'Aruba 6300M 24SFP+ 4SFP56 Switch', '**********', '01', 'Vespa Room | 01', 'JL658A', 'SG36LMP1WL', 1, 'Genting Malaysia Berhad', 'SO-004531\r\nCCTV - Aruba Switches', 1, '', 'Mudzaffar Haniffuddin', 'N7Uo5Mw21H', 0),
(2, 'Aruba 6300M 24SFP+ 4SFP56 Switch', '**********', '01', 'Vespa Room | 01', 'JL658A', 'SG36LMP1WQ', 1, 'Genting Malaysia Berhad', 'SO-004531\r\nCCTV - Aruba Switches', 1, '', 'Mudzaffar Haniffuddin', 'njqTK6QVV4', 0),
(3, 'Aruba 6300M 24SFP+ 4SFP56 Switch', '**********', '7577505059', 'Vespa Room | 01', 'JL658A', 'SG36LMP1WR', 1, 'Genting Malaysia Berhad', 'SO-004531\r\nCCTV - Aruba Switches', 1, '', 'Mudzaffar Haniffuddin', 'jIFeG2AYq1', 0),
(4, 'Aruba 6300M 24SFP+ 4SFP56 Switch', '**********', '01', 'Vespa Room | 01', 'JL658A', 'SG36LMP1ZC', 1, 'Genting Malaysia Berhad', 'SO-004531 \r\nCCTV - Aruba Switches', 1, '', 'Mudzaffar Haniffuddin', '9082JYHfKl', 0),
(5, 'Aruba 6200M 24G Class4 PoE 4SFP+ Switch', '**********', '01', 'Vespa Room | 01', 'R8Q68A', 'VN36LB80GW', 1, 'Genting Malaysia Berhad', 'SO-004531\r\nCCTV - Aruba Switches', 1, '', 'Mudzaffar Haniffuddin', 'zZIcL4CFxz', 0),
(6, 'Aruba 6200M 24G Class4 PoE 4SFP+ Switch', '**********', '01', 'Vespa Room | 01', 'R8Q68A', 'VN36LB80H6', 1, 'Genting Malaysia Berhad', 'SO-004531 \r\nCCTV - Aruba Switches', 1, '', 'Mudzaffar Haniffuddin', '7ixWLmQnus', 0),
(7, 'Aruba 10G SFP+ to SFP+ 1m DAC Cable', '**********', '**********', 'Vespa Room | 01', 'J9281D', 'CN2AKS3DRJ', 1, 'Genting Malaysia Berhad', 'SO-004531 \r\nCCTV - Aruba Switches', 1, '', 'Mudzaffar Haniffuddin', 'jK6pZtQMVD', 0),
(8, 'Aruba 10G SFP+ to SFP+ 1m DAC Cable', '**********', '**********', 'Vespa Room | 01', 'J9281D', 'CN2AKS3DRK', 1, 'Genting Malaysia Berhad', 'SO-004531 \r\nCCTV - Aruba Switches', 1, '', 'Mudzaffar Haniffuddin', '2CWLetXUyY', 0),
(9, 'Aruba 10G SFP+ to SFP+ 1m DAC Cable', '**********', '**********', 'Vespa Room | 01', 'J9281D', 'CN2AKS3DS1', 1, 'Genting Malaysia Berhad', 'SO-004531 \r\nCCTV - Aruba Switches', 1, '', 'Mudzaffar Haniffuddin', '1P4sG4L0Ri', 0),
(10, 'Aruba 10G SFP+ to SFP+ 1m DAC Cable', '**********', '**********', 'Vespa Room | 01', 'J9281D', 'CN2AKS3HD0', 1, 'Genting Malaysia Berhad', 'SO-004531 \r\nCCTV - Aruba Switches', 1, '', 'Mudzaffar Haniffuddin', 'FRLwrwQnK7', 0),
(11, 'Aruba 10G SFP+ LC LR 10km SMF Transceiver', '**********', '06', 'Vespa Room | 01', 'J9151E', 'CN27KCC55X', 1, 'Genting Malaysia Berhad', 'SO-004531 \r\nCCTV - Aruba Switches', 1, '', 'Mudzaffar Haniffuddin', 'QL3kWYKXSR', 0),
(12, 'Aruba 10G SFP+ LC LR 10km SMF Transceiver', '**********', '06', 'Vespa Room | 01', 'J9151E', 'CN27KCC0FR', 1, 'Genting Malaysia Berhad', 'SO-004531 \r\nCCTV - Aruba Switches', 1, '', 'Mudzaffar Haniffuddin', 'nrRET67GRU', 0),
(13, 'Aruba 10G SFP+ LC LR 10km SMF Transceiver', '**********', '06', 'Vespa Room | 01', 'J9151E', 'CN27KCC5F9', 1, 'Genting Malaysia Berhad', 'SO-004531 \r\nCCTV - Aruba Switches', 1, '', 'Mudzaffar Haniffuddin', 'Fs54y1LIzN', 0),
(14, 'Aruba 10G SFP+ LC LR 10km SMF Transceiver', '**********', '06', 'Vespa Room | 01', 'J9151E', 'CN27KCC2P1', 1, 'Genting Malaysia Berhad', 'SO-004531 \r\nCCTV - Aruba Switches', 1, '', 'Mudzaffar Haniffuddin', 'cVeW9jBHRm', 0),
(15, 'Aruba 10G SFP+ LC ER 40km SMF Transceiver', '**********', '06', 'Vespa Room | 01', 'J9153D', 'MY26KBY06J', 1, 'Genting Malaysia Berhad', 'SO-004531 \r\nCCTV - Aruba Switches', 1, '', 'Mudzaffar Haniffuddin', 'Os4ao9YOK9', 0),
(16, 'Aruba 10G SFP+ LC ER 40km SMF Transceiver', '**********', '06', 'Vespa Room | 01', 'J9153D', 'MY26KBY06K', 1, 'Genting Malaysia Berhad', 'SO-004531\r\nCCTV - Aruba Switches', 1, '', 'Mudzaffar Haniffuddin', 'Ys1jk1XRDf', 0),
(17, 'Aruba 10G SFP+ LC ER 40km SMF Transceiver', '**********', '06', 'Vespa Room | 01', 'J9153D', 'MY26KBY06R', 1, 'Genting Malaysia Berhad', 'SO-004531 \r\nCCTV - Aruba Switches', 1, '', 'Mudzaffar Haniffuddin', 'Ib99ru6lLa', 0),
(18, 'Aruba 10G SFP+ LC ER 40km SMF Transceiver', '**********', '06', 'Vespa Room | 01', 'J9153D', 'MY26KBY078', 1, 'Genting Malaysia Berhad', 'SO-004531 \r\nCCTV - Aruba Switches', 1, '', 'Mudzaffar Haniffuddin', '0kAXvXmZQX', 0),
(19, 'Aruba X371 12VDC 250W PS', '**********', '3431257563', 'Vespa Room | 01', 'JL085A', 'TH33GZ8WCV', 1, 'Genting Malaysia Berhad', 'SO-004531 \r\nCCTV - Aruba Switches', 1, '', 'Mudzaffar Haniffuddin', '5ctuiJT2q7', 0),
(20, 'Aruba X371 12VDC 250W PS', '**********', '3431257563', 'Vespa Room | 01', 'JL085A', 'TH33GZ8VGY', 1, 'Genting Malaysia Berhad', 'SO-004531 \r\nCCTV - Aruba Switches', 1, '', 'Mudzaffar Haniffuddin', 't1E1OBVjvA', 0),
(21, 'Aruba X371 12VDC 250W PS', '**********', '3431257563', 'Vespa Room | 01', 'JL085A', 'TH33GZ8VGZ', 1, 'Genting Malaysia Berhad', 'SO-004531 \r\nCCTV - Aruba Switches', 1, '', 'Mudzaffar Haniffuddin', 'AVAXzR1Z6K', 0),
(22, 'Aruba X371 12VDC 250W PS', '**********', '3431257563', 'Vespa Room | 01', 'JL085A', 'TH33GZ8ZJW', 1, 'Genting Malaysia Berhad', 'SO-004531 \r\nCCTV - Aruba Switches', 1, '', 'Mudzaffar Haniffuddin', 'YpcDkUXltK', 0),
(23, 'Aruba X371 12VDC 250W PS', '**********', '3431257563', 'Vespa Room | 01', 'JL085A', 'TH33GZ8VL5', 1, 'Genting Malaysia Berhad', 'SO-004531 \r\nCCTV - Aruba Switches', 1, '', 'Mudzaffar Haniffuddin', 'DDpe8XsYMg', 0),
(24, 'Aruba X371 12VDC 250W PS', '**********', '3431257563', 'Vespa Room | 01', 'JL085A', 'TH33GZ8VDX', 1, 'Genting Malaysia Berhad', 'SO-004531 \r\nCCTV - Aruba Switches', 1, '', 'Mudzaffar Haniffuddin', 'FfIiCdj8Kh', 0),
(80, 'Aruba X371 12VDC 250W PS', '**********', '3431257563', 'Vespa Room | 01', 'JL085A', 'TH33GZ8VHZ', 1, 'Genting Malaysia Berhad', 'SO-004531 CCTV - Aruba Switches', 1, '', 'Mudzaffar Haniffuddin', '4qUvXNku5K', 0),
(81, 'Aruba X371 12VDC 250W PS', '**********', '3431257563', 'Vespa Room | 01', 'JL085A', 'TH33GZ8ZL4', 1, 'Genting Malaysia Berhad', 'SO-004531 CCTV - Aruba Switches', 1, '', 'Mudzaffar Haniffuddin', 'zVLiz0FGug', 0),
(82, 'Aruba X372 54VDC 680W PS', '**********', '3431257563', 'Vespa Room | 01', 'JL086A', 'TH35GZ90X6', 1, 'Genting Malaysia Berhad', 'SO-004531 CCTV - Aruba Switches', 1, '', 'Mudzaffar Haniffuddin', 'WyXTb12syV', 0),
(83, 'Aruba X372 54VDC 680W PS', '**********', '3431257563', 'Vespa Room | 01', 'JL086A', 'TH35GZ90GK', 1, 'Genting Malaysia Berhad', 'SO-004531 CCTV - Aruba Switches', 1, '', 'Mudzaffar Haniffuddin', 'NkIA7Ym4ZO', 0),
(84, 'Aruba X372 54VDC 680W PS', '**********', '3431257563', 'Vespa Room | 01', 'JL086A', 'TH35GZ90FH', 1, 'Genting Malaysia Berhad', 'SO-004531 CCTV - Aruba Switches', 1, '', 'Mudzaffar Haniffuddin', 'xf6rJqmrEK', 0),
(85, 'Aruba X372 54VDC 680W PS', '**********', '3431257563', 'Vespa Room | 01', 'JL086A', 'TH35GZ9109', 1, 'Genting Malaysia Berhad', 'SO-004531 CCTV - Aruba Switches', 1, '', 'Mudzaffar Haniffuddin', 'rtBTyRGH3B', 0),
(86, '24-Port SFP+ interfaces, 2-Port 40G QSFP+ interfaces, Dual Fan Slots, Dual Power Slots', '3107439717', '01', 'Server Room | 01', 'NSS5810-26XF', '23006814001840', 1, 'CLL', 'For internal use', 1, '', 'Mudzaffar Haniffuddin', 'wENghr6vRo', 0),
(87, 'V2 Version: FAN-01C-01B Fan Module For NSS5810-26XF', '3107439717', '7577505059', 'Server Room | 01', 'FAN-01C-01B', '23006715001230', 1, 'CLL', 'For internal use', 1, '', 'Mudzaffar Haniffuddin', 'Cj2VM4oRC6', 0),
(88, 'V2 Version: FAN-01C-01B Fan Module For NSS5810-26XF', '3107439717', '7577505059', 'Server Room | 01', 'FAN-01C-01B', '23006715001229', 1, 'CLL', 'Internal use', 1, '', 'Mudzaffar Haniffuddin', 'sz9xsD0CEr', 0),
(89, '120W AC Power Supply Module', '3107439717', '3431257563', 'Vespa Room | 01', 'AD120M-HS0N', '23165814001411', 1, 'CLL', 'For internal use', 1, '', 'Mudzaffar Haniffuddin', 'EEW7a7ZuIk', 0),
(90, '120W AC Power Supply Module', '3107439717', '3431257563', 'Server Room | 01', 'AD120M-HS0N', '23165830000277', 1, 'CLL', 'For internal use', 1, '', 'Mudzaffar Haniffuddin', 'D9tLKNkGUE', 0),
(91, '10G SFP+ 850nm 300m LC DDM Dual Core multi-mode Transceiver', '3107439717', '06', 'Server Room | 01', 'MP-S851X3-NCLM	', 'A853220820741', 1, 'CLL', 'For internal use', 1, '', 'Mudzaffar Haniffuddin', 'cKcL63jgb7', 0),
(97, 'Aruba Instant On 1930 24G 4SFP/SFP+ Switch', '**********', '01', 'Server Room | 01', 'JL682A', 'CN20KPDFGD', 1, 'Eco Shop', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', '0wlVofDBs0', 0),
(98, 'Aruba Instant On 1930 24G 4SFP/SFP+ Switch', '**********', '01', 'Server Room | 01', 'JL682A', 'CN20KPDDRQ', 1, 'Eco Shop', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'PRYOtpfi7d', 0),
(99, 'Aruba Instant On 1930 24G 4SFP/SFP+ Switch', '**********', '01', 'Server Room | 01', 'JL682A', 'CN20KPDF0P', 1, 'Eco Shop', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'WYK5lWmIgH', 0),
(100, 'Aruba Instant On 1930 24G 4SFP/SFP+ Switch', '**********', '01', 'Server Room | 01', 'JL682A', 'CN20KPDDWT', 1, 'Eco Shop', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'PYtQYGPbA8', 0),
(101, 'Aruba Instant On 1930 24G 4SFP/SFP+ Switch', '**********', '01', 'Server Room | 01', 'JL682A', 'CN20KPDDQY', 1, 'Eco Shop', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'EFRqiN1rOH', 0),
(102, 'Aruba Instant On 1930 24G 4SFP/SFP+ Switch', '**********', '01', 'Server Room | 01', 'JL682A', 'CN20KPDFHF', 1, 'Eco Shop', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'irIaguuOK8', 0),
(103, 'Aruba Instant On 1930 24G 4SFP/SFP+ Switch', '**********', '01', 'Server Room | 01', 'JL682A', 'CN20KPDDVK', 1, 'Eco Shop', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', '2ZxaBXBpDH', 0),
(104, 'Aruba Instant On 1930 24G 4SFP/SFP+ Switch', '**********', '01', 'Server Room | 01', 'JL682A', 'CN20KPDFHC', 1, 'Eco Shop', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'ypOZeo6jac', 0),
(105, 'Aruba Instant On 1930 24G 4SFP/SFP+ Switch', '**********', '01', 'Server Room | 01', 'JL682A', 'CN20KPDD34', 1, 'Eco Shop', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'd1HwutyeaH', 0),
(106, 'Aruba Instant On 1930 24G 4SFP/SFP+ Switch', '**********', '01', 'Server Room | 01', 'JL682A', 'CN20KPDDH3', 1, 'Eco Shop', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'iSKxySrstm', 0),
(107, 'Aruba Instant On 1930 24G 4SFP/SFP+ Switch', '**********', '01', 'Server Room | 01', 'JL682A', 'CN20KPDPF8', 1, 'Eco Shop', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'qSgTEa2IzG', 0),
(108, 'Aruba Instant On 1930 24G 4SFP/SFP+ Switch', '**********', '01', 'Server Room | 01', 'JL682A', 'CN20KPDNS3', 1, 'Eco Shop', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'ftaU37KZsi', 0),
(109, 'Aruba Instant On 1930 24G 4SFP/SFP+ Switch', '**********', '01', 'Server Room | 01', 'JL682A', 'CN20KPDNXZ', 1, 'Eco Shop', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', '0fw3WTfNwm', 0),
(110, 'Aruba Instant On 1930 24G 4SFP/SFP+ Switch', '**********', '01', 'Server Room | 01', 'JL682A', 'CN20KPDPN2', 1, 'Eco Shop', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'Bx09rfYU1E', 0),
(111, 'Aruba Instant On 1930 24G 4SFP/SFP+ Switch', '**********', '01', 'Server Room | 01', 'JL682A', 'CN20KPDPHJ', 1, 'Eco Shop', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', '2tM3at93jO', 0),
(112, 'Aruba Instant On 1830 24G 2SFP Switch', '**********', '01', 'Server Room | 01', 'JL812A', 'VN29KYCCTW', 1, 'Eco Shop', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'HgAZ30Qplm', 0),
(113, 'Aruba Instant On 1930 24G Class4 PoE 4SFP/SFP+ 370W Switch', '**********', '01', 'Server Room | 01', 'JL684A', 'CN13KPG0P8', 1, 'Eco Shop', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'US7vCHI26W', 0),
(114, 'miniFTTO Main Gaterway_OptiXstar F1001_AC', '**********', '01', 'Vespa Room | 01', '4581136H078', '', 1, 'CLL', 'SO-004951', 1, '', ' Mudzaffar Haniffuddin', 'cDu6ps5gXz', 0),
(115, 'miniFTTO Main Gateway OptiXstar F1001_AC', '**********', '01', 'Vespa Room | 01', '4581136H077', '', 1, 'CLL', 'SO-004951', 1, '', ' Mudzaffar Haniffuddin', 'CUQyZL4gFU', 0),
(116, 'ODN_ODN ATB21 (Home FTTR ODN Optical Component)', '**********', '01', 'Vespa Room | 01', '4581136F092', '', 1, 'CLL', 'SO-004951', 1, '', ' Mudzaffar Haniffuddin', 'nXhOm57mm0', 0),
(117, 'ODN spare parts_ODN ATB21 (Home FTTR ODN Optical Component)_1', '**********', '01', 'Vespa Room | 01', '4581136F326', '', 1, 'CLL', 'SO-004951', 1, '', ' Mudzaffar Haniffuddin', 'QI8O0GOiIK', 0),
(118, 'ODN spare parts_MiniFTTO Accessory Bag_1', '**********', '01', 'Vespa Room | 01', '4581136G538', '', 1, 'CLL', 'SO-004951', 1, '', ' Mudzaffar Haniffuddin', 'l0ApyiTYty', 0),
(119, 'ODN spare parts_MiniFTTO Accessory Bag_1', '**********', '01', 'Vespa Room | 01', '4581136N078', '', 1, 'CLL', 'SO-004951', 1, '', ' Mudzaffar Haniffuddin', 'RIo5g3xTih', 0),
(120, 'ODN_MiniFTTO Accessory Bag', '**********', '01', 'Vespa Room | 01', '4581136N077', '', 1, 'CLL', 'SO-004951', 1, '', ' Mudzaffar Haniffuddin', '4dH36z5Ria', 0),
(121, 'ODN_MiniFTTO Accessory Bag', '**********', '01', 'Vespa Room | 01', '4581136G471', '', 1, 'CLL', 'SO-004951', 1, '', ' Mudzaffar Haniffuddin', 'GMWneXMF7R', 0),
(122, 'Panel ONU_006_OptiXstar F100P_2G', '**********', '01', 'Vespa Room | 01', '4581136F604', '', 1, 'CLL', 'SO-004951', 1, '', ' Mudzaffar Haniffuddin', 'UZKMo8NQde', 0),
(123, 'Ceiling AP_013_OptiXstar F600C_30_1GH', '**********', '01', 'Vespa Room | 01', '4581136G539', '', 1, 'CLL', 'SO-004951', 1, '', ' Mudzaffar Haniffuddin', 'dVdJI8vOiv', 0),
(124, 'Desktop AP_001_OptiXstar F600D_30_4G1V', '**********', '01', 'Vespa Room | 01', '4581136F296', '', 1, 'CLL', 'SO-004951', 1, '', ' Mudzaffar Haniffuddin', 'kHJXDlIkQp', 0),
(125, 'PoE ONU_OptiXstar F200D_8P', '**********', '01', 'Vespa Room | 01', '4581136F096', '', 1, 'CLL', 'SO-004951', 1, '', ' Mudzaffar Haniffuddin', 'Djdw1FVAzo', 0),
(126, 'Indoor access terminal box,Wall-mounting,127*86*21mm, ', '**********', '01', 'Vespa Room | 01', '14261316', '2114261316BAP6016536', 1, 'CLL', 'SO-004951', 1, '', ' Mudzaffar Haniffuddin', 'BxitTIsNtb', 0),
(127, 'Indoor access terminal box,Wall-mounting,127*86*21mm, ', '**********', '01', 'Vespa Room | 01', '14261316', '2114261316BAP6011033 ', 1, 'CLL', 'SO-004951', 1, '', ' Mudzaffar Haniffuddin', '9P9BmP93gT', 0),
(128, 'Indoor access terminal box,Wall-mounting,127*86*21mm, ', '**********', '01', 'Vespa Room | 01', '14261316', '2114261316BAP6009486 ', 1, 'CLL', 'SO-004951', 1, '', ' Mudzaffar Haniffuddin', 'Qgr9VWvm2C', 0),
(129, 'Indoor access terminal box,Wall-mounting,127*86*21mm, ', '**********', '01', 'Vespa Room | 01', '14261316', '2114261316BAP6016682 ', 1, 'CLL', 'SO-004951', 1, '', ' Mudzaffar Haniffuddin', 'hE8iJZ52vh', 0),
(130, 'Indoor access terminal box,Wall-mounting,127*86*21mm, ', '**********', '01', 'Vespa Room | 01', '14261316', '2114261316BAP6010990 ', 1, 'CLL', 'SO-004951', 1, '', ' Mudzaffar Haniffuddin', 'jgrYIN5I9W', 0),
(131, 'Indoor access terminal box,Wall-mounting,127*86*21mm, ', '**********', '01', 'Vespa Room | 01', '14261316', '2114261316BAP6011038 ', 1, 'CLL', 'SO-004951', 1, '', ' Mudzaffar Haniffuddin', '61WDws048G', 0),
(132, 'Indoor access terminal box,Wall-mounting,127*86*21mm, ', '**********', '01', 'Vespa Room | 01', '14261316', '2114261316BAP6016571', 1, 'CLL', 'SO-004951', 1, '', ' Mudzaffar Haniffuddin', '2srNbrn2lM', 0),
(133, 'FTTR Fiber Installation Kit ', '**********', '01', 'Vespa Room | 01', '1523843', '2101523843AFPA002248', 1, 'CLL', 'SO-004951', 1, '', ' Mudzaffar Haniffuddin', 'ZdkGiUYeHM', 0),
(134, 'Huawei OptiXstar F600D-30-4G1V,GPON Terminal,SC/UPC,White shell,Europe Plug Adapter(Non-EU)', '**********', '01', 'Vespa Room | 01', '02233UWQ', '2102233UWQ10PB100005', 1, 'CLL', 'SO-004951', 1, '', ' Mudzaffar Haniffuddin', '4z9E6O8WI1', 0),
(135, 'F600C-30-1GH Multi-service Access Equipment,GPON Uplink(XC/UPC),1GE+2*2 2.4G WIFI,12V DC/POF,Without Power Cable,For Oversea(Non-EU)', '**********', '01', 'Vespa Room | 01', '********', '21********LDPA000647 ', 1, 'CLL', 'SO-004951', 1, '', ' Mudzaffar Haniffuddin', 'HLZv3hX17Z', 0),
(136, 'F600C-30-1GH Multi-service Access Equipment,GPON Uplink(XC/UPC),1GE+2*2 2.4G WIFI,12V DC/POF,Without Power Cable,For Oversea(Non-EU)', '**********', '01', 'Vespa Room | 01', '********', '21********LDPA000635 ', 1, 'CLL', 'SO-004951', 1, '', ' Mudzaffar Haniffuddin', 'z3yB0JH78N', 0),
(137, 'F600C-30-1GH Multi-service Access Equipment,GPON Uplink(XC/UPC),1GE+2*2 2.4G WIFI,12V DC/POF,Without Power Cable,For Oversea(Non-EU)', '**********', '01', 'Vespa Room | 01', '********', '21********LDPA000633 ', 1, 'CLL', 'SO-004951', 1, '', ' Mudzaffar Haniffuddin', 'QcyBmkH2go', 0),
(138, 'F600C-30-1GH Multi-service Access Equipment,GPON Uplink(XC/UPC),1GE+2*2 2.4G WIFI,12V DC/POF,Without Power Cable,For Oversea(Non-EU)', '**********', '01', 'Vespa Room | 01', '********', '21********LDPA000620 ', 1, 'CLL', 'SO-004951', 1, '', ' Mudzaffar Haniffuddin', 'mzPe3QmJsJ', 0),
(139, 'F600C-30-1GH Multi-service Access Equipment,GPON Uplink(XC/UPC),1GE+2*2 2.4G WIFI,12V DC/POF,Without Power Cable,For Oversea(Non-EU)', '**********', '01', 'Vespa Room | 01', '********', '21********LDPA000607 ', 1, 'CLL', 'SO-004951', 1, '', ' Mudzaffar Haniffuddin', 'Jh6DvsJOmK', 0),
(140, 'F600C-30-1GH Multi-service Access Equipment,GPON Uplink(XC/UPC),1GE+2*2 2.4G WIFI,12V DC/POF,Without Power Cable,For Oversea(Non-EU)', '**********', '01', 'Vespa Room | 01', '********', '21********LDPA000637 ', 1, 'CLL', 'SO-004951', 1, '', ' Mudzaffar Haniffuddin', 'qZtgwO4CDL', 0),
(141, 'F600C-30-1GH Multi-service Access Equipment,GPON Uplink(XC/UPC),1GE+2*2 2.4G WIFI,12V DC/POF,Without Power Cable,For Oversea(Non-EU)', '**********', '01', 'Vespa Room | 01', '********', '21********LDPA000613 ', 1, 'CLL', 'SO-004951', 1, '', ' Mudzaffar Haniffuddin', 'SMjuO0Cuez', 0),
(142, 'F600C-30-1GH Multi-service Access Equipment,GPON Uplink(XC/UPC),1GE+2*2 2.4G WIFI,12V DC/POF,Without Power Cable,For Oversea(Non-EU)', '**********', '01', 'Vespa Room | 01', '********', '21********LDPA000612', 1, 'CLL', 'SO-004951', 1, '', ' Mudzaffar Haniffuddin', '9ivzXDpGaF', 0),
(143, 'HPE DL380 Gen10 8SFF NC CTO Server', '**********', '**********', 'Vespa Room | 01', 'P19720-B21', 'SGH039W9S4', 1, 'Eco Shop', 'Loan by EN', 1, '', ' Mudzaffar Haniffuddin', '7EQHAlWG03', 0),
(144, 'Cisco Compute UCS C220 M5 SFF', '**********', '**********', 'Server Room | 01', 'UCSC-C220-M5SX=', 'WZP25021CG8', 1, 'Maybank', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'BrdMrRbkzo', 0),
(145, 'Cisco Compute UCS C220 M5 SFF', '**********', '**********', 'Server Room | 01', 'UCSC-C220-M5SX=', 'WZP25160BYZ', 1, 'Maybank', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'sSZ3RjrorG', 0),
(146, 'Cisco UCS-FI-6454 + Rail kit', '**********', '**********', 'Server Room | 01', 'UCS-FI-6454-U-RF', 'FD0245219YC', 1, 'Maybank', 'loan to Wei Lip COMSTOR', 1, '', ' Mudzaffar Haniffuddin', '9r5jA1ZHU1', 0),
(147, 'Cisco UCS-FI-6454 + Rail kit', '**********', '**********', 'Server Room | 01', 'UCS-FI-6454-U-RF', 'FD0245219YP', 1, 'Maybank', 'loan to Wei Lip COMSTOR', 1, '', ' Mudzaffar Haniffuddin', '7DEHEZRQ3a', 0),
(148, '10G RJ45 Copper SFP Transceiver Module 10GBase-Tx Ethernet Gpon Olt Fiber Optic FTTH Compatible with Cisco/Mikrotik Switch 30m', '5e924-6L44', '06', 'Server Room | 01', 'JT-C1TE-R01', '**********', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'moXgJYK1yQ', 0),
(149, '10G RJ45 Copper SFP Transceiver Module 10GBase-Tx Ethernet Gpon Olt Fiber Optic FTTH Compatible with Cisco/Mikrotik Switch 30m', '5e924-6L44', '06', 'Server Room | 01', 'JT-C1TE-R01', '**********', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'u7plNsUYUF', 0),
(150, '10G RJ45 Copper SFP Transceiver Module 10GBase-Tx Ethernet Gpon Olt Fiber Optic FTTH Compatible with Cisco/Mikrotik Switch 30m', '5e924-6L44', '06', 'Server Room | 01', 'JT-C1TE-R01', '**********', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'v0m0ZNSesc', 0),
(151, '10G RJ45 Copper SFP Transceiver Module 10GBase-Tx Ethernet Gpon Olt Fiber Optic FTTH Compatible with Cisco/Mikrotik Switch 30m', '5e924-6L44', '06', 'Server Room | 01', 'JT-C1TE-R01', '2306010030', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', '2eDGU7qxdz', 0),
(152, '1000BASE-T RJ45 100M Transceiver', '2070235736', '06', 'Server Room | 01', 'SFP-GE-T', '202304040041', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'd3ihm3cKNJ', 0),
(153, '1000BASE-T RJ45 100M Transceiver', '2070235736', '06', 'Server Room | 01', 'SFP-GE-T', '202304040060', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'AhaxjBpDXJ', 0),
(154, '10Gb iSCSI/16Gb FC Universal SFP+ Transceiver', '8131462793', '06', 'Server Room | 01', 'AFBR-57F5UMZ-LVX', 'AC2244J02CF', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', '6HzTQcJk6T', 0),
(155, '10Gb iSCSI/16Gb FC Universal SFP+ Transceiver', '8131462793', '06', 'Server Room | 01', 'AFBR-57F5UMZ-LVX', 'AC2244J02CB', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'NdNtSsjbWx', 0),
(156, '10G Copper SFP 30m Transceiver', '0742870078', '06', 'Server Room | 01', 'SFP-10G-T', 'MACI220825118', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', '08hVIvRVjB', 0),
(157, '10G Copper SFP 30m Transceiver', '0742870078', '06', 'Server Room | 01', 'SFP-10G-T', 'MACI220825120', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'E9Ealcnwxi', 0),
(158, '10G Copper SFP 30m Transceiver', '0742870078', '06', 'Server Room | 01', 'SFP-10G-T', 'MACI220825117', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'Dke3EKIWYd', 0),
(159, '10G Copper SFP 30m Transceiver', '0742870078', '06', 'Server Room | 01', 'SFP-10G-T', 'MACI220825119', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'g2oy3zSxiW', 0),
(160, '10G Copper SFP 30m Transceiver', '0742870078', '06', 'Server Room | 01', 'SFP-10G-T', 'MACI220825115', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'PbIV3cz12X', 0),
(161, '10G Copper SFP 30m Transceiver', '0742870078', '06', 'Server Room | 01', 'SFP-10G-T', 'MACI220825116', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'WygsLhQV7h', 0),
(162, 'HPE 10GB SR SFP+ Transceiver', '**********', '06', 'Server Room | 01', '456096-001', '7CR119J3ZY', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'QYIvuvYkUA', 0),
(163, '	HPE 10GB SR SFP+ Transceiver', '**********', '06', 'Server Room | 01', '456096-001', '7CR119J7HH', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'GSEnF99MwK', 0),
(164, 'HPE 10GB SR SFP+ Transceiver', '**********', '06', 'Server Room | 01', '456096-001', '7CR119J40T', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'guFQxmW1OE', 0),
(165, 'HPE 10GB SR SFP+ Transceiver', '**********', '06', 'Server Room | 01', '456096-001', '7CR119J42S', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'XX3nd4oyBN', 0),
(166, 'HPE 10GB SR SFP+ Transceiver', '**********', '06', 'Server Room | 01', '456096-001', '7CR119J5BL', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', '6H0qLLo0nF', 0),
(167, 'HPE 10GB SR SFP+ Transceiver', '**********', '06', 'Server Room | 01', '455885-001', '7CR127JQSR', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'ST0wLQVhpY', 0),
(168, 'HPE 10GB SR SFP+ Transceiver', '**********', '06', 'Server Room | 01', '455885-001', '7CR127JSCQ', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', '3oBJY62X7N', 0),
(169, 'HPE 10GB SR SFP+ Transceiver', '**********', '06', 'Server Room | 01', '455885-001', '7CR127J31Q', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'CbLsQU4Lnh', 0),
(170, 'HPE 10GB SR SFP+ Transceiver', '**********', '06', 'Server Room | 01', '455885-001', '7CR127J11R', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'b7j9D6rR9P', 0),
(171, 'HPE 10GB SR SFP+', '**********', '06', 'Server Room | 01', '455885-001', '7CR119J9S0', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', '95vhtwL6LT', 0),
(172, 'HPE 10GB SR SFP+', '**********', '06', 'Server Room | 01', '455885-001', '7CR119J6J4', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'tIaQCRg70l', 0),
(173, 'HPE 10GB SR SFP+', '**********', '06', 'Server Room | 01', '455885-001', '7CR119JC67', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', '91fYGqhoEp', 0),
(174, 'HPE 10GB SR SFP+', '**********', '06', 'Server Room | 01', '455885-001', '7CR119J54B', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'iT6zMR7LPX', 0),
(175, 'HPE 10GB SR SFP+', '**********', '06', 'Server Room | 01', '455885-001', '7CR119J5GC', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'na0tidWcle', 0),
(176, 'HPE 10GB SR SFP+', '**********', '06', 'Server Room | 01', '455885-001', '7CR041J9L9', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'diVLJuUTZF', 0),
(177, 'HPE 10GB SR SFP+', '**********', '06', 'Server Room | 01', '455885-001', '7CR041JFDM', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'bqih6kM5f3', 0),
(178, 'HPE 10GB SR SFP+', '**********', '06', 'Server Room | 01', '455885-001', '7CR119J46R', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', '1ZpWKO801u', 0),
(179, 'HPE 10GB SR SFP+', '**********', '06', 'Server Room | 01', '455885-001', '7CR119J61X', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'tsaIrFspcl', 0),
(180, 'HPE 10GB SR SFP+', '**********', '06', 'Server Room | 01', '455885-001', '7CR119J5CS', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'i7w1PPSAdE', 0),
(181, 'HPE 10GB SR SFP+', '**********', '06', 'Server Room | 01', '455885-001', '7CR119J42X', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'z30xiGR3v8', 0),
(182, 'HPE 10GB SR SFP+', '**********', '06', 'Server Room | 01', '455885-001', '7CR119J42B', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'vcRnh6ajAF', 0),
(183, 'HPE 10GB SR SFP+', '**********', '06', 'Server Room | 01', '455885-001', '7CR041JF9C', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'Be0MiOajZU', 0),
(184, 'HPE 10GB SR SFP+', '**********', '06', 'Server Room | 01', '455885-001', '7CR119J4C2', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', '2LNcgVGEn6', 0),
(185, 'HPE 10GB SR SFP+', '**********', '06', 'Server Room | 01', '455885-001', '7CR119J441', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'hjW3439JIh', 0),
(186, 'HPE 10GB SR SFP+', '**********', '06', 'Server Room | 01', '455885-001', '7CR134J0BX', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', '9xIPbDKRii', 0),
(187, 'HPE 10GB SR SFP+', '**********', '06', 'Server Room | 01', '455885-001', '7CR041J9HY', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', '5vsLvtuD5t', 0),
(188, 'HPE 10GB SR SFP+', '**********', '06', 'Server Room | 01', '455885-001', '7CR119J8SZ', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'oUdXeyUpST', 0),
(189, 'HPE 10GB SR SFP+', '**********', '06', 'Server Room | 01', '455885-001', '7CR119JB6Q', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'kce9wqO3zi', 0),
(190, 'HPE 10GB SR SFP+', '**********', '06', 'Server Room | 01', '455885-001', '7CR119J99H', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'JKyRmHcRKF', 0),
(191, 'HPE 10GB SR SFP+', '**********', '06', 'Server Room | 01', '455885-001', '7CR119J81Y', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', '7IyeThDoFZ', 0),
(192, 'HPE 10GB SR SFP+', '**********', '06', 'Server Room | 01', '455885-001', '7CR119J58R', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'KsjWAYTB3d', 0),
(193, 'HPE 10GB SR SFP+', '**********', '06', 'Server Room | 01', '455885-001', '7CR119JBL4', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'KigVpdMbgv', 0),
(194, 'HPE 10GB SR SFP+', '**********', '06', 'Server Room | 01', '455885-001', '7CR119J58X', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'VAxCWFJ2kq', 0),
(195, 'HPE 10GB SR SFP+', '**********', '06', 'Server Room | 01', '455885-001', '7CR119J61N', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'g6bTKFYSdQ', 0),
(196, 'HPE 10GB SR SFP+', '**********', '06', 'Server Room | 01', '455885-001', '7CR041J9N7', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'GVK8FMk5zr', 0),
(197, 'Cisco Multimode SFP Module Transceiver', '**********', '06', 'Server Room | 01', 'GLC-SX-MMD', 'FNS19281VUW', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'KnefIFBHWp', 0),
(198, 'Cisco Gigabit Ethernet SFP 1000Base-T Mini-GBIC Transceiver', '**********', '06', 'Server Room | 01', 'GLC-T', 'MTC172500CN', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'suzIPltYzJ', 0),
(199, 'Cisco 1000BASE-T Standard Copper SFP Transceiver Module', '**********', '06', 'Server Room | 01', 'GLC-TE', 'MTC214707UX', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'sKS2bAbYGT', 0),
(200, 'Cisco 1000BASE-T Standard Copper SFP Transceiver Module', '**********', '06', 'Server Room | 01', 'GLC-TE', 'MTC214702WC', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'jQGlxQVsyo', 0),
(201, 'Cisco 1000BASE-T Standard Copper SFP Transceiver Module', '**********', '06', 'Server Room | 02', 'GLC-TE', 'AVC23042192', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'jwyEL7BSYU', 0),
(202, 'Cisco 1000BASE-T Standard Copper SFP Transceiver Module', '**********', '06', 'Server Room | 03', 'GLC-TE', 'AVC230820V9', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'dWLJMm8WU9', 0),
(203, 'Cisco 1000BASE-T SFP Transceiver Module RJ-45', '**********', '06', 'Server Room | 01', 'SFP-GE-T', 'MTC200206JF', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', '0vjKrnIQpG', 0),
(204, 'Cisco 1000BASE-T SFP Transceiver Module RJ-45', '**********', '06', 'Server Room | 01', 'SFP-GE-T', 'MTC1926044G', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'EOnbmCIllj', 0),
(205, '10Gbps 10Base-SR Multi-Mode Fiber 850nm LC Transceiver', '5021987615', '06', 'Server Room | 01', 'FTLX8571D3BCL-FC', 'ARP06EP', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'rrDC3ygXxD', 0),
(206, '10Gbps 10Base-SR Multi-Mode Fiber 850nm LC Tranceiver', '5021987615', '06', 'Server Room | 01', 'FTLX8571D3BCL-FC', 'ARP0658', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', '9AdKniy6hn', 0),
(207, '2Gb SFP 850nm SW GBIC Transceiver', '5021987615', '06', 'Server Room | 01', 'FTLF8519P2BNL', 'PBN3E7A', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'poC9ZgbxxH', 0),
(208, '2Gb SFP 850nm SW GBIC Transceiver', '5021987615', '06', 'Server Room | 01', 'FTLF8519P2BNL', 'PBQ3YM8', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'sJFYxv2mMW', 0),
(209, 'Fortinet FN-TRAN-GC 1 GE SFP RJ45 Transceiver Module', '0244180542', '06', 'Server Room | 01', 'FN-TRAN-GC', '232404003084', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'i9wE6K4N5y', 0),
(210, 'Fortinet FN-TRAN-GC 1 GE SFP RJ45 Transceiver Module', '0244180542', '06', 'Server Room | 01', 'FN-TRAN-GC', '232404003085', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'kAn4bvmZxL', 0),
(211, 'Finisar 10Gbps 10GBase-SR Multi-mode Fiber 300m 850nm Duplex LC Connector SFP+ Transceiver Module', '3501829193', '06', 'Server Room | 01', 'FTLX8574D3BCV', 'N52EA2G', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'w5Kfw2cRpb', 0),
(212, 'Finisar 1000BASE-T Copper SFP Fiber Optic Transceiver', '3501829193', '06', 'Server Room | 01', 'FCLF8521P2BTL', 'PWQ04VB', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'QPjdkgNHNt', 0),
(213, 'Finisar 1000BASE-T Copper SFP Fiber Optic Transceiver', '3501829193', '06', 'Server Room | 01', 'FCLF8521P2BTL', 'PWM1B6H', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'p6rgYhXA4D', 0),
(214, '10Gb iSCSI/16Gb FC Universal SFP+ Transceiver', '8131462793', '06', 'Server Room | 01', 'AFBR-57F5UMZ-LVX', 'AC2244J02CG', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'jPsqOyf3Xq', 0),
(215, '10Gb iSCSI/16Gb FC Universal SFP+ Transceiver', '8131462793', '06', 'Server Room | 01', 'AFBR-57F5UMZ-LVX', 'AC2244J02CA', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'KdtkuCKUDx', 0),
(216, 'Cisco 10GBASE-SR SFP+ Transceiver Module', '**********', '06', 'Server Room | 01', 'SFP-10G-SR', 'ACW24531FNJ', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'X4sLVUj0F9', 0),
(217, 'Cisco 10GBASE-SR SFP+ Transceiver Module', '**********', '06', 'Server Room | 01', 'SFP-10G-SR', 'ACW24531FNX', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'NIwINst5oS', 0),
(218, 'Cisco 10GBASE-SR SFP+ Transceiver Module', '**********', '06', 'Server Room | 01', 'SFP-10G-SR', 'ACW25031CNV', 1, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'W92DbTM22p', 0),
(219, '3m UK 3pin power cord cable', '2070235736', '**********', 'Server Room | 01', '-', '-', 38, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'q7B4BYePQK', 0),
(220, '1m C13-14 power cord cable', '2070235736', '**********', 'Server Room | 01', '-', '-', 74, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'cMtNgCzh6d', 0),
(221, '3m C13-14 power cord cable', '2070235736', '**********', 'Server Room | 01', '-', '-', 95, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'R3hTyPjP9a', 0),
(222, 'OM3 fiber partch cords LC-LC 3m', '1634112121', '**********', 'Server Room | 01', '-', '-', 6, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'LlRAWRnQ5K', 0),
(223, 'OM3 fiber partch cords LC-SC 3m', '1634112121', '**********', 'Server Room | 01', '-', '-', 10, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'L8v1sYOB72', 0),
(224, 'CAT6 UTP cables 5m', '4268328558', '**********', 'Server Room | 01', '-', '-', 11, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'uOa9Ro6h4S', 0),
(225, 'CAT6 UTP cables 3m', '4268328558', '**********', 'Server Room | 01', '-', '-', 12, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'KzUTIX5SQG', 0),
(226, 'CAT6 UTP cables 10m', '4268328558', '**********', 'Server Room | 01', '-', '-', 2, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'fmOlpqzVbR', 0),
(227, 'Casio Tape cartridge 6mm', '9223873625', '**********', 'Server Room | 01', '-', '-', 4, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'DCa7SsXaqG', 0),
(228, 'ANET LC-LC 3m LSZH OM3 Fiber Patch Cord 0.1DB', '4174530348', '**********', 'Server Room | 01', '-', '-', 32, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'MiWrElrvHg', 0),
(229, 'ANET OM4 5M LC-LC MM 50/125UM FIBER CORD', '4174530348', '**********', 'Server Room | 01', '-', '-', 36, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', '6HyZdCNUSz', 0),
(230, 'ANET LC-LC 10m LSZH OM3 Fiber Patch Cord 0.1D', '4174530348', '**********', 'Server Room | 01', '-', '-', 32, 'CLL', 'spare unit', 1, '', ' Mudzaffar Haniffuddin', 'tgrgPVMncR', 0),
(231, '1000BASE-T RJ45 100M Transceiver', '2070235736', '06', 'Server Room | 01', 'SFP-GE-T', 'FA2302080007', 1, 'CLL', 'Loan to Aaron Tay from Tech3', 1, '', ' Mudzaffar Haniffuddin', 'SYc7G52KLE', 0),
(232, 'Cisco 10GBASE-SR-S Transceiver', '**********', '06', 'Server Room | 01', 'SFP-10G-SR-S', 'ACW24531JJ1', 1, 'CLL', 'Loan to Wei Lip COMSTOR', 1, '', ' Mudzaffar Haniffuddin', 'QuAyFIh0p7', 0),
(233, 'Cisco 10GBASE-SR-S Transceiver', '**********', '06', 'Server Room | 01', 'SFP-10G-SR-S', 'ACW25031CNQ', 1, 'CLL', 'Loan to Wei Lip COMSTOR', 1, '', ' Mudzaffar Haniffuddin', '30TeuJUjV0', 0),
(234, 'Cisco 10GBASE-SR-S Transceiver', '**********', '06', 'Server Room | 01', 'SFP-10G-SR-S', 'ACW254531FNY', 1, 'CLL', 'Loan to Wei Lip COMSTOR', 1, '', ' Mudzaffar Haniffuddin', '5B6u831ajn', 0),
(235, 'Cisco 10GBASE-SR-S Transceiver', '**********', '06', 'Server Room | 01', 'SFP-10G-SR-S', 'ACW25031CNN', 1, 'CLL', 'Loan to Wei Lip COMSTOR', 1, '', ' Mudzaffar Haniffuddin', 'cpLoasF4XB', 0),
(236, 'CAT6 UTP cables 10m', 'Ld55e6-1L0', '**********', 'Server Room | 01', '-', '-', 10, 'CLL', '', 1, '', ' Mudzaffar Haniffuddin', 'rJ2LPgJEnp', 0),
(237, 'Cisco Catalyst 3850 12X 48 UPOE', '**********', '01', 'Server Room | 01', 'WS-C3850-12X48U-L', 'FOC1920U169', 1, 'Comstor', '', 1, 'Loan by Comstor', ' Mudzaffar Haniffuddin', 'E6KUKzkEcI', 0),
(238, 'Cisco Aironet 1832 Series 802.11ac Dual Band Access Point', '**********', '08', 'Server Room | 01', 'AIR-AP18321-C-K9', 'KWC193301QA', 1, 'Comstor', '', 1, 'Loan by Comstor', ' Mudzaffar Haniffuddin', 'Xb8RXMs7lS', 0),
(239, '5 Gang 3 Meter Socket Extension Plug (3250W)', '177042LLc0', '09', 'Server Room | 01', '-', '-', 0, 'CLL', 'Spare unit', 1, '', ' Mudzaffar Haniffuddin', 'RDPycEE1VM', 0),
(240, 'Palo Alto Networks PA-440 NFR unit', '0.70f2f-66', '10', 'Server Room | 01', '910-000212-00M', '021201147527', 1, 'CLL', '', 1, '', ' Mudzaffar Haniffuddin', 'PYZ3azVtha', 0),
(241, 'Aruba 25G SFP28 to SFP28 0.65m DAC Cable', '**********', '**********', 'Vespa Room | 01', 'JL487A', 'CN29L6S0NC', 1, 'Genting Malaysia', 'SO-004531\r\nCCTV - Aruba Switches', 1, '', ' Mudzaffar Haniffuddin', 'vs0bzycZqD', 0),
(242, 'Aruba 25G SFP28 to SFP28 0.65m DAC Cable', '**********', '**********', 'Vespa Room | 01', 'JL487A', 'CN29L6S0N7', 1, 'Genting Malaysia', 'SO-004531\r\nCCTV - Aruba Switches', 1, '', ' Mudzaffar Haniffuddin', 'NFD3nadhiK', 0),
(243, 'Cisco UCS 64108 108-Port Fabric Interconnect', '**********', '01', 'Server Room | 01', 'USCI-FI-64108', 'FD025080973', 1, 'Maybank', '', 1, '', ' Mudzaffar Haniffuddin', 'e78fVbwZ8h', 0),
(244, 'Cisco UCS 64108 108-Port Fabric Interconnect', '**********', '01', 'Server Room | 01', 'USCI-FI-64108', 'FD02508097J', 1, 'Maybank', '', 1, '', ' Mudzaffar Haniffuddin', '7VjkURlAb8', 0),
(245, 'HX220c M5', '**********', '11', 'Server Room | 01', '', 'WZP24510ESR', 1, 'CLL', '', 1, 'Rack #12', ' Mudzaffar Haniffuddin', 'Q3O7J2XxHG', 0),
(246, 'HX220c M5', '**********', '11', 'Server Room | 01', '', 'WZP24510ESY', 1, 'CLL', '', 1, 'Rack #13', ' Mudzaffar Haniffuddin', '9S9fPKv2fo', 0),
(247, 'HXAF 220c M5', '**********', '11', 'Server Room | 01', '', 'WZP244906RF', 1, 'CLL', '', 1, 'Rack #14', ' Mudzaffar Haniffuddin', 'QzzzipNhE3', 0),
(248, 'HXAF 220c M5', '**********', '11', 'Server Room | 01', '', 'WZP244906RH', 1, 'CLL', '', 1, 'Rack #15', ' Mudzaffar Haniffuddin', 'ke6gmPqJ5H', 0),
(249, 'USCI-FI-64108', '**********', '11', 'Server Room | 01', '', 'FD025080973', 1, 'CLL', '', 1, 'Rack #17-18', ' Mudzaffar Haniffuddin', 'xA3MX6XeOG', 0),
(250, 'USCI-FI-64108', '**********', '11', 'Server Room | 01', '', 'FD02508097J', 1, 'CLL', '', 1, 'Rack #20-21', ' Mudzaffar Haniffuddin', 'VXOa5smeG4', 0),
(251, 'HXAF 220c M5', '**********', '11', 'Server Room | 01', '', 'WZP244906QL', 1, 'CLL', '', 1, 'Rack #23', ' Mudzaffar Haniffuddin', 'mS7Hh4H0mI', 0),
(252, 'HXAF 220c M5', '**********', '11', 'Server Room | 01', '', 'WZP244906QJ', 1, 'CLL', '', 1, 'Rack #24', ' Mudzaffar Haniffuddin', 'JRQK5kHKUD', 0),
(253, 'ProLiant DL380 Gen10', '**********', '**********', 'Server Room | 01', '', 'SGH131SJZ3', 1, 'CLL', '', 1, 'Rack #25-26', ' Mudzaffar Haniffuddin', '47ZU9dSpI5', 0),
(254, 'ProLiant DL380 Gen10', '**********', '**********', 'Server Room | 01', '', 'SGH839Y9SH', 1, 'CLL', '', 1, 'Rack #27', ' Mudzaffar Haniffuddin', 'B46Ww1KBkM', 0),
(255, 'ProLiant DL380 Gen10', '**********', '**********', 'Server Room | 01', '', 'SGH839Y9SJ', 1, 'CLL', '', 1, 'Rack #28', ' Mudzaffar Haniffuddin', 'PPLIwyHFmS', 0),
(256, 'PowerEdge R620', '5021987615', '**********', 'Server Room | 01', '', '2PF3F2S', 1, 'CLL', '', 1, 'Rack #30', ' Mudzaffar Haniffuddin', 'hDTPeZGmNG', 0),
(257, 'N3048P POE Switch', '5021987615', '01', 'Server Room | 01', '', '', 1, 'CLL', '', 1, 'Rack #31', ' Mudzaffar Haniffuddin', 'VQZ8jHtcf9', 0),
(258, 'PA-820', '0.70f2f-66', '10', 'Server Room | 01', '', '12001034000', 1, 'CLL', '', 1, 'Rack #34', ' Mudzaffar Haniffuddin', 'mPL1OwMIfv', 0),
(259, 'NGAF M5300-F-I', 'L08969.913', '10', 'Server Room | 01', '', '5031502894', 1, 'CLL', '', 1, 'Rack #35', ' Mudzaffar Haniffuddin', 'O2zFg3diiG', 0),
(260, 'PowerEdge R640', '5021987615', '**********', 'Server Room | 01', '', '', 1, 'CLL', '', 1, 'Rack #17', ' Mudzaffar Haniffuddin', 'ta6b7KNfVJ', 0),
(261, 'PowerEdge R640', '5021987615', '**********', 'Server Room | 01', '', '', 1, 'CLL', '', 1, 'Rack #18', ' Mudzaffar Haniffuddin', '1fFLiwVHj9', 0),
(262, 'PowerEdge R640', '5021987615', '**********', 'Server Room | 01', '', '', 1, 'CLL', '', 1, 'Rack #19', ' Mudzaffar Haniffuddin', '2JgK528vXk', 0),
(263, 'NX-3460-G5-2650v4', '28620Lc973', '**********', 'Server Room | 01', '', '17SM6B020110', 1, 'CLL', '', 1, 'Rack #21-22', ' Mudzaffar Haniffuddin', 'oWYZmEDaW8', 0),
(264, 'FortiGate 200E', '0244180542', '10', 'Server Room | 01', '', 'FG200ETK18901305', 1, 'CLL', '', 1, 'Rack #34', ' Mudzaffar Haniffuddin', 'wKbbREVYBE', 0),
(265, 'FortiSwitch 108D-POE', '0244180542', '01', 'Server Room | 01', '', 'FS108D3W14000794', 1, 'CLL', '', 1, 'Rack #35 (not use)', ' Mudzaffar Haniffuddin', 'ay8CKVuKyT', 0),
(266, 'S5024PV3-EI-PWR', 'L128.5CL2c', '**********', 'Server Room | 01', '', '219801A1QF921AQ00033', 1, 'CLL', '', 1, 'Rack #37', ' Mudzaffar Haniffuddin', 'mef141gUzh', 0),
(267, 'x1026', '5021987615', '01', 'Server Room | 01', '', 'G3911Y24', 1, 'CLL', '', 1, '', ' Mudzaffar Haniffuddin', '45DNhZPoCE', 0),
(268, 'BCM 5719 1Gb 4p BASE-T OCP Adaptr', '**********', '12', 'Server Room | 01', 'P51181-B21', 'VNM410050K', 1, 'CLL', 'Previous use for C&F (SO-005340)', 1, 'SO-005340', ' Mudzaffar Haniffuddin', 'UEmCbKvMEx', 0),
(269, 'BCM 5719 1Gb 4p BASE-T OCP Adaptr', '**********', '12', 'Server Room | 01', 'P51181-B21', 'VNM4100532', 1, 'CLL', 'Previous use for C&F (SO-005340)', 1, 'SO-005340', ' Mudzaffar Haniffuddin', '6ORDNYzqkt', 0),
(270, 'BCM 5719 1Gb 4p BASE-T OCP Adaptr', '**********', '12', 'Server Room | 01', 'P51181-B21', 'VNM4100527', 1, 'CLL', 'Previous use for C&F (SO-005340)', 1, 'SO-005340', ' Mudzaffar Haniffuddin', 'oXuFBZjEeI', 0),
(271, 'BCM 5719 1Gb 4p BASE-T OCP Adaptr', '**********', '12', 'Server Room | 01', 'P51181-B21', 'VNM41006NN', 1, 'CLL', 'Previous use for C&F (SO-005340)', 1, 'SO-005340', ' Mudzaffar Haniffuddin', 'apSMzP74iH', 0);

-- --------------------------------------------------------

--
-- Table structure for table `itsupportinq_list`
--

CREATE TABLE `itsupportinq_list` (
  `id` int(11) NOT NULL,
  `itsupport_inq_id` varchar(50) NOT NULL,
  `itsupport_inq` varchar(255) NOT NULL,
  `des` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `itsupportinq_list`
--

INSERT INTO `itsupportinq_list` (`id`, `itsupport_inq_id`, `itsupport_inq`, `des`) VALUES
(1, 'ITS01', 'Hardware', ''),
(2, 'ITS02', 'Software', '');

-- --------------------------------------------------------

--
-- Table structure for table `itsupport_category_list`
--

CREATE TABLE `itsupport_category_list` (
  `id` int(11) NOT NULL,
  `itsupport_inq_id` varchar(50) NOT NULL,
  `itsupport_category_id` varchar(50) NOT NULL,
  `itsupport_category` varchar(255) NOT NULL,
  `des` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `itsupport_category_list`
--

INSERT INTO `itsupport_category_list` (`id`, `itsupport_inq_id`, `itsupport_category_id`, `itsupport_category`, `des`) VALUES
(1, 'ITS01', '61C47L16ed', 'Hardware1', ''),
(2, 'ITS02', '5d-d68675L', 'Software1', ''),
(3, 'ITS01', '0d66-06612', 'Other', ''),
(4, 'ITS02', '67c3366aL1', 'Other', '');

-- --------------------------------------------------------

--
-- Table structure for table `itsupport_list`
--

CREATE TABLE `itsupport_list` (
  `id` int(255) NOT NULL,
  `inq_id` varchar(255) NOT NULL,
  `itsupport_inq_id` varchar(255) NOT NULL,
  `itsupport_category_id` varchar(255) NOT NULL,
  `brand_id` varchar(255) NOT NULL,
  `des` text NOT NULL,
  `remarks` text NOT NULL,
  `image` varchar(255) DEFAULT NULL,
  `acc_id` varchar(255) NOT NULL,
  `date_submitted` datetime DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `itsupport_list`
--

INSERT INTO `itsupport_list` (`id`, `inq_id`, `itsupport_inq_id`, `itsupport_category_id`, `brand_id`, `des`, `remarks`, `image`, `acc_id`, `date_submitted`) VALUES
(19, '0Oa5kdyuqt', 'ITS02', '67c3366aL1', '2070235736', '[Testing] Format PC request for new joiner', '', NULL, 'snyajRZeBK', '2024-07-09 02:47:32');

-- --------------------------------------------------------

--
-- Table structure for table `location-list`
--

CREATE TABLE `location-list` (
  `id` int(255) NOT NULL,
  `room` varchar(255) NOT NULL,
  `rack` varchar(255) NOT NULL,
  `des` varchar(255) NOT NULL,
  `created` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `location-list`
--

INSERT INTO `location-list` (`id`, `room`, `rack`, `des`, `created`) VALUES
(1, 'Vespa Room', '01', '', 'Mudzaffar Haniffuddin'),
(2, 'Server Room', '01', '', 'Mudzaffar Haniffuddin'),
(3, '001', '03', 'CSV import added this Location ID', 'Alex Ang'),
(4, '001', '04', 'CSV import added this Location ID', 'Alex Ang'),
(5, '001', '05', 'CSV import added this Location ID', 'Alex Ang'),
(6, '001', '06', 'CSV import added this Location ID', 'Alex Ang'),
(15, 'Server Room', '01', 'CSV import added this Location ID', ' Mudzaffar Haniffuddin'),
(16, 'Vespa Room', '01', 'CSV import added this Location ID', ' Mudzaffar Haniffuddin'),
(17, 'Server Room', '02', 'CSV import added this Location ID', ' Mudzaffar Haniffuddin'),
(18, 'Server Room', '03', 'CSV import added this Location ID', ' Mudzaffar Haniffuddin');

-- --------------------------------------------------------

--
-- Table structure for table `notification`
--

CREATE TABLE `notification` (
  `id` int(255) NOT NULL,
  `acc_id` varchar(255) NOT NULL,
  `request_id` varchar(255) NOT NULL,
  `timestamp` varchar(255) NOT NULL,
  `read_msg` int(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `notification`
--

INSERT INTO `notification` (`id`, `acc_id`, `request_id`, `timestamp`, `read_msg`) VALUES
(1, 'ylmh96zgPV', 'p1sSdcbkrZ', '2024-03-14 11:15:12', 1),
(2, 'ylmh96zgPV', 'CLDbrfYWE3', '2024-03-14 12:03:11', 1),
(3, 'ylmh96zgPV', 'QMbxwyRAyE', '2024-03-14 15:43:48', 1),
(5, 'ylmh96zgPV', 'hHszfptVR2', '2024-03-15 16:15:47', 1),
(6, 'ylmh96zgPV', 'Jon4VbBmeR', '2024-03-18 15:56:17', 1),
(7, 'ylmh96zgPV', '5ceqkNi469', '2024-04-18 14:37:46', 1),
(8, 'ylmh96zgPV', '1yggG8RSea', '2024-04-22 09:31:46', 1),
(9, 'ylmh96zgPV', 'adQ42KgZmp', '2024-04-25 15:32:41', 1),
(10, 'ylmh96zgPV', 'waxbvvMFjI', '2024-04-30 12:07:19', 1);

-- --------------------------------------------------------

--
-- Table structure for table `office_area_list`
--

CREATE TABLE `office_area_list` (
  `id` int(11) NOT NULL,
  `office_id` varchar(50) NOT NULL,
  `office_area_id` varchar(50) NOT NULL,
  `office_area` varchar(255) NOT NULL,
  `des` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `office_area_list`
--

INSERT INTO `office_area_list` (`id`, `office_id`, `office_area_id`, `office_area`, `des`) VALUES
(1, 'OFF01', '2.5b5-9506', 'Room', ''),
(2, 'OFF01', 'Lb716Ld196', 'Toilet', ''),
(3, 'OFF01', 'LcC25754-7', 'Office Area 1 -  Engineer', ''),
(4, 'OFF01', '696.C325L7', 'Office Area 2  -  Management, HR, Finance, Sales', ''),
(5, 'OFF01', '6-99L65c4a', 'Other', '');

-- --------------------------------------------------------

--
-- Table structure for table `office_category_list`
--

CREATE TABLE `office_category_list` (
  `id` int(11) NOT NULL,
  `office_id` varchar(50) NOT NULL,
  `office_area_id` varchar(50) NOT NULL,
  `office_category_id` varchar(50) NOT NULL,
  `office_category` varchar(255) NOT NULL,
  `des` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `office_category_list`
--

INSERT INTO `office_category_list` (`id`, `office_id`, `office_area_id`, `office_category_id`, `office_category`, `des`) VALUES
(1, 'OFF01', '2.5b5-9506', 'L6-L353536', 'Buggati Room', ''),
(2, 'OFF01', '2.5b5-9506', 'C5046c5L67', 'Ducati Room', ''),
(3, 'OFF01', '2.5b5-9506', '725b334L68', 'Pagani Zonda Room', ''),
(4, 'OFF01', '2.5b5-9506', '95f667-dc4', 'Vespa Room', ''),
(5, 'OFF01', '2.5b5-9506', '4998566.17', 'Server Room', ''),
(6, 'OFF01', '2.5b5-9506', '6261.361e2', 'Kar Wai Room', ''),
(7, 'OFF01', '2.5b5-9506', '5e9d543ac1', 'Jason Room', ''),
(8, 'OFF01', 'Lb716Ld196', '29d516-L67', 'Toilet Male', ''),
(9, 'OFF01', 'Lb716Ld196', '787L7aec66', 'Toilet Female', ''),
(10, 'OFF01', 'LcC25754-7', '6cdca508La', 'Engineer', ''),
(11, 'OFF01', '696.C325L7', '-dcL2969.4', 'Management', ''),
(12, 'OFF01', '696.C325L7', '7327454bc6', 'HR', ''),
(13, 'OFF01', '696.C325L7', 'ec.a66cd-C', 'Finance', ''),
(14, 'OFF01', '696.C325L7', '2L6200d6Ce', 'Sales', ''),
(15, 'OFF01', '6-99L65c4a', '62cLL96d.9', 'Other', '');

-- --------------------------------------------------------

--
-- Table structure for table `office_list`
--

CREATE TABLE `office_list` (
  `id` int(255) NOT NULL,
  `office_id` varchar(255) NOT NULL,
  `office_name` varchar(255) NOT NULL,
  `des` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `office_list`
--

INSERT INTO `office_list` (`id`, `office_id`, `office_name`, `des`) VALUES
(1, 'OFF01', 'CLL Systems Sdn Bhd', ''),
(2, 'OFF02', 'Axperien Sdn Bhd', '');

-- --------------------------------------------------------

--
-- Table structure for table `procurement_list`
--

CREATE TABLE `procurement_list` (
  `id` int(255) NOT NULL,
  `inq_id` varchar(255) NOT NULL,
  `inquiry` text NOT NULL,
  `remarks` text NOT NULL,
  `image` varchar(255) DEFAULT NULL,
  `acc_id` varchar(255) NOT NULL,
  `date_submitted` datetime DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `procurement_list`
--

INSERT INTO `procurement_list` (`id`, `inq_id`, `inquiry`, `remarks`, `image`, `acc_id`, `date_submitted`) VALUES
(10, '3x7TxVc8SV', 'Software Testing', 'Testing out new features', 'uploads/pexels-adrien-olichon-1257089-3709402.jpg', 'GMqrpu1kC5', '2024-07-09 03:29:27');

-- --------------------------------------------------------

--
-- Table structure for table `request-list`
--

CREATE TABLE `request-list` (
  `id` int(255) NOT NULL,
  `request-id` varchar(255) NOT NULL,
  `inventory-id` varchar(255) NOT NULL,
  `acc_id` varchar(255) NOT NULL,
  `company_name` varchar(255) NOT NULL,
  `company_category` varchar(255) NOT NULL,
  `request_date` varchar(255) NOT NULL,
  `approval_date` varchar(255) NOT NULL,
  `collection_date` varchar(255) NOT NULL,
  `return_date` varchar(255) NOT NULL,
  `quantity` int(255) NOT NULL,
  `approval_status` int(255) NOT NULL,
  `approval_remark` varchar(255) NOT NULL,
  `request_status` int(255) NOT NULL,
  `request_remark` varchar(255) NOT NULL,
  `inventory_status` int(255) NOT NULL,
  `inventory_remark` varchar(255) NOT NULL,
  `approval_made_by` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `request-list`
--

INSERT INTO `request-list` (`id`, `request-id`, `inventory-id`, `acc_id`, `company_name`, `company_category`, `request_date`, `approval_date`, `collection_date`, `return_date`, `quantity`, `approval_status`, `approval_remark`, `request_status`, `request_remark`, `inventory_status`, `inventory_remark`, `approval_made_by`) VALUES
(1, 'p1sSdcbkrZ', '9r5jA1ZHU1', 'ylmh96zgPV', 'Comstor', 'IT Distributor', '2024-03-14 11:15:12', '2024-03-25 14:51:16', '2024-03-14 11:17:20', '2024-03-25 14:51:16', 1, 1, '', 0, 'loan to Wei Lip COMSTOR', 3, 'Returned on 25/03/2024', '<EMAIL>'),
(2, 'p1sSdcbkrZ', '7DEHEZRQ3a', 'ylmh96zgPV', 'Comstor', 'IT Distributor', '2024-03-14 11:15:12', '2024-03-25 14:51:16', '2024-03-14 11:17:20', '2024-03-25 14:51:16', 1, 1, '', 0, 'loan to Wei Lip COMSTOR', 3, 'Returned on 25/03/2024', '<EMAIL>'),
(3, 'CLDbrfYWE3', 'SYc7G52KLE', 'ylmh96zgPV', 'Tech3 Solutions Sdn Bhd', 'IT Distributor', '2024-03-14 12:03:11', '2024-03-29 11:17:03', '2024-03-14 12:03:42', '2024-03-29 11:17:03', 1, 1, '', 0, 'Collected by Wong Kean Chung', 3, 'Returned on 27/03/2024', '<EMAIL>'),
(4, 'QMbxwyRAyE', 'MiWrElrvHg', 'ylmh96zgPV', 'Hino Motors Sales (Malaysia) Sdn Bhd', 'Manufacturer', '2024-03-14 15:43:48', '2024-03-14 15:45:00', '2024-03-14 15:45:00', '14-03-2024', 8, 1, '', 1, 'SO-005159 - Collected by Roy Lai', 2, '', '<EMAIL>'),
(5, 'QMbxwyRAyE', 'KzUTIX5SQG', 'ylmh96zgPV', 'Hino Motors Sales (Malaysia) Sdn Bhd', 'Manufacturer', '2024-03-14 15:43:48', '2024-03-14 15:45:00', '2024-03-14 15:45:00', '14-03-2024', 3, 1, '', 1, 'SO-005159 - Collected by Roy Lai', 2, '', '<EMAIL>'),
(7, 'hHszfptVR2', 'uOa9Ro6h4S', 'ylmh96zgPV', 'Elsa Energy Sdn Bhd (SOMV)', 'Digital Solution', '2024-03-15 16:15:47', '2024-03-18 15:54:34', '', '18-03-2024', 4, 2, 'change to 4 units of 10m UTP cable', 1, 'SO-005108 - collect by Hakimi', 0, '', '<EMAIL>'),
(8, 'Jon4VbBmeR', 'fmOlpqzVbR', 'ylmh96zgPV', 'Elsa Energy Sdn Bhd (SOMV)', 'Digital Solution', '2024-03-18 15:56:17', '2024-03-18 15:57:04', '2024-03-18 15:57:04', '18-03-2024', 4, 1, '', 1, 'SO-005108 - collect by Hakimi', 2, '', '<EMAIL>'),
(9, '5ceqkNi469', 'R3hTyPjP9a', 'ylmh96zgPV', 'Eco-Shop Marketing Sdn. Bhd.', 'Retail', '2024-04-18 14:37:46', '2024-04-18 14:38:40', '2024-04-18 14:38:40', '08-04-2024', 3, 1, '', 1, 'SO-005292 (JTC) - collected by Jack S Team', 2, '', '<EMAIL>'),
(10, '1yggG8RSea', 'RDPycEE1VM', 'ylmh96zgPV', 'Maybank Shared Services Sdn Bhd', 'Financial', '2024-04-22 09:31:46', '2024-04-22 09:32:26', '2024-04-22 09:32:26', '20-05-2024', 5, 1, '', 0, 'SO-005212 - loan to Zulhilmi', 2, '', '<EMAIL>'),
(11, 'adQ42KgZmp', 'q7B4BYePQK', 'ylmh96zgPV', 'Securemetric Technology Sdn Bhd', 'IT Company', '2024-04-25 15:32:41', '2024-04-25 15:34:24', '2024-04-25 15:34:24', '25-04-2024', 8, 1, '', 1, 'SO-005331 - collected by Alan Ang', 2, '', '<EMAIL>'),
(12, 'adQ42KgZmp', 'q7B4BYePQK', 'ylmh96zgPV', 'Securemetric Technology Sdn Bhd', 'IT Company', '2024-04-25 15:32:41', '2024-04-25 15:34:24', '2024-04-25 15:34:24', '25-04-2024', 6, 1, '', 1, 'SO-005331 - collected by Alan Ang', 2, '', '<EMAIL>'),
(13, 'waxbvvMFjI', 'R3hTyPjP9a', 'ylmh96zgPV', 'Securemetric Technology Sdn Bhd', 'IT Company', '2024-04-30 12:07:19', '2024-04-30 12:07:50', '2024-04-30 12:07:50', '30-04-2024', 4, 1, '', 1, 'SO-005330 - collected by Robin', 2, '', '<EMAIL>');

-- --------------------------------------------------------

--
-- Table structure for table `role-list`
--

CREATE TABLE `role-list` (
  `id` int(255) NOT NULL,
  `role-id` varchar(255) NOT NULL,
  `role` varchar(255) NOT NULL,
  `role_description` varchar(255) NOT NULL,
  `inventory_view` int(255) NOT NULL,
  `inventory_create` int(255) NOT NULL,
  `inventory_edit` int(255) NOT NULL,
  `inventory_delete` int(255) NOT NULL,
  `inventory_all` int(255) NOT NULL,
  `import_create` int(255) NOT NULL,
  `category_view` int(255) NOT NULL,
  `category_create` int(255) NOT NULL,
  `category_edit` int(255) NOT NULL,
  `category_delete` int(255) NOT NULL,
  `category_all` int(255) NOT NULL,
  `brand_view` int(255) NOT NULL,
  `brand_create` int(255) NOT NULL,
  `brand_edit` int(255) NOT NULL,
  `brand_delete` int(255) NOT NULL,
  `brand_all` int(255) NOT NULL,
  `location_view` int(255) NOT NULL,
  `location_create` int(255) NOT NULL,
  `location_edit` int(255) NOT NULL,
  `location_delete` int(255) NOT NULL,
  `location_all` int(255) NOT NULL,
  `request_view` int(255) NOT NULL,
  `request_create` int(255) NOT NULL,
  `request_approval` int(255) NOT NULL,
  `request_all` int(255) NOT NULL,
  `inventory_return_view` int(255) NOT NULL,
  `inventory_return_edit` int(255) NOT NULL,
  `inventory_return_delete` int(255) NOT NULL,
  `inventory_return_all` int(255) NOT NULL,
  `user_view` int(255) NOT NULL,
  `user_create` int(255) NOT NULL,
  `user_edit` int(255) NOT NULL,
  `user_ban` int(255) NOT NULL,
  `user_delete` int(255) NOT NULL,
  `user_all` int(255) NOT NULL,
  `group_view` int(255) NOT NULL,
  `group_create` int(255) NOT NULL,
  `group_edit` int(255) NOT NULL,
  `group_delete` int(255) NOT NULL,
  `group_all` int(255) NOT NULL,
  `hide` int(255) NOT NULL,
  `request_smtp` int(255) NOT NULL,
  `backup` int(255) NOT NULL,
  `user` tinyint(1) DEFAULT 0,
  `hr` tinyint(1) DEFAULT 0,
  `finance` tinyint(1) DEFAULT 0,
  `it_support` tinyint(1) DEFAULT 0,
  `anonymous` tinyint(1) DEFAULT 0,
  `section_all` tinyint(1) DEFAULT 0,
  `ticket_all` tinyint(1) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `role-list`
--

INSERT INTO `role-list` (`id`, `role-id`, `role`, `role_description`, `inventory_view`, `inventory_create`, `inventory_edit`, `inventory_delete`, `inventory_all`, `import_create`, `category_view`, `category_create`, `category_edit`, `category_delete`, `category_all`, `brand_view`, `brand_create`, `brand_edit`, `brand_delete`, `brand_all`, `location_view`, `location_create`, `location_edit`, `location_delete`, `location_all`, `request_view`, `request_create`, `request_approval`, `request_all`, `inventory_return_view`, `inventory_return_edit`, `inventory_return_delete`, `inventory_return_all`, `user_view`, `user_create`, `user_edit`, `user_ban`, `user_delete`, `user_all`, `group_view`, `group_create`, `group_edit`, `group_delete`, `group_all`, `hide`, `request_smtp`, `backup`, `user`, `hr`, `finance`, `it_support`, `anonymous`, `section_all`, `ticket_all`) VALUES
(1, 'x9Mj5GmOGz', 'Admin', 'Admin', 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0),
(2, 'UzZhqYA3OJ', 'User', 'Normal User', 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0),
(3, 'xAFBxurItf', 'Procurement', 'Procurement will receive an email notification when there is a request from an end user.', 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0),
(5, '9QX9sqLyKQ', 'Master Admin', 'Master Admin - Receive Backup', 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 1, 1),
(6, 'JtIrAE0MCZ', 'HR', 'HR Department', 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 1, 0, 1),
(7, 'iP3RQrZt1C', 'Finance', 'Finance Department', 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1),
(8, 'kJNT9dJynu', 'IT Support', 'Support Team', 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1);

-- --------------------------------------------------------

--
-- Table structure for table `sections`
--

CREATE TABLE `sections` (
  `id` int(255) NOT NULL,
  `inq_id` varchar(255) NOT NULL,
  `section_name` varchar(255) NOT NULL,
  `acc_id` varchar(255) NOT NULL,
  `status` enum('New Ticket','In Progress','Rejected','Completed','Withdrawn') NOT NULL,
  `priority_level` varchar(255) DEFAULT NULL,
  `date_submitted` datetime DEFAULT current_timestamp(),
  `involved_hr` tinyint(1) DEFAULT 0,
  `involved_finance` tinyint(1) DEFAULT 0,
  `involved_it_support` tinyint(1) DEFAULT 0,
  `involved_anonymous` tinyint(1) DEFAULT 0,
  `comments` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `sections`
--

INSERT INTO `sections` (`id`, `inq_id`, `section_name`, `acc_id`, `status`, `priority_level`, `date_submitted`, `involved_hr`, `involved_finance`, `involved_it_support`, `involved_anonymous`, `comments`) VALUES
(187, 'o4EaAw997J', 'Assets Faulty', 'Wwc27y3ybR', 'New Ticket', 'Medium', '2024-07-09 01:38:24', 1, 0, 0, 0, NULL),
(188, 'XMGpIRwu02', 'Assets Faulty', 'Wwc27y3ybR', 'New Ticket', 'High', '2024-07-09 01:40:38', 1, 0, 0, 0, NULL),
(189, 'eVNiBOa3kI', 'Anonymous Inquiry', '771a1c26b3', 'Completed', '-', '2024-07-09 01:44:18', 1, 0, 0, 1, 'halo'),
(190, 'rFU1Kng4lL', 'Assets Faulty', 'snyajRZeBK', 'Withdrawn', 'High', '2024-07-09 02:30:31', 1, 0, 0, 0, NULL),
(191, '0Oa5kdyuqt', 'IT Support', 'snyajRZeBK', 'Completed', 'Medium', '2024-07-09 02:47:32', 0, 0, 1, 0, ''),
(192, 'FSJkXFzxkC', 'Finance', 'snyajRZeBK', 'New Ticket', 'Medium', '2024-07-09 03:09:58', 0, 1, 0, 0, NULL),
(193, 'wPejCWl1YE', 'Anonymous Inquiry', '3f11b3d5b7', 'Rejected', '-', '2024-07-09 03:22:49', 0, 0, 0, 1, 'no love in CLL'),
(194, 'MiuJ09zHAb', 'Anonymous Inquiry', '3f11b3d5b7', 'New Ticket', '-', '2024-07-09 03:28:14', 0, 0, 0, 1, NULL),
(195, '3x7TxVc8SV', 'Procurement', 'GMqrpu1kC5', 'New Ticket', 'Medium', '2024-07-09 03:29:27', 0, 1, 0, 0, NULL),
(196, 'd07ewGS0Up', 'Assets Faulty', 'GMqrpu1kC5', 'Rejected', 'Medium', '2024-07-09 03:32:17', 1, 0, 0, 0, 'PLS bring more numbers to CLL, then we change monitor for you'),
(197, 'YisehBwydR', 'Anonymous Inquiry', 'de1f7516b0', 'In Progress', '-', '2024-07-09 21:38:53', 1, 0, 0, 1, 'Okay.. '),
(198, '8wBmG6uN7g', 'Anonymous Inquiry', '771a1c26b3', 'Withdrawn', '-', '2024-07-18 00:49:40', 0, 0, 0, 1, NULL),
(199, 'RpUEst7XSC', 'Assets Faulty', 'Wwc27y3ybR', 'Withdrawn', 'High', '2024-07-30 01:26:33', 1, 0, 0, 0, NULL),
(200, '6yRTN7sdNr', 'Assets Faulty', 'Wwc27y3ybR', 'In Progress', 'Medium', '2024-07-30 01:31:28', 1, 0, 0, 0, ''),
(201, 'yVQ2AhldU3', 'Assets Faulty', 'ylmh96zgPV', 'New Ticket', 'High', '2024-08-09 02:17:16', 1, 0, 0, 0, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `user-list`
--

CREATE TABLE `user-list` (
  `id` int(255) NOT NULL,
  `acc_id` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `designation` varchar(255) NOT NULL,
  `department` varchar(255) NOT NULL,
  `profile_image` varchar(255) DEFAULT NULL,
  `description` varchar(999) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `user-list`
--

INSERT INTO `user-list` (`id`, `acc_id`, `email`, `name`, `designation`, `department`, `profile_image`, `description`) VALUES
(1, 'cIvf72h28P', '<EMAIL>', 'Alex Ang', 'Software Engineer', 'Software Team', 'profile-image/Unknown_person.jpg', ''),
(2, 'LFuUInMStl', '<EMAIL>', 'Firdaus Baharin', 'Service Delivery Manager', 'Technical Department', NULL, ''),
(3, 'snyajRZeBK', '<EMAIL>', 'Alan Ang', 'Lead, Services Delivery', 'Technical Department', NULL, ''),
(4, 'ed0OIAZ8ne', '<EMAIL>', 'Jason Lim', 'Director, Technical', 'Technical Department', NULL, ''),
(5, 'lOoY8fXCUX', '<EMAIL>', 'KK Chong', 'Software Sales Director', 'Software Team', NULL, ''),
(6, 'ylmh96zgPV', '<EMAIL>', ' Mudzaffar Haniffuddin', 'Senior Procurement Executive', 'Finance Department', NULL, ''),
(8, 'JB4McW0Yvm', '<EMAIL>', 'Anthony Yen', 'Lead, Customer Success', 'Technical Department', NULL, ''),
(9, 'w19klnNQ30', '<EMAIL>', 'Robin Chong', 'Project Manager', 'Technical Department', NULL, ''),
(10, 'Cxjao6ZTVm', '<EMAIL>', 'Nur Mutmainnah', 'Admin cum Procurement Executive', 'Finance Department', NULL, ''),
(11, 'Wwc27y3ybR', '<EMAIL>', 'Ernest Wong', 'Software Engineer', 'Software Team', NULL, ''),
(12, 'cPQodCOVki', '<EMAIL>', 'Jessica Pah', 'HR', 'Admin Department', NULL, ''),
(13, 'jLqlRKUGJu', '<EMAIL>', 'Crystal Yap', 'HR & Admin Executive', 'Admin Department', NULL, ''),
(14, 'OqI8SLBkZm', '<EMAIL>', 'Tan Si Min', 'Finance', 'Finance Department', NULL, ''),
(15, 'e7Nt7drD3D', '<EMAIL>', 'Muhammad Haziq Fathi', 'Associate Systems Engineer', 'Technical Department', NULL, ''),
(16, 'GMqrpu1kC5', '<EMAIL>', 'Yip Fay', 'Software Engineer', 'Software Team', NULL, ''),
(17, 'OKBYoQVBBI', '<EMAIL>', 'ZAIM HAZWAN BIN ZAINAL', 'Business Analyst', 'Software Team', NULL, ''),
(18, 'KYMjdaA0h2', '<EMAIL>', 'syamie', 'Software Developer', 'Software Team', NULL, ''),
(19, 'JjL56oPDQQ', '<EMAIL>', 'evan', 'Software Engineer', 'Software Team', NULL, '');

-- --------------------------------------------------------

--
-- Table structure for table `useracc`
--

CREATE TABLE `useracc` (
  `id` int(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `acc_id` varchar(255) NOT NULL,
  `token` varchar(255) NOT NULL,
  `otp` varchar(255) NOT NULL,
  `expired` varchar(255) NOT NULL,
  `ban` int(255) NOT NULL,
  `attempt` int(255) NOT NULL,
  `role_id` varchar(255) NOT NULL,
  `verification_token` varchar(64) NOT NULL,
  `verified` tinyint(1) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `useracc`
--

INSERT INTO `useracc` (`id`, `email`, `acc_id`, `token`, `otp`, `expired`, `ban`, `attempt`, `role_id`, `verification_token`, `verified`) VALUES
(1, '<EMAIL>', 'cIvf72h28P', '', '', '', 0, 0, '9QX9sqLyKQ', '', 1),
(2, '<EMAIL>', 'LFuUInMStl', '', '', '', 0, 0, 'UzZhqYA3OJ', '', 1),
(3, '<EMAIL>', 'snyajRZeBK', '', '', '', 0, 0, 'UzZhqYA3OJ', '', 1),
(4, '<EMAIL>', 'ed0OIAZ8ne', '', '', '', 0, 0, 'UzZhqYA3OJ', '', 1),
(5, '<EMAIL>', 'lOoY8fXCUX', '', '', '', 0, 0, 'UzZhqYA3OJ', '', 1),
(6, '<EMAIL>', 'ylmh96zgPV', '', '', '', 0, 0, 'UzZhqYA3OJ', '', 1),
(8, '<EMAIL>', 'JB4McW0Yvm', '', '', '', 0, 0, 'UzZhqYA3OJ', '', 1),
(9, '<EMAIL>', 'w19klnNQ30', '', '', '', 0, 0, 'UzZhqYA3OJ', '', 1),
(10, '<EMAIL>', 'Cxjao6ZTVm', '', '', '', 0, 0, 'UzZhqYA3OJ', '', 1),
(11, '<EMAIL>', 'Wwc27y3ybR', '', '', '', 0, 0, '9QX9sqLyKQ', '', 1),
(12, '<EMAIL>', 'cPQodCOVki', '', '', '', 0, 0, 'JtIrAE0MCZ', '', 1),
(13, '<EMAIL>', 'jLqlRKUGJu', '', '', '', 0, 0, 'JtIrAE0MCZ', '', 1),
(14, '<EMAIL>', 'OqI8SLBkZm', '', '', '', 0, 0, 'iP3RQrZt1C', '', 1),
(15, '<EMAIL>', 'e7Nt7drD3D', '', '', '', 0, 0, 'kJNT9dJynu', '', 1),
(16, '<EMAIL>', 'GMqrpu1kC5', '', '', '', 0, 0, 'UzZhqYA3OJ', '', 1),
(17, '<EMAIL>', 'OKBYoQVBBI', '', '', '', 0, 0, 'UzZhqYA3OJ', '', 1),
(18, '<EMAIL>', 'KYMjdaA0h2', '', '', '', 0, 0, 'UzZhqYA3OJ', '', 1),
(19, '<EMAIL>', 'JjL56oPDQQ', '', '', '', 0, 0, '9QX9sqLyKQ', '', 1);

--
-- Indexes for dumped tables
--

--
-- Indexes for table `anonymous_inquiry_list`
--
ALTER TABLE `anonymous_inquiry_list`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `assets_faulty_list`
--
ALTER TABLE `assets_faulty_list`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `brand-list`
--
ALTER TABLE `brand-list`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `category-list`
--
ALTER TABLE `category-list`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `fault_category_list`
--
ALTER TABLE `fault_category_list`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `financeinq_list`
--
ALTER TABLE `financeinq_list`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `finance_list`
--
ALTER TABLE `finance_list`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `hrinq_list`
--
ALTER TABLE `hrinq_list`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `hr_list`
--
ALTER TABLE `hr_list`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `inventory-list`
--
ALTER TABLE `inventory-list`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `itsupportinq_list`
--
ALTER TABLE `itsupportinq_list`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `itsupport_inq_id` (`itsupport_inq_id`);

--
-- Indexes for table `itsupport_category_list`
--
ALTER TABLE `itsupport_category_list`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `itsupport_list`
--
ALTER TABLE `itsupport_list`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `location-list`
--
ALTER TABLE `location-list`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `notification`
--
ALTER TABLE `notification`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `office_area_list`
--
ALTER TABLE `office_area_list`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `office_area_id` (`office_area_id`);

--
-- Indexes for table `office_category_list`
--
ALTER TABLE `office_category_list`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `office_list`
--
ALTER TABLE `office_list`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `procurement_list`
--
ALTER TABLE `procurement_list`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `request-list`
--
ALTER TABLE `request-list`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `role-list`
--
ALTER TABLE `role-list`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `sections`
--
ALTER TABLE `sections`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `user-list`
--
ALTER TABLE `user-list`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `useracc`
--
ALTER TABLE `useracc`
  ADD PRIMARY KEY (`id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `anonymous_inquiry_list`
--
ALTER TABLE `anonymous_inquiry_list`
  MODIFY `id` int(255) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=101;

--
-- AUTO_INCREMENT for table `assets_faulty_list`
--
ALTER TABLE `assets_faulty_list`
  MODIFY `id` int(255) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=129;

--
-- AUTO_INCREMENT for table `brand-list`
--
ALTER TABLE `brand-list`
  MODIFY `id` int(255) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=24;

--
-- AUTO_INCREMENT for table `category-list`
--
ALTER TABLE `category-list`
  MODIFY `id` int(255) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=14;

--
-- AUTO_INCREMENT for table `fault_category_list`
--
ALTER TABLE `fault_category_list`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=17;

--
-- AUTO_INCREMENT for table `financeinq_list`
--
ALTER TABLE `financeinq_list`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `finance_list`
--
ALTER TABLE `finance_list`
  MODIFY `id` int(255) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=83;

--
-- AUTO_INCREMENT for table `hr_list`
--
ALTER TABLE `hr_list`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=16;

--
-- AUTO_INCREMENT for table `inventory-list`
--
ALTER TABLE `inventory-list`
  MODIFY `id` int(255) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=272;

--
-- AUTO_INCREMENT for table `itsupportinq_list`
--
ALTER TABLE `itsupportinq_list`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `itsupport_category_list`
--
ALTER TABLE `itsupport_category_list`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `itsupport_list`
--
ALTER TABLE `itsupport_list`
  MODIFY `id` int(255) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=20;

--
-- AUTO_INCREMENT for table `location-list`
--
ALTER TABLE `location-list`
  MODIFY `id` int(255) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=19;

--
-- AUTO_INCREMENT for table `notification`
--
ALTER TABLE `notification`
  MODIFY `id` int(255) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `office_area_list`
--
ALTER TABLE `office_area_list`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `office_category_list`
--
ALTER TABLE `office_category_list`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=16;

--
-- AUTO_INCREMENT for table `office_list`
--
ALTER TABLE `office_list`
  MODIFY `id` int(255) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `procurement_list`
--
ALTER TABLE `procurement_list`
  MODIFY `id` int(255) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `request-list`
--
ALTER TABLE `request-list`
  MODIFY `id` int(255) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=14;

--
-- AUTO_INCREMENT for table `role-list`
--
ALTER TABLE `role-list`
  MODIFY `id` int(255) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `sections`
--
ALTER TABLE `sections`
  MODIFY `id` int(255) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=202;

--
-- AUTO_INCREMENT for table `user-list`
--
ALTER TABLE `user-list`
  MODIFY `id` int(255) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=20;

--
-- AUTO_INCREMENT for table `useracc`
--
ALTER TABLE `useracc`
  MODIFY `id` int(255) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=20;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
