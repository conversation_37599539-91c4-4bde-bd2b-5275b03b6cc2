<?php
require("database.php");
if (isset($_SESSION["user"])) {
    $email = $_SESSION["user"];

    if (isset($_POST["submit"])) {
        $role = $_POST["role"];
        $des = $_POST["des"];
        // Capture the form values
        $permissions = [
            'inventory_view', 'inventory_create', 'inventory_edit', 'inventory_all', 'import_create', 'category_view',
            'category_create', 'category_edit', 'category_delete', 'category_all', 'brand_view', 'brand_create',
            'brand_edit', 'brand_delete', 'brand_all', 'location_view', 'location_create', 'location_edit',
            'location_all', 'request_view', 'request_create', 'request_approval', 'request_all', 'inventory_return_view',
            'inventory_return_edit', 'inventory_return_all', 'user_view', 'user_create', 'user_edit', 'user_ban', 'user_all',
            'group_view', 'group_create', 'group_edit', 'group_delete', 'group_all', 'request_smtp', 'backup', 
            'section_all', 'hr', 'finance', 'it_support', 'anonymous' ,'ticket_all', 'offboarding_view', 'offboarding_create',
            'offboarding_edit', 'offboarding_all','offboarding_smtp', 'onboarding_view', 'onboarding_create',
            'onboarding_edit', 'onboarding_all'
        ];

        $values = [];
        foreach ($permissions as $permission) {
            $values[] = isset($_POST[$permission]) ? 1 : 0;
        }

        function generateRoleId($length, $conn) {
            $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
            $maxTries = 10;
            $tryCount = 0;

            do {
                $roleId = '';
                for ($i = 0; $i < $length; $i++) {
                    $roleId .= $characters[rand(0, strlen($characters) - 1)];
                }

                $checkQuery = "SELECT COUNT(*) AS count FROM `role-list` WHERE `role-id` = ?";
                $checkStmt = mysqli_prepare($conn, $checkQuery);
                if (!$checkStmt) {
                    die('Error preparing check statement: ' . mysqli_error($conn));
                }
                mysqli_stmt_bind_param($checkStmt, "s", $roleId);
                if (!mysqli_stmt_execute($checkStmt)) {
                    die('Error executing check statement: ' . mysqli_error($conn));
                }
                mysqli_stmt_bind_result($checkStmt, $count);
                mysqli_stmt_fetch($checkStmt);
                mysqli_stmt_close($checkStmt);

                $tryCount++;
            } while ($count > 0 && $tryCount < $maxTries);

            return $roleId;
        }

        $roleId = generateRoleId(10, $conn);

        $sql = "INSERT INTO `role-list` 
        (`role-id`, `role`, `role_description`, `inventory_view`, `inventory_create`, `inventory_edit`, `inventory_all`, 
        `import_create`, `category_view`, `category_create`, `category_edit`, `category_delete`, `category_all`, 
        `brand_view`, `brand_create`, `brand_edit`, `brand_delete`, `brand_all`, `location_view`, `location_create`, 
        `location_edit`, `location_all`, `request_view`, `request_create`, `request_approval`, `request_all`, 
        `inventory_return_view`, `inventory_return_edit`, `inventory_return_all`, `user_view`, `user_create`, `user_edit`, 
        `user_ban`, `user_all`, `group_view`, `group_create`, `group_edit`, `group_delete`, `group_all`, `request_smtp`, 
        `backup`, `section_all`, `hr`, `finance`, `it_support`, `anonymous`, `ticket_all`, `offboarding_view`, `offboarding_create`,
        `offboarding_edit`, `offboarding_all`, `offboarding_smtp`, `onboarding_view`, `onboarding_create`,
        `onboarding_edit`, `onboarding_all`
        ) 
        VALUES (?, ?, ?, " . str_repeat('?, ', count($values) - 1) . "?)";

        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, "sss" . str_repeat('i', count($values)), $roleId, $role, $des, ...$values);

        if (mysqli_stmt_execute($stmt)) {
            echo "<script>alert('The role $role has been added.'); window.location='grouppermissions.php';</script>";
        } else {
            echo "<script>alert('Error: " . mysqli_error($conn) . "'); window.location='grouppermissions.php';</script>";
        }
        mysqli_stmt_close($stmt);
    }
?>
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0">
<meta name="description" content="CLL Warehouse Inventory System">
<meta name="author" content="Powered by CLL Systems Sdn Bhd">
<meta name="robots" content="noindex, nofollow">
<title>CLLXWARE-Create Group Permission</title>
<link rel='icon' type='image/x-icon' href='https://www.cllsystems.com/wp-content/themes/cllsystems/img/favicon.png'>
<link rel="stylesheet" href="assets/css/bootstrap.min.css">
<link rel="stylesheet" href="assets/css/animate.css">
<link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">
<link rel="stylesheet" href="assets/css/dataTables.bootstrap4.min.css">
<link rel="stylesheet" href="assets/plugins/fontawesome/css/fontawesome.min.css">
<link rel="stylesheet" href="assets/plugins/fontawesome/css/all.min.css">
<link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
<div id="global-loader">
<div class="whirly-loader"> </div>
</div>

<div class="main-wrapper">
<?php 
$role_id = $_SESSION["role_id"];
$sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
$result20 = mysqli_query($conn, $sql20);

if ($result20) {
    $row20 = mysqli_fetch_assoc($result20);
    if ($row20) {
       $create = $row20['group_create'];
       $all = $row20['group_all'];
       if ($create != '1' && $all != '1') {
           header("location: ./index.php");
       }
    } else {
        echo "<script>alert('Role data not found')</script>";
    }
} else {
    echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
}
include("header.php"); ?>
</div>

<div class="page-wrapper">
<div class="content">
<div class="page-header">
<div class="page-title">
<h4>Create Permission</h4>
<h6>Manage Create Permissions</h6>
</div>
</div>
<form action="#" method="post">
<div class="card">
<div class="card-body">
<div class="row">
<div class="col-lg-3 col-sm-12">
<div class="form-group">
<label>Role</label>
<input type="text" name='role' required>
</div>
</div>
<div class="col-lg-9 col-sm-12">
<div class="form-group">
<label>Role Description</label>
<input type="text" name='des'>
</div>
</div>
</div>
<div class="row">
<div class="col-12">
<div class="productdetails product-respon">
<ul>
<!-- Add checkboxes for each permission category as required -->
<li>
<h4>Inventory List</h4>
<div class="input-checkset">
<ul>
<li>
<label class="inputcheck">Select All
<input type="checkbox" name='inventory_all' value='1'>
<span class="checkmark"></span>
</label>
</li>
<li>
<label class="inputcheck">View
<input type="checkbox" name='inventory_view' value='1'>
<span class="checkmark"></span>
</label>
</li>
<li>
<label class="inputcheck">Create
<input type="checkbox" name='inventory_create' value='1'>
<span class="checkmark"></span>
</label>
</li>
<li>
<label class="inputcheck">Edit
<input type="checkbox" name='inventory_edit' value='1'>
<span class="checkmark"></span>
</label>
</li>
</ul>
</div>
</li>
<li>
<h4>Import Product</h4>
<div class="input-checkset">
<ul>
<label class="inputcheck">Create
<input type="checkbox" name='import_create' value='1'>
<span class="checkmark"></span>
</label>
</li>
</ul>
</div>
</li>
<li>
<h4>Category List</h4>
<div class="input-checkset">
<ul><li>
<label class="inputcheck">Select All
<input type="checkbox" name='category_all' value='1'>
<span class="checkmark"></span>
</label>
</li>
<li>
<label class="inputcheck">View
<input type="checkbox" name='category_view' value='1'>
<span class="checkmark"></span>
</label>
</li>
<li>
<label class="inputcheck">Create
<input type="checkbox" name='category_create' value='1'>
<span class="checkmark"></span>
</label>
</li>
<li>
<label class="inputcheck">Edit
<input type="checkbox" name='category_edit' value='1'>
<span class="checkmark"></span>
</label>
</li>
<li>
<label class="inputcheck">Hide
<input type="checkbox" name='category_delete' value='1'>
<span class="checkmark"></span>
</label>
</li>
</ul>
</div>
</li>
<li>
<h4>Brand List</h4>
<div class="input-checkset">
<ul>
<li>
<label class="inputcheck">Select All
<input type="checkbox" name='brand_all' value='1'>
<span class="checkmark"></span>
</label>
</li>
<li>
<label class="inputcheck">View
<input type="checkbox" name='brand_view' value='1'>
<span class="checkmark"></span>
</label>
</li>
<li>
<label class="inputcheck">Create
<input type="checkbox" name='brand_create' value='1'>
<span class="checkmark"></span>
</label>
</li>
<li>
<label class="inputcheck">Edit
<input type="checkbox" name='brand_edit' value='1'>
<span class="checkmark"></span>
</label>
</li>
<li>
<label class="inputcheck">Hide
<input type="checkbox" name='brand_delete' value='1'>
<span class="checkmark"></span>
</label>
</li>
</ul>
</div>
</li>
<li>
<h4>Location List</h4>
<div class="input-checkset">
<ul>
<li>
<label class="inputcheck">Select All
<input type="checkbox" name='location_all' value='1'>
<span class="checkmark"></span>
</label>
</li>
<li>
<label class="inputcheck">View
<input type="checkbox" name='location_view' value='1'>
<span class="checkmark"></span>
</label>
</li>
<li>
<label class="inputcheck">Create
<input type="checkbox" name='location_create' value='1'>
<span class="checkmark"></span>
</label>
</li>
<li>
<label class="inputcheck">Edit
<input type="checkbox" name='location_edit' value='1'>
<span class="checkmark"></span>
</label>
</li>
</ul>
</div>
</li>
<li>
<h4>Request List</h4>
<div class="input-checkset">
<ul>
<li>
<label class="inputcheck">Select All
<input type="checkbox" name='request_all' value='1'>
<span class="checkmark"></span>
</label>
</li>
<li>
<label class="inputcheck">View
<input type="checkbox" name='request_view' value='1'>
<span class="checkmark"></span>
 </label>
</li>
<li>
<label class="inputcheck">Create
<input type="checkbox" name='request_create' value='1'>
<span class="checkmark"></span>
</label>
</li>
<li>
<label class="inputcheck">Approval
<input type="checkbox" name='request_approval' value='1'>
<span class="checkmark"></span>
</label>
</li>
<li>
<label class="inputcheck">Send Mail Notification
<input type="checkbox" name='request_smtp' value='1'>
<span class="checkmark"></span>
</label>
</li>
</ul>
</div>
</li>
<li>
<h4>Inventory Return List</h4>
<div class="input-checkset">
<ul>
<li>
<label class="inputcheck">Select All
<input type="checkbox" name='inventory_return_all' value='1'>
<span class="checkmark"></span>
</label>
</li>
<li>
<label class="inputcheck">View
<input type="checkbox" name='inventory_return_view' value='1'>
<span class="checkmark"></span>
</label>
</li>
<li>
<label class="inputcheck">Edit
<input type="checkbox" name='inventory_return_edit' value='1'>
<span class="checkmark"></span>
</label>
</li>
</ul>
</div>
</li>
<li>
<h4>User List</h4>
<div class="input-checkset">
<ul>
<li>
<label class="inputcheck">Select All
<input type="checkbox" name='user_all' value='1'>
<span class="checkmark"></span>
</label>
</li>
<li>
<label class="inputcheck">View
<input type="checkbox" name='user_view' value='1'>
<span class="checkmark"></span>
</label>
</li>
<li>
<label class="inputcheck">Create
<input type="checkbox" name='user_create' value='1'>
<span class="checkmark"></span>
</label>
</li>
<li>
<label class="inputcheck">Edit
<input type="checkbox" name='user_edit' value='1'>
<span class="checkmark"></span>
</label>
</li>
<li>
<label class="inputcheck">Ban
<input type="checkbox" name='user_ban' value='1'>
<span class="checkmark"></span>
</label>
</li>
</ul>
</div>
</li>
<li>
<h4>Group Permission</h4>
<div class="input-checkset">
<ul>
<li>
<label class="inputcheck">Select All
<input type="checkbox" name='group_all' value='1'>
<span class="checkmark"></span>
</label>
</li>
<li>
<label class="inputcheck">View
<input type="checkbox" name='group_view' value='1'>
<span class="checkmark"></span>
</label>
</li>
<li>
<label class="inputcheck">Create
<input type="checkbox" name='group_create' value='1'>
<span class="checkmark"></span>
</label>
</li>
<li>
<label class="inputcheck">Edit
<input type="checkbox" name='group_edit' value='1'>
<span class="checkmark"></span>
</label>
</li>
<li>
<label class="inputcheck">Hide
<input type="checkbox" name='group_delete' value='1'>
<span class="checkmark"></span>
</label>
</li>
</ul>
</div>
</li>

<li>
<h4>Daily Backup</h4>
<div class="input-checkset">
<ul>
<li>
<label class="inputcheck">Receive Backup
<input type="checkbox" name='backup' value='1'>
<span class="checkmark"></span>
</label>
</li>
</ul>
</div>
</li>

<li>
<h4>Ticketing List</h4>
<div class="input-checkset">
<ul>
<li>
<label class="inputcheck">Select All
<input type="checkbox" name='section_all' value='1'>
<span class="checkmark"></span>
</label>
</li>
<li>
<label class="inputcheck">user
<input type="checkbox" name='user' value='1'>
<span class="checkmark"></span>
</label>
</li>
<li>
<label class="inputcheck">HR
<input type="checkbox" name='hr' value='1'>
<span class="checkmark"></span>
</label>
</li>
<li>
<label class="inputcheck">Finance
<input type="checkbox" name='finance' value='1'>
<span class="checkmark"></span>
</label>
</li>
<li>
<label class="inputcheck">IT Support
<input type="checkbox" name='it_support' value='1'>
<span class="checkmark"></span>
</label>
</li>
<li>
<label class="inputcheck">Anonymous
<input type="checkbox" name='anonymous' value='1'>
<span class="checkmark"></span>
</label>
</li>
</ul>
</div>
</li>

<li>
<h4>Ticket Form List</h4>
<div class="input-checkset">
<ul>
<li>
<label class="inputcheck">Select All
<input type="checkbox" name='ticket_all' value='1'>
<span class="checkmark"></span>
</label>
</li>
</ul>
</div>
</li>

<li>
<h4>Off Boarding List</h4>
<div class="input-checkset">
<ul>
<li>
<label class="inputcheck">Select All
<input type="checkbox" name='offboarding_all' value='1'>
<span class="checkmark"></span>
</label>
</li>
<li>
<label class="inputcheck">View
<input type="checkbox" name='offboarding_view' value='1'>
<span class="checkmark"></span>
</label>
</li>
<li>
<label class="inputcheck">Create
<input type="checkbox" name='offboarding_create' value='1'>
<span class="checkmark"></span>
</label>
</li>
<li>
<label class="inputcheck">Edit
<input type="checkbox" name='offboarding_edit' value='1'>
<span class="checkmark"></span>
</label>
</li>
<li>
<label class="inputcheck">Send Mail Notification
<input type="checkbox" name='offboarding_smtp' value='1'>
<span class="checkmark"></span>
</label>
</li>
</ul>
</div>
</li>

<li>
<h4>On Boarding List</h4>
<div class="input-checkset">
<ul>
<li>
<label class="inputcheck">Select All
<input type="checkbox" name='onboarding_all' value='1'>
<span class="checkmark"></span>
</label>
</li>
<li>
<label class="inputcheck">View
<input type="checkbox" name='onboarding_view' value='1'>
<span class="checkmark"></span>
</label>
</li>
<li>
<label class="inputcheck">Create
<input type="checkbox" name='onboarding_create' value='1'>
<span class="checkmark"></span>
</label>
</li>
<li>
<label class="inputcheck">Edit
<input type="checkbox" name='onboarding_edit' value='1'>
<span class="checkmark"></span>
</label>
</li>
</ul>
</div>
</li>





</ul>
</div>
</div>
</div>
</div>
</div>
<div class="col-lg-12">
<input type='submit'  class='btn btn-submit me-2' name='submit' value='Submit'>
<a href="grouppermissions.php" class="btn btn-cancel">Cancel</a>
</div>
</form>
</div>
</div>
</div>

<script src="assets/js/jquery-3.6.0.min.js"></script>
<script src="assets/js/feather.min.js"></script>
<script src="assets/js/jquery.slimscroll.min.js"></script>
<script src="assets/js/jquery.dataTables.min.js"></script>
<script src="assets/js/dataTables.bootstrap4.min.js"></script>
<script src="assets/js/bootstrap.bundle.min.js"></script>
<script src="assets/plugins/select2/js/select2.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalert2.all.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalerts.min.js"></script>
<script src="assets/js/script.js"></script>
</body>
</html>
<?php
} else {
    header("location: ./signin.php");
}
?>
