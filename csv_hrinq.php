<?php
require("database.php");

header('Content-Type: text/csv; charset=utf-8');
header('Content-Disposition: attachment; filename=hr_inquiries.csv');

$output = fopen('php://output', 'w');
fputcsv($output, array('No', 'HR Inquiry', 'Description'));

$sql = "SELECT * FROM `hrinq_list`";
$result = mysqli_query($conn, $sql);

if ($result && mysqli_num_rows($result) > 0) {
    $counter = 1;
    while ($row = mysqli_fetch_assoc($result)) {
        fputcsv($output, array($counter, $row['hr_inq'], $row['des']));
        $counter++;
    }
} else {
    fputcsv($output, array('No data available.'));
}

fclose($output);
mysqli_close($conn);
?>
