<?php
require("database.php");
$sql = "SELECT i.`inventory-id`, i.`product-name`, b.`brand-name`, c.`category-name`, i.`location-id`, i.`part-number`, i.`serial-number`, i.`quantity`, i.`ownership`, i.`des`, i.`status`, i.`remark`, i.`created` 
        FROM `inventory-list` i
        LEFT JOIN `brand-list` b ON i.`brand-id` = b.`brand-id`
        LEFT JOIN `category-list` c ON i.`category-id` = c.`category-id`";
$result = $conn->query($sql);

if ($result->num_rows > 0) {
    // Set the output filename
    $filename = "CLL Systems Inventory List.csv";

    // Open the file in write mode
    $file = fopen($filename, 'w');

    // Write the CSV headers
    $headers = array("Inventory ID", "Product Name", "Brand", "Category", "Location", "Product Part Number", "Product Serial Number", "Quantity", "Ownership", "Description", "Status (Available / Taken)", "Remark", "Created By");
    fputcsv($file, $headers);
    

    while ($row = $result->fetch_assoc()) {
        // Adjust the status to display "Available" or "Taken"
        $row['status'] = ($row['status'] == 1) ? 'Available' : 'Taken';
        
        // Write the row to the CSV file
        fputcsv($file, $row);
    }
    
    // Close the file
    fclose($file);

    // Set the appropriate headers for the HTTP response
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="' . $filename . '"');

    // Read the file and output its contents
    readfile($filename);

    // Delete the temporary file
    unlink($filename);
} else {
    echo "<script> alert('No data found in the table.');window.location='inventorylist.php';</script>";
}

// Close the database connection
$conn->close();
?>
