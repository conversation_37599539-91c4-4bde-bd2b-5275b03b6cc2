<?php
require("database.php");
$sql = "SELECT l.`room`, l.`rack`, l.`des`, l.`created` 
        FROM `location-list` l";
$result = $conn->query($sql);

if ($result->num_rows > 0) {
    // Set the output filename
    $filename = "CLL Systems Location List.csv";

    // Open the file in write mode
    $file = fopen($filename, 'w');

    // Write the CSV headers
    $headers = array("Room", "Rack", "Description", "Created By");
    fputcsv($file, $headers);
    

    while ($row = $result->fetch_assoc()) {
        fputcsv($file, $row);
    }
    
    // Close the file
    fclose($file);

    // Set the appropriate headers for the HTTP response
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="' . $filename . '"');

    // Read the file and output its contents
    readfile($filename);

    // Delete the temporary file
    unlink($filename);
} else {
    echo "<script> alert('No data found in the table.');window.location='location_list.php';</script>";
}

// Close the database connection
$conn->close();
?>
