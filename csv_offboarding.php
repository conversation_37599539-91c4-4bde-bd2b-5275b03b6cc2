<?php
require("database.php");

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (isset($_SESSION["user"])) {
    // Set headers to prompt download with UTF-8 BOM for Excel compatibility
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename=offboarding_list.csv');

    // Open output stream
    $output = fopen('php://output', 'w');

    // Add UTF-8 BOM to ensure correct encoding in Excel
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

    // Output column headings
    fputcsv($output, array(
        'No',
        'Employee ID',
        'Employee Email',
        'Name',
        'Position',
        'Department',
        'Personal Email',
        'Physical Last Day',
        'Official Last Day',
        'Reporting Manager',
        'Remark'
    ));

    // Enhanced SQL Query with JOINs to fetch related names
    $sql = "
        SELECT 
            ol.offboarding_id,
            ol.employee_id, 
            ol.employee_email,
            ol.name, 
            pl.position_name, 
            dl.department_name, 
            ol.personal_email, 
            ol.physical_last_day, 
            ol.official_last_day, 
            ml.manager_name, 
            ol.remark
        FROM 
            `offboarding_list` ol
        LEFT JOIN 
            `position_list` pl ON ol.position_id = pl.position_id
        LEFT JOIN 
            `department_list` dl ON ol.department_id = dl.department_id
        LEFT JOIN 
            `manager_list` ml ON ol.reporting_manager_id = ml.manager_id
        ORDER BY 
            ol.offboarding_id ASC
    ";
    
    // Prepare and execute the statement
    $stmt = mysqli_prepare($conn, $sql);
    if ($stmt) {
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);

        if ($result && mysqli_num_rows($result) > 0) {
            $counter = 1;
            while ($row = mysqli_fetch_assoc($result)) {
                // Prepare the row data
                $csvRow = array(
                    $counter,
                    $row['employee_id'],
                    $row['employee_email'],
                    $row['name'],
                    $row['position_name'] ? $row['position_name'] : 'N/A',
                    $row['department_name'] ? $row['department_name'] : 'N/A',
                    $row['personal_email'],
                    $row['physical_last_day'],
                    $row['official_last_day'],
                    $row['manager_name'] ? $row['manager_name'] : 'N/A',
                    $row['remark']
                );

                // Output the row to CSV
                fputcsv($output, $csvRow);
                $counter++;
            }
        } else {
            // If no records found, add a row indicating that
            fputcsv($output, array('No records found.'));
        }

        // Close the statement
        mysqli_stmt_close($stmt);
    } else {
        // Handle SQL preparation error
        fputcsv($output, array('Error preparing the SQL statement.'));
    }

    // Close the output stream
    fclose($output);
    exit;
} else {
    header("location: ./signin.php");
    exit;
}
?>
