<?php
require("database.php");
$sql = "SELECT r.`request-id`, r.`request_date`, i.`product-name`, b.`brand-name`, r.`quantity`, ul.`name`, r.`company_name`, i.`ownership`, r.`return_date` , r.`request_status`, r.`approval_status`, r.`approval_date` , r.`inventory_status`, r.`collection_date`
        FROM `request-list` r 
        LEFT JOIN `user-list` ul ON r.`acc_id` = ul.`acc_id`
        LEFT JOIN `inventory-list` i ON r.`inventory-id` = i.`inventory-id`
        LEFT JOIN `brand-list` b ON i.`brand-id` = b.`brand-id`
        WHERE `approval_status` = '1' AND `inventory_status` = '3'
        ";
$result = $conn->query($sql);

if ($result->num_rows > 0) {
    // Set the output filename
    $filename = "CLL Systems Return Request List.csv";

    // Open the file in write mode
    $file = fopen($filename, 'w');

    // Write the CSV headers
    $headers = array("Request ID", "Request Date", "Product Model", "Brand", "Quantity", "Requester Name", "Company Name", "Ownership", "Inventory Return Date", "Request Status", "Approval Status", "Approval Date", "Inventory Status", "Collection Date");
    fputcsv($file, $headers);
    

    while ($row = $result->fetch_assoc()) {
        $row['request_status'] = ($row['request_status'] == 1) ? 'Permanent' : 'Borrowed';
        
        if ($row['approval_status'] == 0) {
            $row['approval_status'] = 'Pending';
        } elseif ($row['approval_status'] == 1) {
            $row['approval_status'] = 'Approved';
        } elseif ($row['approval_status'] == 2) {
            $row['approval_status'] = 'Declined';
        }

        if ($row['inventory_status'] == 0) {
            $row['inventory_status'] = 'Available';
        } elseif ($row['inventory_status'] == 1) {
            $row['inventory_status'] = 'Pending Collection';
        } elseif ($row['inventory_status'] == 2) {
            $row['inventory_status'] = 'Taken';
        } elseif ($row['inventory_status'] == 3) {
            $row['inventory_status'] = 'Returned';
        }

        fputcsv($file, $row);
    }
    
    // Close the file
    fclose($file);

    // Set the appropriate headers for the HTTP response
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="' . $filename . '"');

    // Read the file and output its contents
    readfile($filename);

    // Delete the temporary file
    unlink($filename);
} else {
    echo "<script> alert('No data found in the table.');window.location='inventoryreturnlists.php';</script>";
}

// Close the database connection
$conn->close();
?>
