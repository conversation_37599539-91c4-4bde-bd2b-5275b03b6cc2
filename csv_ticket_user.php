<?php
require("database.php");

// Check if the user is logged in
if (!isset($_SESSION["user"])) {
    header("location: ./signin.php");
    exit();
}

$acc_id = $_SESSION["acc_id"];
$role_id = $_SESSION["role_id"];
$anon_token = $_COOKIE["anon_token_$acc_id"] ?? '';

$is_admin = false;

// Fetch role permissions
$sql_role = "SELECT * FROM `role-list` WHERE `role-id` = ?";
$stmt_role = mysqli_prepare($conn, $sql_role);
mysqli_stmt_bind_param($stmt_role, "s", $role_id);
mysqli_stmt_execute($stmt_role);
$result_role = mysqli_stmt_get_result($stmt_role);

if ($row_role = mysqli_fetch_assoc($result_role)) {
    $all = $row_role['section_all'];
    $user = $row_role['user'] || $row_role['hr'] || $row_role['finance'] || $row_role['it_support'] || $row_role['anonymous'];

    if ($user != '1' && $all != '1') {
        header("location: ./index.php");
        exit();
    }
} else {
    echo "<script>alert('Role data not found')</script>";
    exit();
}

$where_clause = "WHERE (`sections`.`acc_id` = '$acc_id' OR `anonymous_inquiry_list`.`token` = '$anon_token')";

// Query to get the tickets based on the conditions
$sql = "SELECT sections.*, anonymous_inquiry_list.token FROM `sections`
        LEFT JOIN `anonymous_inquiry_list` ON `sections`.`inq_id` = `anonymous_inquiry_list`.`inq_id`
        $where_clause
        ORDER BY `sections`.`date_submitted` DESC";
$result = mysqli_query($conn, $sql);

if (!$result) {
    echo "Error fetching data: " . mysqli_error($conn);
    exit();
}

// Define the CSV headers
$csv_headers = ["No", "Section Name", "Status", "Priority Level", "Created by", "Date Submitted", "Comments"];

// Start output buffering to capture output
ob_start();
$csv_file = fopen('php://output', 'w');

// Send the headers
fputcsv($csv_file, $csv_headers);

$counter = 1;
while ($row = mysqli_fetch_assoc($result)) {
    // Query to fetch data from user-list
    $sql3 = "SELECT * FROM `user-list` WHERE `acc_id` = '" . $row['acc_id'] . "'";
    $result3 = mysqli_query($conn, $sql3);
    if ($result3 && mysqli_num_rows($result3) > 0) {
        $row3 = mysqli_fetch_assoc($result3);
        $created_by = !empty($row3['name']) ? $row3['name'] : $row3['email'];
    } else {
        $created_by = 'Anonymous';
    }

    $priority_level = !empty($row['priority_level']) ? $row['priority_level'] : '-';

    $csv_row = [
        $counter,
        $row['section_name'],
        $row['status'],
        $priority_level,
        $created_by,
        $row['date_submitted'],
        $row['comments']
    ];

    fputcsv($csv_file, $csv_row);
    $counter++;
}

// Close the file
fclose($csv_file);

// Capture the output and send the appropriate headers
$csv_output = ob_get_clean();
header('Content-Type: text/csv');
header('Content-Disposition: attachment;filename=CLL_Systems_MyRequests_List.csv');
echo $csv_output;
exit();
?>
