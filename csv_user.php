<?php
require("database.php");
$sql = "SELECT ul.`acc_id`, ul.`name`, ul.`email`, ul.`designation`, ul.`department`, rl.`role`, ua.`ban` 
        FROM `user-list` ul
        LEFT JOIN `useracc` ua ON ul.`acc_id` = ua.`acc_id`
        LEFT JOIN `role-list` rl ON ua.`role_id` = rl.`role-id`";
$result = $conn->query($sql);

if ($result->num_rows > 0) {
    // Set the output filename
    $filename = "CLL Systems User List.csv";

    // Open the file in write mode
    $file = fopen($filename, 'w');

    // Write the CSV headers
    $headers = array("Profile ID", "Full Name", "Email", "Designation", "Department", "Role", "Account Status");
    fputcsv($file, $headers);
    

    while ($row = $result->fetch_assoc()) {
        $row['ban'] = ($row['ban'] == 1) ? 'Restricted' : 'Active';
        
        // Write the row to the CSV file
        fputcsv($file, $row);
    }
    
    // Close the file
    fclose($file);

    // Set the appropriate headers for the HTTP response
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="' . $filename . '"');

    // Read the file and output its contents
    readfile($filename);

    // Delete the temporary file
    unlink($filename);
} else {
    echo "<script> alert('No data found in the table.');window.location='userlist.php';</script>";
}

// Close the database connection
$conn->close();
?>
