-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jul 05, 2024 at 10:03 AM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `cll-inventory`
--

-- --------------------------------------------------------

--
-- Table structure for table `role-list`
--

CREATE TABLE `role-list` (
  `id` int(255) NOT NULL,
  `role-id` varchar(255) NOT NULL,
  `role` varchar(255) NOT NULL,
  `role_description` varchar(255) NOT NULL,
  `inventory_view` int(255) NOT NULL,
  `inventory_create` int(255) NOT NULL,
  `inventory_edit` int(255) NOT NULL,
  `inventory_delete` int(255) NOT NULL,
  `inventory_all` int(255) NOT NULL,
  `import_create` int(255) NOT NULL,
  `category_view` int(255) NOT NULL,
  `category_create` int(255) NOT NULL,
  `category_edit` int(255) NOT NULL,
  `category_delete` int(255) NOT NULL,
  `category_all` int(255) NOT NULL,
  `brand_view` int(255) NOT NULL,
  `brand_create` int(255) NOT NULL,
  `brand_edit` int(255) NOT NULL,
  `brand_delete` int(255) NOT NULL,
  `brand_all` int(255) NOT NULL,
  `location_view` int(255) NOT NULL,
  `location_create` int(255) NOT NULL,
  `location_edit` int(255) NOT NULL,
  `location_delete` int(255) NOT NULL,
  `location_all` int(255) NOT NULL,
  `request_view` int(255) NOT NULL,
  `request_create` int(255) NOT NULL,
  `request_approval` int(255) NOT NULL,
  `request_all` int(255) NOT NULL,
  `inventory_return_view` int(255) NOT NULL,
  `inventory_return_edit` int(255) NOT NULL,
  `inventory_return_delete` int(255) NOT NULL,
  `inventory_return_all` int(255) NOT NULL,
  `user_view` int(255) NOT NULL,
  `user_create` int(255) NOT NULL,
  `user_edit` int(255) NOT NULL,
  `user_ban` int(255) NOT NULL,
  `user_delete` int(255) NOT NULL,
  `user_all` int(255) NOT NULL,
  `group_view` int(255) NOT NULL,
  `group_create` int(255) NOT NULL,
  `group_edit` int(255) NOT NULL,
  `group_delete` int(255) NOT NULL,
  `group_all` int(255) NOT NULL,
  `hide` int(255) NOT NULL,
  `request_smtp` int(11) NOT NULL,
  `backup` int(255) NOT NULL,
  `user` tinyint(1) DEFAULT 0,
  `hr` tinyint(1) DEFAULT 0,
  `finance` tinyint(1) DEFAULT 0,
  `it_support` tinyint(1) DEFAULT 0,
  `anonymous` tinyint(1) DEFAULT 0,
  `section_all` tinyint(1) DEFAULT 0,
  `ticket_all` tinyint(1) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `role-list`
--

INSERT INTO `role-list` (`id`, `role-id`, `role`, `role_description`, `inventory_view`, `inventory_create`, `inventory_edit`, `inventory_delete`, `inventory_all`, `import_create`, `category_view`, `category_create`, `category_edit`, `category_delete`, `category_all`, `brand_view`, `brand_create`, `brand_edit`, `brand_delete`, `brand_all`, `location_view`, `location_create`, `location_edit`, `location_delete`, `location_all`, `request_view`, `request_create`, `request_approval`, `request_all`, `inventory_return_view`, `inventory_return_edit`, `inventory_return_delete`, `inventory_return_all`, `user_view`, `user_create`, `user_edit`, `user_ban`, `user_delete`, `user_all`, `group_view`, `group_create`, `group_edit`, `group_delete`, `group_all`, `hide`, `request_smtp`, `backup`, `user`, `hr`, `finance`, `it_support`, `anonymous`, `section_all`, `ticket_all`) VALUES
(1, 'x9Mj5GmOGz', 'HR', 'Master Admin', 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1),
(2, 'UzZhqYA3OJ', 'User', 'Normal User', 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0),
(3, '0IH26m4Tiv', 'Procurement', 'Procurement will receive an email notification when there is a request from an end user.', 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(4, 'LvIDnzcptn', 'IT Support', 'test', 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 1, 0, 0, 0, 1, 0, 0, 1);

--
-- Indexes for dumped tables
--

--
-- Indexes for table `role-list`
--
ALTER TABLE `role-list`
  ADD PRIMARY KEY (`id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `role-list`
--
ALTER TABLE `role-list`
  MODIFY `id` int(255) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
