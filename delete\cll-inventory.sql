-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Mar 08, 2024 at 09:08 AM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.0.30

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `cll-inventory`
--

-- --------------------------------------------------------

--
-- Table structure for table `brand-list`
--

CREATE TABLE `brand-list` (
  `id` int(255) NOT NULL,
  `brand-name` varchar(255) NOT NULL,
  `brand-id` varchar(255) NOT NULL,
  `des` varchar(999) NOT NULL,
  `created` varchar(255) NOT NULL,
  `hide` int(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `brand-list`
--

INSERT INTO `brand-list` (`id`, `brand-name`, `brand-id`, `des`, `created`, `hide`) VALUES
(1, 'Cisco', '3271959233', 'CSV import added this Brand ID', 'Alex Ang', 0),
(2, 'Juniper Networks', '3107439717', 'CSV import added this Brand ID', 'Alex Ang', 0),
(3, 'APC', '9223873625', 'CSV import added this Brand ID', 'Alex Ang', 0),
(4, 'Aruba Networks', '4268328558', 'CSV import added this Brand ID', 'Alex Ang', 0),
(5, 'Belkin', '4174530348', 'CSV import added this Brand ID', 'Alex Ang', 0),
(6, 'Fortinet', '1634112121', 'CSV import added this Brand ID', 'Alex Ang', 0),
(7, 'Panduit', '2070235736', 'CSV import added this Brand ID', 'Alex Ang', 0),
(8, 'Tripp Lite', '0742870078', 'CSV import added this Brand ID', 'Alex Ang', 0),
(9, 'Corning', '0244180542', 'CSV import added this Brand ID', 'Alex Ang', 0),
(10, 'Dell EMC', '3501829193', 'CSV import added this Brand ID', 'Alex Ang', 0),
(11, 'Synology', '5021987615', 'CSV import added this Brand ID', 'Alex Ang', 0),
(12, 'IOGEAR', '8131462793', 'CSV import added this Brand ID', 'Alex Ang', 0),
(13, 'HPE', '0295776285', 'CSV import added this Brand ID', 'Alex Ang', 0);

-- --------------------------------------------------------

--
-- Table structure for table `category-list`
--

CREATE TABLE `category-list` (
  `id` int(255) NOT NULL,
  `category-id` varchar(255) NOT NULL,
  `category-name` varchar(255) NOT NULL,
  `des` varchar(999) NOT NULL,
  `created` varchar(255) NOT NULL,
  `hide` int(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `category-list`
--

INSERT INTO `category-list` (`id`, `category-id`, `category-name`, `des`, `created`, `hide`) VALUES
(1, '7577505059', 'Infrastructure', 'CSV import added this Category ID', 'Alex Ang', 0),
(2, '5501310663', 'Cat6', 'CSV import added this Category ID', 'Alex Ang', 0),
(3, '8848462118', 'Router', 'CSV import added this Category ID', 'Alex Ang', 0),
(4, '3431257563', 'NAS', 'CSV import added this Category ID', 'Alex Ang', 0);

-- --------------------------------------------------------

--
-- Table structure for table `inventory-list`
--

CREATE TABLE `inventory-list` (
  `id` int(255) NOT NULL,
  `product-name` varchar(255) NOT NULL,
  `brand-id` varchar(255) NOT NULL,
  `category-id` varchar(255) NOT NULL,
  `location-id` varchar(255) NOT NULL,
  `part-number` varchar(255) NOT NULL,
  `serial-number` varchar(255) NOT NULL,
  `quantity` int(255) NOT NULL,
  `ownership` varchar(255) NOT NULL,
  `des` varchar(999) NOT NULL,
  `status` int(255) NOT NULL,
  `remark` varchar(255) NOT NULL,
  `created` varchar(255) NOT NULL,
  `inventory-id` varchar(255) NOT NULL,
  `reserve_qty` int(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `inventory-list`
--

INSERT INTO `inventory-list` (`id`, `product-name`, `brand-id`, `category-id`, `location-id`, `part-number`, `serial-number`, `quantity`, `ownership`, `des`, `status`, `remark`, `created`, `inventory-id`, `reserve_qty`) VALUES
(1, 'Server Rack', '3271959233', '7577505059', '001 | 01', 'SR-001', 'CISCOSR001', 2, 'Owned', '42U server rack with locking doors', 1, '', 'Alex Ang', 'N7Uo5Mw21H', 0),
(2, 'Network Switch', '3107439717', '7577505059', '001 | 02', 'NS-002', 'JNWSW002', 3, 'Leased', '24-port gigabit Ethernet switch', 1, '', 'Alex Ang', 'njqTK6QVV4', 0),
(3, 'UPS (Uninterruptible Power Supply)', '9223873625', '7577505059', '001 | 03', 'UPS-003', 'APCUPS003', 1, 'Owned', '1500VA UPS for power backup', 1, '', 'Alex Ang', 'jIFeG2AYq1', 0),
(4, 'Wireless Access Point', '4268328558', '7577505059', '001 | 01', 'WAP-004', 'ARUBAWAP004', 5, 'Owned', 'Dual-band Wi-Fi access point', 1, '', 'Alex Ang', '9082JYHfKl', 0),
(5, 'Ethernet Cable (Cat6)', '4174530348', '5501310663', '001 | 03', 'EC-005', 'BLKEC005', 100, 'Owned', '5-foot Cat6 Ethernet cable', 1, '', 'Alex Ang', 'zZIcL4CFxz', 0),
(6, 'Firewall Appliance', '1634112121', '7577505059', '001 | 01', 'FA-006', 'FORTFA006', 2, 'Owned', 'Next-generation firewall appliance', 1, '', 'Alex Ang', '7ixWLmQnus', 0),
(7, 'Patch Panel', '2070235736', '7577505059', '001 | 01', 'PP-007', 'PANDPP007', 1, 'Owned', '24-port patch panel for Ethernet connections', 1, '', 'Alex Ang', 'jK6pZtQMVD', 0),
(8, 'Power Distribution Unit (PDU)', '0742870078', '7577505059', '001 | 02', 'PDU-008', 'TRIPDU008', 3, 'Leased', '20-outlet rackmount PDU', 1, '', 'Alex Ang', '2CWLetXUyY', 0),
(9, 'Fiber Optic Cable', '0244180542', '7577505059', '001 | 04', 'FOC-009', 'CORNFOC009', 50, 'Owned', 'Single-mode fiber optic cable (10 meters)', 1, '', 'Alex Ang', '1P4sG4L0Ri', 0),
(10, 'Server', '3501829193', '7577505059', '001 | 01', 'SVR-010', 'DELLSVR010', 4, 'Leased', 'Dell PowerEdge R740 rack server', 1, '', 'Alex Ang', 'FRLwrwQnK7', 0),
(11, 'Router', '3271959233', '8848462118', '001 | 04', 'RTR-011', 'CISRTR011', 2, 'Owned', 'Cisco ISR 4000 series router', 1, '', 'Alex Ang', 'QL3kWYKXSR', 0),
(12, 'Power Cable', '9223873625', '7577505059', '001 | 05', 'PC-012', 'APCCAB012', 50, 'Owned', '6-foot power cable with IEC C13 connector', 1, '', 'Alex Ang', 'nrRET67GRU', 0),
(13, 'Network Attached Storage (NAS)', '5021987615', '3431257563', '001 | 02', 'NAS-013', 'SYNNAS013', 1, 'Owned', 'Synology DiskStation DS220+ NAS', 1, '', 'Alex Ang', 'Fs54y1LIzN', 0),
(14, 'KVM Switch', '8131462793', '7577505059', '001 | 05', 'KVM-014', 'IOGKVM014', 1, 'Owned', '4-port USB VGA KVM switch', 1, '', 'Alex Ang', 'cVeW9jBHRm', 0),
(15, 'Ethernet Switch Module', '0295776285', '7577505059', '001 | 06', 'ESM-015', 'HPEESM015', 3, 'Owned', '8-port Gigabit Ethernet switch module for HP ProCurve switch', 1, '', 'Alex Ang', 'Os4ao9YOK9', 0);

-- --------------------------------------------------------

--
-- Table structure for table `location-list`
--

CREATE TABLE `location-list` (
  `id` int(255) NOT NULL,
  `room` varchar(255) NOT NULL,
  `rack` varchar(255) NOT NULL,
  `des` varchar(255) NOT NULL,
  `created` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `location-list`
--

INSERT INTO `location-list` (`id`, `room`, `rack`, `des`, `created`) VALUES
(1, '001', '01', 'CSV import added this Location ID', 'Alex Ang'),
(2, '001', '02', 'CSV import added this Location ID', 'Alex Ang'),
(3, '001', '03', 'CSV import added this Location ID', 'Alex Ang'),
(4, '001', '04', 'CSV import added this Location ID', 'Alex Ang'),
(5, '001', '05', 'CSV import added this Location ID', 'Alex Ang'),
(6, '001', '06', 'CSV import added this Location ID', 'Alex Ang');

-- --------------------------------------------------------

--
-- Table structure for table `notification`
--

CREATE TABLE `notification` (
  `id` int(255) NOT NULL,
  `acc_id` varchar(255) NOT NULL,
  `request_id` varchar(255) NOT NULL,
  `timestamp` varchar(255) NOT NULL,
  `read_msg` int(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `request-list`
--

CREATE TABLE `request-list` (
  `id` int(255) NOT NULL,
  `request-id` varchar(255) NOT NULL,
  `inventory-id` varchar(255) NOT NULL,
  `acc_id` varchar(255) NOT NULL,
  `company_name` varchar(255) NOT NULL,
  `company_category` varchar(255) NOT NULL,
  `request_date` varchar(255) NOT NULL,
  `approval_date` varchar(255) NOT NULL,
  `collection_date` varchar(255) NOT NULL,
  `return_date` varchar(255) NOT NULL,
  `quantity` int(255) NOT NULL,
  `approval_status` int(255) NOT NULL,
  `approval_remark` varchar(255) NOT NULL,
  `request_status` int(255) NOT NULL,
  `request_remark` varchar(255) NOT NULL,
  `inventory_status` int(255) NOT NULL,
  `inventory_remark` varchar(255) NOT NULL,
  `approval_made_by` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `role-list`
--

CREATE TABLE `role-list` (
  `id` int(255) NOT NULL,
  `role-id` varchar(255) NOT NULL,
  `role` varchar(255) NOT NULL,
  `role_description` varchar(255) NOT NULL,
  `inventory_view` int(255) NOT NULL,
  `inventory_create` int(255) NOT NULL,
  `inventory_edit` int(255) NOT NULL,
  `inventory_delete` int(255) NOT NULL,
  `inventory_all` int(255) NOT NULL,
  `import_create` int(255) NOT NULL,
  `category_view` int(255) NOT NULL,
  `category_create` int(255) NOT NULL,
  `category_edit` int(255) NOT NULL,
  `category_delete` int(255) NOT NULL,
  `category_all` int(255) NOT NULL,
  `brand_view` int(255) NOT NULL,
  `brand_create` int(255) NOT NULL,
  `brand_edit` int(255) NOT NULL,
  `brand_delete` int(255) NOT NULL,
  `brand_all` int(255) NOT NULL,
  `location_view` int(255) NOT NULL,
  `location_create` int(255) NOT NULL,
  `location_edit` int(255) NOT NULL,
  `location_delete` int(255) NOT NULL,
  `location_all` int(255) NOT NULL,
  `request_view` int(255) NOT NULL,
  `request_create` int(255) NOT NULL,
  `request_approval` int(255) NOT NULL,
  `request_all` int(255) NOT NULL,
  `inventory_return_view` int(255) NOT NULL,
  `inventory_return_edit` int(255) NOT NULL,
  `inventory_return_delete` int(255) NOT NULL,
  `inventory_return_all` int(255) NOT NULL,
  `user_view` int(255) NOT NULL,
  `user_create` int(255) NOT NULL,
  `user_edit` int(255) NOT NULL,
  `user_ban` int(255) NOT NULL,
  `user_delete` int(255) NOT NULL,
  `user_all` int(255) NOT NULL,
  `group_view` int(255) NOT NULL,
  `group_create` int(255) NOT NULL,
  `group_edit` int(255) NOT NULL,
  `group_delete` int(255) NOT NULL,
  `group_all` int(255) NOT NULL,
  `hide` int(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `role-list`
--

INSERT INTO `role-list` (`id`, `role-id`, `role`, `role_description`, `inventory_view`, `inventory_create`, `inventory_edit`, `inventory_delete`, `inventory_all`, `import_create`, `category_view`, `category_create`, `category_edit`, `category_delete`, `category_all`, `brand_view`, `brand_create`, `brand_edit`, `brand_delete`, `brand_all`, `location_view`, `location_create`, `location_edit`, `location_delete`, `location_all`, `request_view`, `request_create`, `request_approval`, `request_all`, `inventory_return_view`, `inventory_return_edit`, `inventory_return_delete`, `inventory_return_all`, `user_view`, `user_create`, `user_edit`, `user_ban`, `user_delete`, `user_all`, `group_view`, `group_create`, `group_edit`, `group_delete`, `group_all`, `hide`) VALUES
(1, 'x9Mj5GmOGz', 'Admin', 'Master Admin', 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0),
(2, 'UzZhqYA3OJ', 'User', 'Normal User', 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);

-- --------------------------------------------------------

--
-- Table structure for table `user-list`
--

CREATE TABLE `user-list` (
  `id` int(255) NOT NULL,
  `acc_id` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `designation` varchar(255) NOT NULL,
  `department` varchar(255) NOT NULL,
  `description` varchar(999) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `user-list`
--

INSERT INTO `user-list` (`id`, `acc_id`, `email`, `name`, `designation`, `department`, `description`) VALUES
(1, 'cIvf72h28P', '<EMAIL>', 'Alex Ang', 'Software Engineer', 'Software Team', ''),
(2, 'LFuUInMStl', '<EMAIL>', 'Firdaus Baharin', 'Service Delivery Manager', 'Technical Department', ''),
(3, 'snyajRZeBK', '<EMAIL>', 'Alan Ang', 'Lead System Engineer', 'Technical Department', ''),
(4, 'ed0OIAZ8ne', '<EMAIL>', 'Jason Lim', 'Director, Technical', 'Technical Department', ''),
(5, 'lOoY8fXCUX', '<EMAIL>', 'KK Chong', 'Software Sales Director', 'Software Team', ''),
(6, 'ylmh96zgPV', '<EMAIL>', ' Mudzaffar Haniffuddin', 'Senior Procurement Executive', 'Finance Department', '');

-- --------------------------------------------------------

--
-- Table structure for table `useracc`
--

CREATE TABLE `useracc` (
  `id` int(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `acc_id` varchar(255) NOT NULL,
  `token` varchar(255) NOT NULL,
  `otp` varchar(255) NOT NULL,
  `expired` varchar(255) NOT NULL,
  `ban` int(255) NOT NULL,
  `attempt` int(255) NOT NULL,
  `role_id` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `useracc`
--

INSERT INTO `useracc` (`id`, `email`, `acc_id`, `token`, `otp`, `expired`, `ban`, `attempt`, `role_id`) VALUES
(1, '<EMAIL>', 'cIvf72h28P', '', '', '', 0, 0, 'x9Mj5GmOGz'),
(2, '<EMAIL>', 'LFuUInMStl', '', '', '', 0, 0, 'x9Mj5GmOGz'),
(3, '<EMAIL>', 'snyajRZeBK', '', '', '', 0, 0, 'x9Mj5GmOGz'),
(4, '<EMAIL>', 'ed0OIAZ8ne', '', '', '', 0, 0, 'x9Mj5GmOGz'),
(5, '<EMAIL>', 'lOoY8fXCUX', '', '', '', 0, 0, 'x9Mj5GmOGz'),
(6, '<EMAIL>', 'ylmh96zgPV', '', '', '', 0, 0, 'x9Mj5GmOGz');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `brand-list`
--
ALTER TABLE `brand-list`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `category-list`
--
ALTER TABLE `category-list`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `inventory-list`
--
ALTER TABLE `inventory-list`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `location-list`
--
ALTER TABLE `location-list`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `notification`
--
ALTER TABLE `notification`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `request-list`
--
ALTER TABLE `request-list`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `role-list`
--
ALTER TABLE `role-list`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `user-list`
--
ALTER TABLE `user-list`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `useracc`
--
ALTER TABLE `useracc`
  ADD PRIMARY KEY (`id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `brand-list`
--
ALTER TABLE `brand-list`
  MODIFY `id` int(255) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=14;

--
-- AUTO_INCREMENT for table `category-list`
--
ALTER TABLE `category-list`
  MODIFY `id` int(255) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `inventory-list`
--
ALTER TABLE `inventory-list`
  MODIFY `id` int(255) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=16;

--
-- AUTO_INCREMENT for table `location-list`
--
ALTER TABLE `location-list`
  MODIFY `id` int(255) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `notification`
--
ALTER TABLE `notification`
  MODIFY `id` int(255) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `request-list`
--
ALTER TABLE `request-list`
  MODIFY `id` int(255) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `role-list`
--
ALTER TABLE `role-list`
  MODIFY `id` int(255) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `user-list`
--
ALTER TABLE `user-list`
  MODIFY `id` int(255) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `useracc`
--
ALTER TABLE `useracc`
  MODIFY `id` int(255) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
