 <!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0">
<meta name="description" content="POS - Bootstrap Admin Template">
<meta name="keywords" content="admin, estimates, bootstrap, business, corporate, creative, management, minimal, modern,  html5, responsive">
<meta name="author" content="Dreamguys - Bootstrap Admin Template">
<meta name="robots" content="noindex, nofollow">
<title>Dreams Pos admin template</title>

<link rel="shortcut icon" type="image/x-icon" href="assets/img/favicon.png">

<link rel="stylesheet" href="assets/css/bootstrap.min.css">

<link rel="stylesheet" href="assets/css/animate.css">

<link rel="stylesheet" href="assets/css/dataTables.bootstrap4.min.css">

<link rel="stylesheet" href="assets/plugins/fontawesome/css/fontawesome.min.css">
<link rel="stylesheet" href="assets/plugins/fontawesome/css/all.min.css">

<link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
<div id="global-loader">
<div class="whirly-loader"> </div>
</div>

<div class="main-wrapper">

<div class="header">

<div class="header-left active">
<a href="index.html" class="logo">
<img src="assets/img/logo.png" alt="">
</a>
<a href="index.html" class="logo-small">
<img src="assets/img/logo-small.png" alt="">
</a>
<a id="toggle_btn" href="javascript:void(0);">
</a>
</div>

<a id="mobile_btn" class="mobile_btn" href="#sidebar">
<span class="bar-icon">
<span></span>
<span></span>
<span></span>
</span>
</a>

<ul class="nav user-menu">

<li class="nav-item">
<div class="top-nav-search">
<a href="javascript:void(0);" class="responsive-search">
<i class="fa fa-search"></i>
</a>
<form action="#">
<div class="searchinputs">
<input type="text" placeholder="Search Here ...">
<div class="search-addon">
<span><img src="assets/img/icons/closes.svg" alt="img"></span>
</div>
</div>
<a class="btn" id="searchdiv"><img src="assets/img/icons/search.svg" alt="img"></a>
</form>
</div>
</li>


<li class="nav-item dropdown has-arrow flag-nav">
<a class="nav-link dropdown-toggle" data-bs-toggle="dropdown" href="javascript:void(0);" role="button">
<img src="assets/img/flags/us1.png" alt="" height="20">
</a>
<div class="dropdown-menu dropdown-menu-right">
<a href="javascript:void(0);" class="dropdown-item">
<img src="assets/img/flags/us.png" alt="" height="16"> English
</a>
<a href="javascript:void(0);" class="dropdown-item">
<img src="assets/img/flags/fr.png" alt="" height="16"> French
</a>
<a href="javascript:void(0);" class="dropdown-item">
<img src="assets/img/flags/es.png" alt="" height="16"> Spanish
</a>
<a href="javascript:void(0);" class="dropdown-item">
<img src="assets/img/flags/de.png" alt="" height="16"> German
</a>
</div>
</li>


<li class="nav-item dropdown">
<a href="javascript:void(0);" class="dropdown-toggle nav-link" data-bs-toggle="dropdown">
<img src="assets/img/icons/notification-bing.svg" alt="img"> <span class="badge rounded-pill">4</span>
</a>
<div class="dropdown-menu notifications">
<div class="topnav-dropdown-header">
<span class="notification-title">Notifications</span>
<a href="javascript:void(0)" class="clear-noti"> Clear All </a>
</div>
<div class="noti-content">
<ul class="notification-list">
<li class="notification-message">
<a href="activities.html">
<div class="media d-flex">
<span class="avatar flex-shrink-0">
<img alt="" src="assets/img/profiles/avatar-02.jpg">
</span>
<div class="media-body flex-grow-1">
<p class="noti-details"><span class="noti-title">John Doe</span> added new task <span class="noti-title">Patient appointment booking</span></p>
<p class="noti-time"><span class="notification-time">4 mins ago</span></p>
</div>
</div>
</a>
</li>
<li class="notification-message">
<a href="activities.html">
<div class="media d-flex">
<span class="avatar flex-shrink-0">
<img alt="" src="assets/img/profiles/avatar-03.jpg">
</span>
<div class="media-body flex-grow-1">
<p class="noti-details"><span class="noti-title">Tarah Shropshire</span> changed the task name <span class="noti-title">Appointment booking with payment gateway</span></p>
<p class="noti-time"><span class="notification-time">6 mins ago</span></p>
</div>
</div>
</a>
</li>
<li class="notification-message">
<a href="activities.html">
<div class="media d-flex">
<span class="avatar flex-shrink-0">
<img alt="" src="assets/img/profiles/avatar-06.jpg">
</span>
<div class="media-body flex-grow-1">
<p class="noti-details"><span class="noti-title">Misty Tison</span> added <span class="noti-title">Domenic Houston</span> and <span class="noti-title">Claire Mapes</span> to project <span class="noti-title">Doctor available module</span></p>
<p class="noti-time"><span class="notification-time">8 mins ago</span></p>
</div>
</div>
</a>
</li>
<li class="notification-message">
<a href="activities.html">
<div class="media d-flex">
<span class="avatar flex-shrink-0">
<img alt="" src="assets/img/profiles/avatar-17.jpg">
</span>
<div class="media-body flex-grow-1">
<p class="noti-details"><span class="noti-title">Rolland Webber</span> completed task <span class="noti-title">Patient and Doctor video conferencing</span></p>
<p class="noti-time"><span class="notification-time">12 mins ago</span></p>
</div>
</div>
</a>
</li>
<li class="notification-message">
<a href="activities.html">
<div class="media d-flex">
<span class="avatar flex-shrink-0">
<img alt="" src="assets/img/profiles/avatar-13.jpg">
</span>
<div class="media-body flex-grow-1">
<p class="noti-details"><span class="noti-title">Bernardo Galaviz</span> added new task <span class="noti-title">Private chat module</span></p>
<p class="noti-time"><span class="notification-time">2 days ago</span></p>
</div>
</div>
</a>
</li>
</ul>
</div>
<div class="topnav-dropdown-footer">
<a href="activities.html">View all Notifications</a>
</div>
</div>
</li>

<li class="nav-item dropdown has-arrow main-drop">
<a href="javascript:void(0);" class="dropdown-toggle nav-link userset" data-bs-toggle="dropdown">
<span class="user-img"><img src="assets/img/profiles/avator1.jpg" alt="">
<span class="status online"></span></span>
</a>
<div class="dropdown-menu menu-drop-user">
<div class="profilename">
<div class="profileset">
<span class="user-img"><img src="assets/img/profiles/avator1.jpg" alt="">
<span class="status online"></span></span>
<div class="profilesets">
<h6>John Doe</h6>
<h5>Admin</h5>
</div>
</div>
<hr class="m-0">
<a class="dropdown-item" href="profile.html"> <i class="me-2" data-feather="user"></i> My Profile</a>
<a class="dropdown-item" href="generalsettings.html"><i class="me-2" data-feather="settings"></i>Settings</a>
<hr class="m-0">
<a class="dropdown-item logout pb-0" href="signin.html"><img src="assets/img/icons/log-out.svg" class="me-2" alt="img">Logout</a>
</div>
</div>
</li>
</ul>


<div class="dropdown mobile-user-menu">
<a href="javascript:void(0);" class="nav-link dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false"><i class="fa fa-ellipsis-v"></i></a>
<div class="dropdown-menu dropdown-menu-right">
<a class="dropdown-item" href="profile.html">My Profile</a>
<a class="dropdown-item" href="generalsettings.html">Settings</a>
<a class="dropdown-item" href="signin.html">Logout</a>
</div>
</div>

</div>


<div class="sidebar" id="sidebar">
<div class="sidebar-inner slimscroll">
<div id="sidebar-menu" class="sidebar-menu">
<ul>
<li>
<a href="index.html"><img src="assets/img/icons/dashboard.svg" alt="img"><span> Dashboard</span> </a>
</li>
<li class="submenu">
<a href="javascript:void(0);"><img src="assets/img/icons/product.svg" alt="img"><span> Product</span> <span class="menu-arrow"></span></a>
<ul>
<li><a href="productlist.html">Product List</a></li>
<li><a href="addproduct.html">Add Product</a></li>
<li><a href="categorylist.html">Category List</a></li>
<li><a href="addcategory.html">Add Category</a></li>
<li><a href="subcategorylist.html">Sub Category List</a></li>
<li><a href="subaddcategory.html">Add Sub Category</a></li>
<li><a href="brandlist.html">Brand List</a></li>
<li><a href="addbrand.html">Add Brand</a></li>
<li><a href="importproduct.html">Import Products</a></li>
<li><a href="barcode.html">Print Barcode</a></li>
</ul>
</li>
<li class="submenu">
<a href="javascript:void(0);"><img src="assets/img/icons/sales1.svg" alt="img"><span> Sales</span> <span class="menu-arrow"></span></a>
<ul>
<li><a href="saleslist.html">Sales List</a></li>
<li><a href="pos.html">POS</a></li>
<li><a href="pos.html">New Sales</a></li>
<li><a href="salesreturnlists.html">Sales Return List</a></li>
<li><a href="createsalesreturns.html">New Sales Return</a></li>
</ul>
</li>
<li class="submenu">
<a href="javascript:void(0);"><img src="assets/img/icons/purchase1.svg" alt="img"><span> Purchase</span> <span class="menu-arrow"></span></a>
<ul>
<li><a href="purchaselist.html">Purchase List</a></li>
<li><a href="addpurchase.html">Add Purchase</a></li>
<li><a href="importpurchase.html">Import Purchase</a></li>
</ul>
</li>
<li class="submenu">
<a href="javascript:void(0);"><img src="assets/img/icons/expense1.svg" alt="img"><span> Expense</span> <span class="menu-arrow"></span></a>
<ul>
<li><a href="expenselist.html">Expense List</a></li>
<li><a href="createexpense.html">Add Expense</a></li>
<li><a href="expensecategory.html">Expense Category</a></li>
</ul>
</li>  <li class="submenu">
<a href="javascript:void(0);"><img src="assets/img/icons/quotation1.svg" alt="img"><span> Quotation</span> <span class="menu-arrow"></span></a>
<ul>
<li><a href="quotationList.html">Quotation List</a></li>
<li><a href="addquotation.html">Add Quotation</a></li>
</ul>
</li>
<li class="submenu">
<a href="javascript:void(0);"><img src="assets/img/icons/transfer1.svg" alt="img"><span> Transfer</span> <span class="menu-arrow"></span></a>
<ul>
<li><a href="transferlist.html">Transfer List</a></li>
<li><a href="addtransfer.html">Add Transfer </a></li>
<li><a href="importtransfer.html">Import Transfer </a></li>
</ul>
</li>
<li class="submenu">
<a href="javascript:void(0);"><img src="assets/img/icons/return1.svg" alt="img"><span> Return</span> <span class="menu-arrow"></span></a>
<ul>
<li><a href="salesreturnlist.html">Sales Return List</a></li>
<li><a href="createsalesreturn.html">Add Sales Return </a></li>
<li><a href="purchasereturnlist.html">Purchase Return List</a></li>
<li><a href="createpurchasereturn.html">Add Purchase Return </a></li>
</ul>
</li>
<li class="submenu">
<a href="javascript:void(0);"><img src="assets/img/icons/users1.svg" alt="img"><span> People</span> <span class="menu-arrow"></span></a>
<ul>
<li><a href="customerlist.html">Customer List</a></li>
<li><a href="addcustomer.html">Add Customer </a></li>
<li><a href="supplierlist.html">Supplier List</a></li>
<li><a href="addsupplier.html">Add Supplier </a></li>
<li><a href="userlist.html">User List</a></li>
<li><a href="adduser.html">Add User</a></li>
<li><a href="storelist.html">Store List</a></li>
<li><a href="addstore.html">Add Store</a></li>
</ul>
</li>
<li class="submenu">
<a href="javascript:void(0);"><img src="assets/img/icons/places.svg" alt="img"><span> Places</span> <span class="menu-arrow"></span></a>
<ul>
<li><a href="newcountry.html">New Country</a></li>
<li><a href="countrieslist.html">Countries list</a></li>
<li><a href="newstate.html">New State </a></li>
<li><a href="statelist.html">State list</a></li>
</ul>
</li>
<li class="active">
<a href="components.html"><i data-feather="layers"></i><span> Components</span> </a>
</li>
<li>
<a href="blankpage.html"><i data-feather="file"></i><span> Blank Page</span> </a>
</li>
<li class="submenu">
<a href="javascript:void(0);"><i data-feather="alert-octagon"></i> <span> Error Pages </span> <span class="menu-arrow"></span></a>
<ul>
<li><a href="error-404.html">404 Error </a></li>
<li><a href="error-500.html">500 Error </a></li>
</ul>
</li>
<li class="submenu">
<a href="javascript:void(0);"><i data-feather="box"></i> <span>Elements </span> <span class="menu-arrow"></span></a>
<ul>
<li><a href="sweetalerts.html">Sweet Alerts</a></li>
<li><a href="tooltip.html">Tooltip</a></li>
<li><a href="popover.html">Popover</a></li>
<li><a href="ribbon.html">Ribbon</a></li>
<li><a href="clipboard.html">Clipboard</a></li>
<li><a href="drag-drop.html">Drag & Drop</a></li>
<li><a href="rangeslider.html">Range Slider</a></li>
<li><a href="rating.html">Rating</a></li>
<li><a href="toastr.html">Toastr</a></li>
<li><a href="text-editor.html">Text Editor</a></li>
<li><a href="counter.html">Counter</a></li>
<li><a href="scrollbar.html">Scrollbar</a></li>
<li><a href="spinner.html">Spinner</a></li>
<li><a href="notification.html">Notification</a></li>
<li><a href="lightbox.html">Lightbox</a></li>
<li><a href="stickynote.html">Sticky Note</a></li>
<li><a href="timeline.html">Timeline</a></li>
<li><a href="form-wizard.html">Form Wizard</a></li>
</ul>
</li>
<li class="submenu">
<a href="javascript:void(0);"><i data-feather="bar-chart-2"></i> <span> Charts </span> <span class="menu-arrow"></span></a>
<ul>
<li><a href="chart-apex.html">Apex Charts</a></li>
<li><a href="chart-js.html">Chart Js</a></li>
<li><a href="chart-morris.html">Morris Charts</a></li>
<li><a href="chart-flot.html">Flot Charts</a></li>
<li><a href="chart-peity.html">Peity Charts</a></li>
</ul>
</li>
<li class="submenu">
<a href="javascript:void(0);"><i data-feather="award"></i><span> Icons </span> <span class="menu-arrow"></span></a>
<ul>
<li><a href="icon-fontawesome.html">Fontawesome Icons</a></li>
<li><a href="icon-feather.html">Feather Icons</a></li>
<li><a href="icon-ionic.html">Ionic Icons</a></li>
<li><a href="icon-material.html">Material Icons</a></li>
<li><a href="icon-pe7.html">Pe7 Icons</a></li>
<li><a href="icon-simpleline.html">Simpleline Icons</a></li>
<li><a href="icon-themify.html">Themify Icons</a></li>
<li><a href="icon-weather.html">Weather Icons</a></li>
<li><a href="icon-typicon.html">Typicon Icons</a></li>
<li><a href="icon-flag.html">Flag Icons</a></li>
</ul>
</li>
<li class="submenu">
<a href="javascript:void(0);"><i data-feather="columns"></i> <span> Forms </span> <span class="menu-arrow"></span></a>
<ul>
<li><a href="form-basic-inputs.html">Basic Inputs </a></li>
<li><a href="form-input-groups.html">Input Groups </a></li>
<li><a href="form-horizontal.html">Horizontal Form </a></li>
<li><a href="form-vertical.html"> Vertical Form </a></li>
<li><a href="form-mask.html">Form Mask </a></li>
<li><a href="form-validation.html">Form Validation </a></li>
<li><a href="form-select2.html">Form Select2 </a></li>
<li><a href="form-fileupload.html">File Upload </a></li>
</ul>
</li>
<li class="submenu">
<a href="javascript:void(0);"><i data-feather="layout"></i> <span> Table </span> <span class="menu-arrow"></span></a>
<ul>
<li><a href="tables-basic.html">Basic Tables </a></li>
<li><a href="data-tables.html">Data Table </a></li>
</ul>
</li>
<li class="submenu">
<a href="javascript:void(0);"><img src="assets/img/icons/product.svg" alt="img"><span> Application</span> <span class="menu-arrow"></span></a>
<ul>
<li><a href="chat.html">Chat</a></li>
<li><a href="calendar.html">Calendar</a></li>
<li><a href="email.html">Email</a></li>
</ul>
</li>
<li class="submenu">
<a href="javascript:void(0);"><img src="assets/img/icons/time.svg" alt="img"><span> Report</span> <span class="menu-arrow"></span></a>
<ul>
<li><a href="purchaseorderreport.html">Purchase order report</a></li>
<li><a href="inventoryreport.html">Inventory Report</a></li>
<li><a href="salesreport.html">Sales Report</a></li>
<li><a href="invoicereport.html">Invoice Report</a></li>
<li><a href="purchasereport.html">Purchase Report</a></li>
<li><a href="supplierreport.html">Supplier Report</a></li>
<li><a href="customerreport.html">Customer Report</a></li>
</ul>
</li>
<li class="submenu">
<a href="javascript:void(0);"><img src="assets/img/icons/users1.svg" alt="img"><span> Users</span> <span class="menu-arrow"></span></a>
<ul>
<li><a href="newuser.html">New User </a></li>
<li><a href="userlists.html">Users List</a></li>
</ul>
</li>
<li class="submenu">
<a href="javascript:void(0);"><img src="assets/img/icons/settings.svg" alt="img"><span> Settings</span> <span class="menu-arrow"></span></a>
<ul>
<li><a href="generalsettings.html">General Settings</a></li>
<li><a href="emailsettings.html">Email Settings</a></li>
<li><a href="paymentsettings.html">Payment Settings</a></li>
<li><a href="currencysettings.html">Currency Settings</a></li>
<li><a href="grouppermissions.html">Group Permissions</a></li>
<li><a href="taxrates.html">Tax Rates</a></li>
</ul>
</li>
</ul>
</div>
</div>
</div>

<div class="page-wrapper">
<div class="content">
<div class="page-header">
<div class="page-title">
<h4>Components</h4>
</div>
</div>
<div class="comp-sec-wrapper">

<section class="comp-section">
<div class="section-header">
<h3 class="section-title">Avatar</h3>
<div class="line"></div>
</div>
<div class="row">
<div class="col-md-12">
<div class="card bg-white">
<div class="card-header">
<h5 class="card-title">Sizing</h5>
</div>
<div class="card-body">
<div class="avatar avatar-xxl">
<img class="avatar-img rounded-circle" alt="User Image" src="assets/img/profiles/avatar-02.jpg">
</div>
<div class="avatar avatar-xl">
<img class="avatar-img rounded-circle" alt="User Image" src="assets/img/profiles/avatar-02.jpg">
</div>
<div class="avatar avatar-lg">
<img class="avatar-img rounded-circle" alt="User Image" src="assets/img/profiles/avatar-02.jpg">
</div>
<div class="avatar">
<img class="avatar-img rounded-circle" alt="User Image" src="assets/img/profiles/avatar-02.jpg">
</div>
<div class="avatar avatar-xs">
<img class="avatar-img rounded-circle" alt="User Image" src="assets/img/profiles/avatar-02.jpg">
</div>
</div>
</div>
</div>
<div class="col-md-12">
<div class="card bg-white">
<div class="card-header">
<h5 class="card-title">Avatar With Status</h5>
</div>
<div class="card-body">
<div class="avatar avatar-online">
<img class="avatar-img rounded-circle" alt="User Image" src="assets/img/profiles/avatar-02.jpg">
</div>
<div class="avatar avatar-offline">
<img class="avatar-img rounded-circle" alt="User Image" src="assets/img/profiles/avatar-02.jpg">
</div>
<div class="avatar avatar-away">
<img class="avatar-img rounded-circle" alt="User Image" src="assets/img/profiles/avatar-02.jpg">
</div>
</div>
</div>
</div>
<div class="col-md-12">
<div class="card bg-white">
<div class="card-header">
<h5 class="card-title">Shape</h5>
</div>
<div class="card-body">
<div class="avatar">
<img class="avatar-img rounded" alt="User Image" src="assets/img/profiles/avatar-02.jpg">
</div>
<div class="avatar">
<img class="avatar-img rounded-circle" alt="User Image" src="assets/img/profiles/avatar-02.jpg">
</div>
</div>
</div>
</div>
<div class="col-md-12">
<div class="card bg-white">
<div class="card-header">
<h5 class="card-title">Group</h5>
</div>
<div class="card-body">
<div class="avatar-group">
<div class="avatar">
<img class="avatar-img rounded-circle border border-white" alt="User Image" src="assets/img/profiles/avatar-02.jpg">
</div>
<div class="avatar">
<img class="avatar-img rounded-circle border border-white" alt="User Image" src="assets/img/profiles/avatar-03.jpg">
</div>
<div class="avatar">
<img class="avatar-img rounded-circle border border-white" alt="User Image" src="assets/img/profiles/avatar-04.jpg">
</div>
<div class="avatar">
<span class="avatar-title rounded-circle border border-white">CF</span>
</div>
</div>
</div>
</div>
</div>
</div>
</section>


<section class="comp-section">
<div class="section-header">
<h3 class="section-title">Alerts</h3>
<div class="line"></div>
</div>
<div class="card bg-white">
<div class="card-body">
<div class="alert alert-primary alert-dismissible fade show" role="alert">
<strong>Holy guacamole!</strong> You should check in on some of those fields below.
<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
<div class="alert alert-secondary alert-dismissible fade show" role="alert">
<strong>Holy guacamole!</strong> You should check in on some of those fields below.
<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
<div class="alert alert-success alert-dismissible fade show" role="alert">
<strong>Holy guacamole!</strong> You should check in on some of those fields below.
<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
<strong>Holy guacamole!</strong> You should check in on some of those fields below.
<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
<div class="alert alert-warning alert-dismissible fade show" role="alert">
<strong>Holy guacamole!</strong> You should check in on some of those fields below.
<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
<div class="alert alert-info alert-dismissible fade show" role="alert">
<strong>Holy guacamole!</strong> You should check in on some of those fields below.
<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
<div class="alert alert-light alert-dismissible fade show" role="alert">
<strong>Holy guacamole!</strong> You should check in on some of those fields below.
<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
<div class="alert alert-dark alert-dismissible fade show" role="alert">
<strong>Holy guacamole!</strong> You should check in on some of those fields below.
<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
</div>
</div>
</section>


<section class="comp-section">
<div class="section-header">
<h3 class="section-title">Breadcrumbs</h3>
<div class="line"></div>
</div>
<div class="card bg-white">
<div class="card-body">
<nav aria-label="breadcrumb">
<ol class="breadcrumb">
<li class="breadcrumb-item active" aria-current="page">Home</li>
 </ol>
</nav>
<nav aria-label="breadcrumb">
<ol class="breadcrumb">
<li class="breadcrumb-item"><a href="javascript:void(0);">Home</a></li>
<li class="breadcrumb-item active" aria-current="page">Products</li>
</ol>
</nav>
<nav aria-label="breadcrumb">
<ol class="breadcrumb mb-0">
<li class="breadcrumb-item"><a href="javascript:void(0);">Home</a></li>
<li class="breadcrumb-item"><a href="javascript:void(0);">Products</a></li>
<li class="breadcrumb-item active" aria-current="page">Accessories</li>
</ol>
</nav>
</div>
</div>
</section>


<section class="comp-section comp-buttons">
<div class="section-header">
<h3 class="section-title">Buttons</h3>
<div class="line"></div>
</div>
<div class="card bg-white">
<div class="card-body">
<h5 class="card-title">Default Button</h5>
<button type="button" class="btn btn-primary">Primary</button>
<button type="button" class="btn btn-secondary">Secondary</button>
<button type="button" class="btn btn-success">Success</button>
<button type="button" class="btn btn-danger">Danger</button>
<button type="button" class="btn btn-warning">Warning</button>
<button type="button" class="btn btn-info">Info</button>
<button type="button" class="btn btn-light">Light</button>
<button type="button" class="btn btn-dark">Dark</button>
<button type="button" class="btn btn-link">Link</button>
<hr>
<h5 class="card-title">Button Sizes</h5>
<p>
<button type="button" class="btn btn-primary btn-lg">Primary</button>
<button type="button" class="btn btn-secondary btn-lg">Secondary</button>
<button type="button" class="btn btn-success btn-lg">Success</button>
<button type="button" class="btn btn-danger btn-lg">Danger</button>
<button type="button" class="btn btn-warning btn-lg">Warning</button>
<button type="button" class="btn btn-info btn-lg">Info</button>
<button type="button" class="btn btn-light btn-lg">Light</button>
<button type="button" class="btn btn-dark btn-lg">Dark</button>
</p>
<p>
<button type="button" class="btn btn-primary">Primary</button>
<button type="button" class="btn btn-secondary">Secondary</button>
<button type="button" class="btn btn-success">Success</button>
<button type="button" class="btn btn-danger">Danger</button>
<button type="button" class="btn btn-warning">Warning</button>
<button type="button" class="btn btn-info">Info</button>
<button type="button" class="btn btn-light">Light</button>
<button type="button" class="btn btn-dark">Dark</button>
</p>
<p>
<button type="button" class="btn btn-primary btn-sm">Primary</button>
<button type="button" class="btn btn-secondary btn-sm">Secondary</button>
<button type="button" class="btn btn-success btn-sm">Success</button>
<button type="button" class="btn btn-danger btn-sm">Danger</button>
<button type="button" class="btn btn-warning btn-sm">Warning</button>
<button type="button" class="btn btn-info btn-sm">Info</button>
<button type="button" class="btn btn-light btn-sm">Light</button>
<button type="button" class="btn btn-dark btn-sm">Dark</button>
</p>
<hr>
<h5 class="card-title">Button Groups</h5>
<div class="btn-toolbar">
<div class="btn-group btn-group-lg">
<button type="button" class="btn btn-primary">Left</button>
<button type="button" class="btn btn-primary">Middle</button> 
<button type="button" class="btn btn-primary">Right</button>
</div>
</div>
<br>
<div class="btn-toolbar">
<div class="btn-group">
<button type="button" class="btn btn-primary">Left</button>
<button type="button" class="btn btn-primary">Middle</button>
<button type="button" class="btn btn-primary">Right</button>
</div>
</div>
<br>
<div class="btn-toolbar">
<div class="btn-group btn-group-sm">
<button type="button" class="btn btn-primary">Left</button>
<button type="button" class="btn btn-primary">Middle</button>
<button type="button" class="btn btn-primary">Right</button>
</div>
</div>
</div>
</div>

<div class="card bg-white">
<div class="card-header">
<h5 class="card-title">Rounded Button</h5>
<p class="card-text">use <code>.btn-rounded</code> in class <code>.btn</code> class to get Rounded button</p>
</div>
<div class="card-body">
<button type="button" class="btn btn-rounded btn-primary">Primary</button>
<button type="button" class="btn btn-rounded btn-secondary">Secondary</button>
<button type="button" class="btn btn-rounded btn-success">Success</button>
<button type="button" class="btn btn-rounded btn-danger">Danger</button>
<button type="button" class="btn btn-rounded btn-warning">Warning</button>
<button type="button" class="btn btn-rounded btn-info">Info</button>
<button type="button" class="btn btn-rounded btn-light">Light</button>
<button type="button" class="btn btn-rounded btn-dark">Dark</button>
<hr>
<p>use <code>.btn-rounded</code> in class <code>.btn-outline-*</code> class to get Rounded Outline button</p>
<button type="button" class="btn btn-rounded btn-outline-primary">Primary</button>
<button type="button" class="btn btn-rounded btn-outline-secondary">Secondary</button>
<button type="button" class="btn btn-rounded btn-outline-success">Success</button>
<button type="button" class="btn btn-rounded btn-outline-danger">Danger</button>
<button type="button" class="btn btn-rounded btn-outline-warning">Warning</button>
<button type="button" class="btn btn-rounded btn-outline-info">Info</button>
<button type="button" class="btn btn-rounded btn-outline-light">Light</button>
<button type="button" class="btn btn-rounded btn-outline-dark">Dark</button>
</div>
</div>


<div class="card bg-white">
<div class="card-header">
<h5 class="card-title">Outline Buttons</h5>
<p class="card-text">Use <code>.btn-outline-*</code> class for outline buttons.</p>
</div>
<div class="card-body">
<div class="row row-sm align-items-center">
<div class="col-12 col-xl mb-3 mb-xl-0">Normal</div>
<div class="col-6 col-sm-4 col-md-2 col-xl mb-3 mb-xl-0">
<button type="button" class="btn btn-block btn-outline-primary">Primary</button>
</div>
<div class="col-6 col-sm-4 col-md-2 col-xl mb-3 mb-xl-0">
<button type="button" class="btn btn-block btn-outline-secondary">Secondary</button>
</div>
<div class="col-6 col-sm-4 col-md-2 col-xl mb-3 mb-xl-0">
<button type="button" class="btn btn-block btn-outline-success">Success</button>
</div>
<div class="col-6 col-sm-4 col-md-2 col-xl mb-3 mb-xl-0">
<button type="button" class="btn btn-block btn-outline-warning">Warning</button>
</div>
<div class="col-6 col-sm-4 col-md-2 col-xl mb-3 mb-xl-0">
<button type="button" class="btn btn-block btn-outline-danger">Danger</button>
</div>
<div class="col-6 col-sm-4 col-md-2 col-xl mb-3 mb-xl-0">
<button type="button" class="btn btn-block btn-outline-info">Info</button>
</div>
<div class="col-6 col-sm-4 col-md-2 col-xl mb-3 mb-xl-0">
<button type="button" class="btn btn-block btn-outline-light">Light</button>
</div>
<div class="col-6 col-sm-4 col-md-2 col-xl mb-3 mb-xl-0">
<button type="button" class="btn btn-block btn-outline-dark">Dark</button>
</div>
</div>
<div class="row row-sm align-items-center mt-3">
<div class="col-12 col-xl mb-3 mb-xl-0">Active</div>
<div class="col-6 col-sm-4 col-md-2 col-xl mb-3 mb-xl-0">
<button type="button" class="btn btn-block btn-outline-primary active">Primary</button>
</div>
<div class="col-6 col-sm-4 col-md-2 col-xl mb-3 mb-xl-0">
<button type="button" class="btn btn-block btn-outline-secondary active">Secondary</button>
</div>
<div class="col-6 col-sm-4 col-md-2 col-xl mb-3 mb-xl-0">
<button type="button" class="btn btn-block btn-outline-success active">Success</button>
</div>
<div class="col-6 col-sm-4 col-md-2 col-xl mb-3 mb-xl-0">
<button type="button" class="btn btn-block btn-outline-warning active">Warning</button>
</div>
<div class="col-6 col-sm-4 col-md-2 col-xl mb-3 mb-xl-0">
<button type="button" class="btn btn-block btn-outline-danger active">Danger</button>
</div>
<div class="col-6 col-sm-4 col-md-2 col-xl mb-3 mb-xl-0">
<button type="button" class="btn btn-block btn-outline-info active">Info</button>
</div>
<div class="col-6 col-sm-4 col-md-2 col-xl mb-3 mb-xl-0">
<button type="button" class="btn btn-block btn-outline-light active">Light</button>
</div>
<div class="col-6 col-sm-4 col-md-2 col-xl mb-3 mb-xl-0">
<button type="button" class="btn btn-block btn-outline-dark active">Dark</button>
</div>
</div>
<div class="row row-sm align-items-center mt-3">
<div class="col-12 col-xl mb-3 mb-xl-0">Disabled</div>
<div class="col-6 col-sm-4 col-md-2 col-xl mb-3 mb-xl-0">
<button disabled="" type="button" class="btn btn-block btn-outline-primary">Primary</button>
</div>
<div class="col-6 col-sm-4 col-md-2 col-xl mb-3 mb-xl-0">
<button disabled="" type="button" class="btn btn-block btn-outline-secondary">Secondary</button>
</div>
<div class="col-6 col-sm-4 col-md-2 col-xl mb-3 mb-xl-0">
<button disabled="" type="button" class="btn btn-block btn-outline-success">Success</button>
</div>
<div class="col-6 col-sm-4 col-md-2 col-xl mb-3 mb-xl-0">
<button disabled="" type="button" class="btn btn-block btn-outline-warning">Warning</button>
</div>
<div class="col-6 col-sm-4 col-md-2 col-xl mb-3 mb-xl-0">
<button disabled="" type="button" class="btn btn-block btn-outline-danger">Danger</button>
</div>
<div class="col-6 col-sm-4 col-md-2 col-xl mb-3 mb-xl-0">
<button disabled="" type="button" class="btn btn-block btn-outline-info">Info</button>
</div>
<div class="col-6 col-sm-4 col-md-2 col-xl mb-3 mb-xl-0">
<button disabled="" type="button" class="btn btn-block btn-outline-light">Light</button>
</div>
<div class="col-6 col-sm-4 col-md-2 col-xl mb-3 mb-xl-0">
<button disabled="" type="button" class="btn btn-block btn-outline-dark">Dark</button>
</div>
</div>
</div>
</div>


<div class="card mb-4">
<div class="card-header">
<h5 class="card-title">Progress Button</h5>
</div>
<div class="card-body bg-white">
<button type="button" class="btn btn-primary"><span class="spinner-border spinner-border-sm me-2" role="status"></span>Primary</button>
<button type="button" class="btn btn-secondary"><span class="spinner-border spinner-border-sm me-2" role="status"></span>Secondary</button>
<button type="button" class="btn btn-success"><span class="spinner-border spinner-border-sm me-2" role="status"></span>Success</button>
<button type="button" class="btn btn-danger"><span class="spinner-border spinner-border-sm me-2" role="status"></span>Danger</button>
<button type="button" class="btn btn-warning"><span class="spinner-border spinner-border-sm me-2" role="status"></span>Warning</button>
<button type="button" class="btn btn-info"><span class="spinner-border spinner-border-sm me-2" role="status"></span>Info</button>
<button type="button" class="btn btn-dark"><span class="spinner-border spinner-border-sm me-2" role="status"></span>Dark</button>
</div>
</div>

</section>


<section class="comp-section comp-cards">
<div class="section-header">
<h3 class="section-title">Cards</h3>
<div class="line"></div>
</div>
<div class="row">
<div class="col-12 col-md-6 col-lg-4 d-flex">
<div class="card flex-fill bg-white">
<img alt="Card Image" src="assets/img/img-01.jpg" class="card-img-top">
<div class="card-header">
<h5 class="card-title mb-0">Card with image and links</h5>
</div>
<div class="card-body">
<p class="card-text">Some quick example text to build on the card title and make up the bulk of the card's content.</p>
<a class="card-link" href="javascript:void(0);">Card link</a>
<a class="card-link" href="javascript:void(0);">Another link</a>
</div>
</div>
</div>
<div class="col-12 col-md-6 col-lg-4 d-flex">
<div class="card flex-fill bg-white">
<img alt="Card Image" src="assets/img/img-01.jpg" class="card-img-top">
<div class="card-header">
<h5 class="card-title mb-0">Card with image and button</h5>
</div>
<div class="card-body">
<p class="card-text">Some quick example text to build on the card title and make up the bulk of the card's content.</p>
<a class="btn btn-primary" href="javascript:void(0);">Go somewhere</a>
</div>
</div>
</div>
<div class="col-12 col-md-6 col-lg-4 d-flex">
<div class="card flex-fill bg-white">
<img alt="Card Image" src="assets/img/img-01.jpg" class="card-img-top">
<div class="card-header">
<h5 class="card-title mb-0">Card with image and list</h5>
</div>
<ul class="list-group list-group-flush">
<li class="list-group-item">Cras justo odio</li>
<li class="list-group-item">Dapibus ac facilisis in</li>
<li class="list-group-item">Vestibulum at eros</li>
</ul>
</div>
</div>
</div>
<div class="row">
<div class="col-12 col-md-6 col-lg-4 d-flex">
<div class="card flex-fill bg-white">
<div class="card-header">
<h5 class="card-title mb-0">Card with links</h5>
</div>
<div class="card-body">
<p class="card-text">Some quick example text to build on the card title and make up the bulk of the card's content.</p>
<a class="card-link" href="javascript:void(0);">Card link</a>
<a class="card-link" href="javascript:void(0);">Another link</a>
</div>
 </div>
</div>
<div class="col-12 col-md-6 col-lg-4 d-flex">
<div class="card flex-fill bg-white">
<div class="card-header">
<h5 class="card-title mb-0">Card with button</h5>
</div>
<div class="card-body">
<p class="card-text">Some quick example text to build on the card title and make up the bulk of the card's content.</p>
<a class="btn btn-primary" href="javascript:void(0);">Go somewhere</a>
</div>
</div>
</div>
<div class="col-12 col-md-6 col-lg-4 d-flex">
<div class="card flex-fill bg-white">
<div class="card-header">
<h5 class="card-title mb-0">Card with list</h5>
</div>
<ul class="list-group list-group-flush">
<li class="list-group-item">Cras justo odio</li>
<li class="list-group-item">Dapibus ac facilisis in</li>
<li class="list-group-item">Vestibulum at eros</li>
</ul>
</div>
</div>
</div>
<div class="row">
<div class="col-12 col-md-6 col-lg-4 d-flex">
<div class="card flex-fill bg-white">
<div class="card-header">
This is my header
</div>
<div class="card-body">
<h5 class="card-title">Special title treatment</h5>
<p class="card-text">Some quick example text to build on the card title and make up the bulk of the card's content.</p>
</div>
<div class="card-footer text-muted">
This is my footer
</div>
</div>
</div>
<div class="col-12 col-md-6 col-lg-4 d-flex">
<div class="card flex-fill bg-white">
<div class="card-header">
<ul role="tablist" class="nav nav-tabs card-header-tabs float-right">
<li class="nav-item">
<a href="#tab-1" data-bs-toggle="tab" class="nav-link active">Active</a>
</li>
<li class="nav-item">
<a href="#tab-2" data-bs-toggle="tab" class="nav-link">Link</a>
</li>
<li class="nav-item">
<a href="#tab-3" data-bs-toggle="tab" class="nav-link disabled">Disabled</a>
</li>
</ul>
</div>
<div class="card-body">
<div class="tab-content pt-0">
<div role="tabpanel" id="tab-1" class="tab-pane fade show active">
<h5 class="card-title">Card with tabs</h5>
<p class="card-text">Some quick example text to build on the card title and make up the bulk of the card's content.</p>
<a class="btn btn-primary" href="javascript:void(0);">Go somewhere</a>
</div>
<div role="tabpanel" id="tab-2" class="tab-pane fade text-center">
<h5 class="card-title">Card with tabs</h5>
<p class="card-text">Some quick example text to build on the card title and make up the bulk of the card's content.</p>
<a class="btn btn-primary" href="javascript:void(0);">Go somewhere</a>
</div>
<div role="tabpanel" id="tab-3" class="tab-pane fade">
<h5 class="card-title">Card with tabs</h5>
<p class="card-text">Some quick example text to build on the card title and make up the bulk of the card's content.</p>
<a class="btn btn-primary" href="javascript:void(0);">Go somewhere</a>
</div>
</div>
</div>
</div>
</div>
<div class="col-12 col-md-6 col-lg-4 d-flex">
<div class="card flex-fill bg-white">
<div class="card-header">
<ul role="tablist" class="nav nav-pills card-header-pills float-right">
<li class="nav-item">
<a href="#tab-4" data-bs-toggle="tab" class="nav-link active">Active</a>
</li>
<li class="nav-item">
<a href="#tab-5" data-bs-toggle="tab" class="nav-link">Link</a>
</li>
<li class="nav-item">
<a href="#tab-6" data-bs-toggle="tab" class="nav-link disabled">Disabled</a>
</li>
</ul>
</div>
<div class="card-body">
<div class="tab-content pt-0">
<div role="tabpanel" id="tab-4" class="tab-pane fade show active">
<h5 class="card-title">Card with pills</h5>
<p class="card-text">Some quick example text to build on the card title and make up the bulk of the card's content.</p>
<a class="btn btn-primary" href="javascript:void(0);">Go somewhere</a>
</div>
<div role="tabpanel" id="tab-5" class="tab-pane fade text-center">
<h5 class="card-title">Card with pills</h5>
<p class="card-text">Some quick example text to build on the card title and make up the bulk of the card's content.</p>
<a class="btn btn-primary" href="javascript:void(0);">Go somewhere</a>
</div>
<div role="tabpanel" id="tab-6" class="tab-pane fade">
<h5 class="card-title">Card with pills</h5>
<p class="card-text">Some quick example text to build on the card title and make up the bulk of the card's content.</p>
<a class="btn btn-primary" href="javascript:void(0);">Go somewhere</a>
</div>
</div>
</div>
</div>
</div>
</div>
</section>


<section class="comp-section comp-dropdowns">
<div class="section-header">
<h3 class="section-title">Dropdowns</h3>
<div class="line"></div>
</div>
<div class="card bg-white">
<div class="card-body">
<h5 class="card-title">Dropdowns within Text</h5>
<div class="dropdown">
<a class="dropdown-toggle" href="javascript:void(0);" role="button" data-bs-toggle="dropdown" aria-expanded="false"> Dropdown </a>
<div class="dropdown-menu">
<a class="dropdown-item" href="javascript:void(0);">Action</a>
<a class="dropdown-item" href="javascript:void(0);">Another action</a>
</div>
</div>
<hr>
<h5 class="card-title">Dropdowns within Buttons</h5>
<div class="btn-group">
<button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Action</button>
<div class="dropdown-menu">
<a class="dropdown-item" href="javascript:void(0);">Action</a>
<a class="dropdown-item" href="javascript:void(0);">Another action</a>
<div class="dropdown-divider"></div>
<a class="dropdown-item" href="javascript:void(0);">Separated link</a>
</div>
</div>
<div class="btn-group">
<button type="button" class="btn btn-secondary dropdown-toggle" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Action</button>
<div class="dropdown-menu">
<a class="dropdown-item" href="javascript:void(0);">Action</a>
<a class="dropdown-item" href="javascript:void(0);">Another action</a>
<div class="dropdown-divider"></div>
<a class="dropdown-item" href="javascript:void(0);">Separated link</a>
</div>
</div>
<div class="btn-group">
<button type="button" class="btn btn-info dropdown-toggle" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Action</button>
<div class="dropdown-menu">
<a class="dropdown-item" href="javascript:void(0);">Action</a>
<a class="dropdown-item" href="javascript:void(0);">Another action</a>
<div class="dropdown-divider"></div>
<a class="dropdown-item" href="javascript:void(0);">Separated link</a>
</div>
</div>
<div class="btn-group">
<button type="button" class="btn btn-success dropdown-toggle" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Action</button>
<div class="dropdown-menu">
<a class="dropdown-item" href="javascript:void(0);">Action</a>
<a class="dropdown-item" href="javascript:void(0);">Another action</a>
<div class="dropdown-divider"></div>
<a class="dropdown-item" href="javascript:void(0);">Separated link</a>
</div>
</div>
<div class="btn-group">
<button type="button" class="btn btn-warning dropdown-toggle" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Action</button>
<div class="dropdown-menu">
<a class="dropdown-item" href="javascript:void(0);">Action</a>
<a class="dropdown-item" href="javascript:void(0);">Another action</a>
<div class="dropdown-divider"></div>
<a class="dropdown-item" href="javascript:void(0);">Separated link</a>
</div>
</div>
<div class="btn-group">
<button type="button" class="btn btn-danger dropdown-toggle" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Action</button>
<div class="dropdown-menu">
<a class="dropdown-item" href="javascript:void(0);">Action</a>
<a class="dropdown-item" href="javascript:void(0);">Another action</a>
<div class="dropdown-divider"></div>
<a class="dropdown-item" href="javascript:void(0);">Separated link</a>
</div>
</div>
<hr>
<h5 class="card-title">Split button dropdowns</h5>
<div class="btn-group">
<button type="button" class="btn btn-primary">Action</button>
<button type="button" class="btn btn-primary dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
<span class="sr-only">Toggle Dropdown</span>
</button>
<div class="dropdown-menu">
<a class="dropdown-item" href="javascript:void(0);">Action</a>
<a class="dropdown-item" href="javascript:void(0);">Another action</a>
<div class="dropdown-divider"></div>
<a class="dropdown-item" href="javascript:void(0);">Separated link</a>
</div>
</div>
<div class="btn-group">
<button type="button" class="btn btn-secondary">Action</button>
<button type="button" class="btn btn-secondary dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
<span class="sr-only">Toggle Dropdown</span>
</button>
<div class="dropdown-menu">
<a class="dropdown-item" href="javascript:void(0);">Action</a>
<a class="dropdown-item" href="javascript:void(0);">Another action</a>
<div class="dropdown-divider"></div>
<a class="dropdown-item" href="javascript:void(0);">Separated link</a>
</div>
</div>
<div class="btn-group">
<button type="button" class="btn btn-info">Action</button>
<button type="button" class="btn btn-info dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
<span class="sr-only">Toggle Dropdown</span>
</button>
<div class="dropdown-menu">
<a class="dropdown-item" href="javascript:void(0);">Action</a>
<a class="dropdown-item" href="javascript:void(0);">Another action</a>
<div class="dropdown-divider"></div>
<a class="dropdown-item" href="javascript:void(0);">Separated link</a>
</div>
</div>
<div class="btn-group">
 <button type="button" class="btn btn-success">Action</button>
<button type="button" class="btn btn-success dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
<span class="sr-only">Toggle Dropdown</span>
</button>
<div class="dropdown-menu">
<a class="dropdown-item" href="javascript:void(0);">Action</a>
<a class="dropdown-item" href="javascript:void(0);">Another action</a>
<div class="dropdown-divider"></div>
<a class="dropdown-item" href="javascript:void(0);">Separated link</a>
</div>
</div>
<div class="btn-group">
<button type="button" class="btn btn-warning">Action</button>
<button type="button" class="btn btn-warning dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
<span class="sr-only">Toggle Dropdown</span>
</button>
<div class="dropdown-menu">
<a class="dropdown-item" href="javascript:void(0);">Action</a>
<a class="dropdown-item" href="javascript:void(0);">Another action</a>
<div class="dropdown-divider"></div>
<a class="dropdown-item" href="javascript:void(0);">Separated link</a>
</div>
</div>
<div class="btn-group">
<button type="button" class="btn btn-danger">Action</button>
<button type="button" class="btn btn-danger dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
<span class="sr-only">Toggle Dropdown</span>
</button>
<div class="dropdown-menu">
<a class="dropdown-item" href="javascript:void(0);">Action</a>
<a class="dropdown-item" href="javascript:void(0);">Another action</a>
<div class="dropdown-divider"></div>
<a class="dropdown-item" href="javascript:void(0);">Separated link</a>
</div>
</div>
</div>
</div>
</section>


<section class="comp-section">
<div class="section-header">
<h3 class="section-title">Pagination</h3>
<div class="line"></div>
</div>
<div class="card bg-white">
<div class="card-body">
<div>
<ul class="pagination mb-4">
<li class="page-item disabled">
<a class="page-link" href="javascript:void(0);" tabindex="-1">Previous</a>
</li>
<li class="page-item"><a class="page-link" href="javascript:void(0);">1</a></li>
<li class="page-item active">
<a class="page-link" href="javascript:void(0);">2 <span class="sr-only">(current)</span></a>
</li>
<li class="page-item"><a class="page-link" href="javascript:void(0);">3</a></li>
<li class="page-item">
<a class="page-link" href="javascript:void(0);">Next</a>
</li>
</ul>
</div>
<div>
<ul class="pagination mb-4">
<li class="page-item">
<a class="page-link" href="javascript:void(0);" aria-label="Previous">
<span aria-hidden="true">«</span>
<span class="sr-only">Previous</span>
</a>
</li>
<li class="page-item"><a class="page-link" href="javascript:void(0);">1</a></li>
<li class="page-item"><a class="page-link" href="javascript:void(0);">2</a></li>
<li class="page-item"><a class="page-link" href="javascript:void(0);">3</a></li>
<li class="page-item">
<a class="page-link" href="javascript:void(0);" aria-label="Next">
<span aria-hidden="true">»</span>
<span class="sr-only">Next</span>
</a>
</li>
</ul>
</div>
<div>
<ul class="pagination pagination-lg mb-4">
<li class="page-item disabled">
<a class="page-link" href="javascript:void(0);" tabindex="-1">Previous</a>
</li>
<li class="page-item"><a class="page-link" href="javascript:void(0);">1</a></li>
<li class="page-item active">
<a class="page-link" href="javascript:void(0);">2 <span class="sr-only">(current)</span></a>
</li>
<li class="page-item"><a class="page-link" href="javascript:void(0);">3</a></li>
<li class="page-item">
<a class="page-link" href="javascript:void(0);">Next</a>
</li>
</ul>
</div>
<div>
<ul class="pagination pagination-sm mb-0">
<li class="page-item disabled">
<a class="page-link" href="javascript:void(0);" tabindex="-1">Previous</a>
</li>
<li class="page-item"><a class="page-link" href="javascript:void(0);">1</a></li>
<li class="page-item active">
<a class="page-link" href="javascript:void(0);">2 <span class="sr-only">(current)</span></a>
</li>
<li class="page-item"><a class="page-link" href="javascript:void(0);">3</a></li>
<li class="page-item">
<a class="page-link" href="javascript:void(0);">Next</a>
</li>
</ul>
</div>
</div>
</div>
</section>


<section class="comp-section">
<div class="section-header">
<h3 class="section-title">Progress</h3>
<div class="line"></div>
</div>
<div class="progress-example card bg-white">
<div class="card-header">
<h5 class="card-title">Large Progress Bars</h5>
</div>
<div class="card-body pb-0">
<div class="row">
<div class="col-md-6">
<div>
<div class="progress progress-lg">
<div class="progress-bar" role="progressbar" style="width: 10%" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100"></div>
</div>
<div class="progress progress-lg">
<div class="progress-bar bg-success" role="progressbar" style="width: 25%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
</div>
<div class="progress progress-lg">
<div class="progress-bar bg-info" role="progressbar" style="width: 50%" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100"></div>
</div>
<div class="progress progress-lg">
<div class="progress-bar bg-warning" role="progressbar" style="width: 75%" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100"></div>
</div>
<div class="progress progress-lg">
<div class="progress-bar bg-danger" role="progressbar" style="width: 100%" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
</div>
</div>
</div>
<div class="col-md-6">
<div class="progress progress-lg">
<div class="progress-bar progress-bar-striped" role="progressbar" style="width: 10%" aria-valuenow="10" aria-valuemin="0" aria-valuemax="100"></div>
</div>
<div class="progress progress-lg">
<div class="progress-bar progress-bar-striped bg-success" role="progressbar" style="width: 25%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
</div>
<div class="progress progress-lg">
<div class="progress-bar progress-bar-striped bg-info" role="progressbar" style="width: 50%" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100"></div>
</div>
<div class="progress progress-lg">
<div class="progress-bar progress-bar-striped bg-warning" role="progressbar" style="width: 75%" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100"></div>
</div>
<div class="progress progress-lg">
<div class="progress-bar progress-bar-striped bg-danger" role="progressbar" style="width: 100%" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
</div>
</div>
</div>
</div>
</div>
<div class="progress-example card bg-white">
<div class="card-header">
<h5 class="card-title">Default Progress Bars</h5>
</div>
<div class="card-body pb-0">
<div class="row">
<div class="col-md-6">
<div class="progress">
<div class="progress-bar" role="progressbar" style="width: 10%" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100"></div>
</div>
<div class="progress">
<div class="progress-bar bg-success" role="progressbar" style="width: 25%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
</div>
<div class="progress">
<div class="progress-bar bg-info" role="progressbar" style="width: 50%" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100"></div>
</div>
<div class="progress">
<div class="progress-bar bg-warning" role="progressbar" style="width: 75%" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100"></div>
</div>
<div class="progress">
<div class="progress-bar bg-danger" role="progressbar" style="width: 100%" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
</div>
</div>
<div class="col-md-6">
<div class="progress">
<div class="progress-bar progress-bar-striped" role="progressbar" style="width: 10%" aria-valuenow="10" aria-valuemin="0" aria-valuemax="100"></div>
</div>
<div class="progress">
<div class="progress-bar progress-bar-striped bg-success" role="progressbar" style="width: 25%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
</div>
<div class="progress">
<div class="progress-bar progress-bar-striped bg-info" role="progressbar" style="width: 50%" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100"></div>
</div>
<div class="progress">
<div class="progress-bar progress-bar-striped bg-warning" role="progressbar" style="width: 75%" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100"></div>
</div>
<div class="progress">
<div class="progress-bar progress-bar-striped bg-danger" role="progressbar" style="width: 100%" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
</div>
</div>
</div>
</div>
</div>
<div class="progress-example card bg-white">
<div class="card-header">
<h5 class="card-title">Medium Progress Bars</h5>
</div>
<div class="card-body pb-0">
<div class="row">
<div class="col-md-6">
<div>
<div class="progress progress-md">
<div class="progress-bar" role="progressbar" style="width: 10%" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100"></div>
</div>
<div class="progress progress-md">
<div class="progress-bar bg-success" role="progressbar" style="width: 25%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
</div>
<div class="progress progress-md">
<div class="progress-bar bg-info" role="progressbar" style="width: 50%" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100"></div>
</div>
<div class="progress progress-md">
<div class="progress-bar bg-warning" role="progressbar" style="width: 75%" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100"></div>
</div>
<div class="progress progress-md">
<div class="progress-bar bg-danger" role="progressbar" style="width: 100%" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
</div>
</div>
</div>
<div class="col-md-6">
<div class="progress progress-md">
<div class="progress-bar progress-bar-striped" role="progressbar" style="width: 10%" aria-valuenow="10" aria-valuemin="0" aria-valuemax="100"></div>
</div>
<div class="progress progress-md">
<div class="progress-bar progress-bar-striped bg-success" role="progressbar" style="width: 25%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
</div>
<div class="progress progress-md">
<div class="progress-bar progress-bar-striped bg-info" role="progressbar" style="width: 50%" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100"></div>
</div>
<div class="progress progress-md">
<div class="progress-bar progress-bar-striped bg-warning" role="progressbar" style="width: 75%" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100"></div>
</div>
<div class="progress progress-md">
<div class="progress-bar progress-bar-striped bg-danger" role="progressbar" style="width: 100%" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
</div>
</div>
</div>
</div>
</div>
<div class="progress-example card bg-white">
<div class="card-header">
<h5 class="card-title">Small Progress Bars</h5>
</div>
<div class="card-body pb-0">
<div class="row">
<div class="col-md-6">
<div>
<div class="progress progress-sm">
<div class="progress-bar" role="progressbar" style="width: 10%" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100"></div>
</div>
<div class="progress progress-sm">
<div class="progress-bar bg-success" role="progressbar" style="width: 25%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
</div>
<div class="progress progress-sm">
<div class="progress-bar bg-info" role="progressbar" style="width: 50%" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100"></div>
</div>
<div class="progress progress-sm">
<div class="progress-bar bg-warning" role="progressbar" style="width: 75%" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100"></div>
</div>
<div class="progress progress-sm">
<div class="progress-bar bg-danger" role="progressbar" style="width: 100%" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
</div>
</div>
</div>
<div class="col-md-6">
<div class="progress progress-sm">
<div class="progress-bar progress-bar-striped" role="progressbar" style="width: 10%" aria-valuenow="10" aria-valuemin="0" aria-valuemax="100"></div>
</div>
<div class="progress progress-sm">
<div class="progress-bar progress-bar-striped bg-success" role="progressbar" style="width: 25%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
</div>
<div class="progress progress-sm">
<div class="progress-bar progress-bar-striped bg-info" role="progressbar" style="width: 50%" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100"></div>
</div>
<div class="progress progress-sm">
<div class="progress-bar progress-bar-striped bg-warning" role="progressbar" style="width: 75%" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100"></div>
</div>
<div class="progress progress-sm">
<div class="progress-bar progress-bar-striped bg-danger" role="progressbar" style="width: 100%" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
</div>
</div>
</div>
</div>
</div>
<div class="progress-example card bg-white">
<div class="card-header">
<h5 class="card-title">Extra Small Progress Bars</h5>
</div>
<div class="card-body pb-0">
<div class="row">
<div class="col-md-6">
<div>
<div class="progress progress-xs">
<div class="progress-bar w-75" role="progressbar" style="width: 10%" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100"></div>
</div>
<div class="progress progress-xs">
<div class="progress-bar bg-success" role="progressbar" style="width: 25%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
</div>
<div class="progress progress-xs">
<div class="progress-bar bg-info" role="progressbar" style="width: 50%" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100"></div>
</div>
<div class="progress progress-xs">
<div class="progress-bar bg-warning" role="progressbar" style="width: 75%" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100"></div>
</div>
<div class="progress progress-xs">
<div class="progress-bar bg-danger" role="progressbar" style="width: 100%" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
</div>
</div>
</div>
<div class="col-md-6">
<div class="progress progress-xs">
<div class="progress-bar progress-bar-striped" role="progressbar" style="width: 10%" aria-valuenow="10" aria-valuemin="0" aria-valuemax="100"></div>
</div>
<div class="progress progress-xs">
<div class="progress-bar progress-bar-striped bg-success" role="progressbar" style="width: 25%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
</div>
<div class="progress progress-xs">
<div class="progress-bar progress-bar-striped bg-info" role="progressbar" style="width: 50%" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100"></div>
</div>
<div class="progress progress-xs">
<div class="progress-bar progress-bar-striped bg-warning" role="progressbar" style="width: 75%" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100"></div>
</div>
<div class="progress progress-xs">
<div class="progress-bar progress-bar-striped bg-danger" role="progressbar" style="width: 100%" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
</div>
</div>
</div>
</div>
</div>
</section>


<section class="comp-section">
<div class="section-header">
<h3 class="section-title">Tabs</h3>
<div class="line"></div>
</div>
<div class="row">
<div class="col-md-6">
<div class="card bg-white">
<div class="card-header">
<h5 class="card-title">Basic tabs</h5>
</div>
<div class="card-body">
<ul class="nav nav-tabs">
<li class="nav-item"><a class="nav-link active" href="#basictab1" data-bs-toggle="tab">Home</a></li>
<li class="nav-item"><a class="nav-link" href="#basictab2" data-bs-toggle="tab">Profile</a></li>
<li class="nav-item"><a class="nav-link" href="#basictab3" data-bs-toggle="tab">Messages</a></li>
</ul>
<div class="tab-content">
<div class="tab-pane show active" id="basictab1">
Tab content 1
</div>
<div class="tab-pane" id="basictab2">
Tab content 2
</div>
<div class="tab-pane" id="basictab3">
Tab content 3
</div>
</div>
</div>
</div>
</div>
<div class="col-md-6">
<div class="card bg-white">
<div class="card-header">
<h5 class="card-title">Basic justified tabs</h5>
</div>
<div class="card-body">
<ul class="nav nav-tabs nav-justified">
<li class="nav-item"><a class="nav-link active" href="#basic-justified-tab1" data-bs-toggle="tab">Home</a></li>
<li class="nav-item"><a class="nav-link" href="#basic-justified-tab2" data-bs-toggle="tab">Profile</a></li>
<li class="nav-item dropdown">
<a href="javascript:void(0);" class="dropdown-toggle nav-link" data-bs-toggle="dropdown">Dropdown</a>
<div class="dropdown-menu dropdown-menu-right">
<a class="dropdown-item" href="#basic-justified-tab3" data-bs-toggle="tab">Dropdown 1</a>
<a class="dropdown-item" href="#basic-justified-tab4" data-bs-toggle="tab">Dropdown 2</a>
</div>
</li>
</ul>
<div class="tab-content">
<div class="tab-pane show active" id="basic-justified-tab1">
Tab content 1
</div>
<div class="tab-pane" id="basic-justified-tab2">
Tab content 2
</div>
<div class="tab-pane" id="basic-justified-tab3">
Tab content 3
</div>
<div class="tab-pane" id="basic-justified-tab4">
Tab content 4
</div>
</div>
</div>
</div>
</div>
</div>
<div class="row">
<div class="col-md-6">
<div class="card bg-white">
<div class="card-header">
<h5 class="card-title">Solid tabs</h5>
</div>
<div class="card-body">
<ul class="nav nav-tabs nav-tabs-solid">
<li class="nav-item"><a class="nav-link active" href="#solid-tab1" data-bs-toggle="tab">Home</a></li>
<li class="nav-item"><a class="nav-link" href="#solid-tab2" data-bs-toggle="tab">Profile</a></li>
<li class="nav-item"><a class="nav-link" href="#solid-tab3" data-bs-toggle="tab">Messages</a></li>
</ul>
<div class="tab-content">
<div class="tab-pane show active" id="solid-tab1">
Tab content 1
</div>
<div class="tab-pane" id="solid-tab2">
Tab content 2
</div>
<div class="tab-pane" id="solid-tab3">
Tab content 3
</div>
</div>
</div>
</div>
</div>
<div class="col-md-6">
<div class="card bg-white">
<div class="card-header">
<h5 class="card-title">Solid justified</h5>
</div>
<div class="card-body">
<ul class="nav nav-tabs nav-tabs-solid nav-justified">
<li class="nav-item"><a class="nav-link active" href="#solid-justified-tab1" data-bs-toggle="tab">Home</a></li>
<li class="nav-item"><a class="nav-link" href="#solid-justified-tab2" data-bs-toggle="tab">Profile</a></li>
<li class="nav-item"><a class="nav-link" href="#solid-justified-tab3" data-bs-toggle="tab">Messages</a></li>
</ul>
 <div class="tab-content">
<div class="tab-pane show active" id="solid-justified-tab1">
Tab content 1
</div>
<div class="tab-pane" id="solid-justified-tab2">
Tab content 2
</div>
<div class="tab-pane" id="solid-justified-tab3">
Tab content 3
</div>
</div>
</div>
</div>
</div>
</div>
<div class="row">
<div class="col-md-6">
<div class="card bg-white">
<div class="card-header">
<h5 class="card-title">Solid Rounded</h5>
</div>
<div class="card-body">
<ul class="nav nav-tabs nav-tabs-solid nav-tabs-rounded">
<li class="nav-item"><a class="nav-link active" href="#solid-rounded-tab1" data-bs-toggle="tab">Home</a></li>
<li class="nav-item"><a class="nav-link" href="#solid-rounded-tab2" data-bs-toggle="tab">Profile</a></li>
<li class="nav-item"><a class="nav-link" href="#solid-rounded-tab3" data-bs-toggle="tab">Messages</a></li>
</ul>
<div class="tab-content">
<div class="tab-pane show active" id="solid-rounded-tab1">
Tab content 1
</div>
<div class="tab-pane" id="solid-rounded-tab2">
Tab content 2
</div>
<div class="tab-pane" id="solid-rounded-tab3">
Tab content 3
</div>
</div>
</div>
</div>
</div>
<div class="col-md-6">
<div class="card bg-white">
<div class="card-header">
<h5 class="card-title">Rounded justified</h5>
</div>
<div class="card-body">
<ul class="nav nav-tabs nav-tabs-solid nav-tabs-rounded nav-justified">
<li class="nav-item"><a class="nav-link active" href="#solid-rounded-justified-tab1" data-bs-toggle="tab">Home</a></li>
<li class="nav-item"><a class="nav-link" href="#solid-rounded-justified-tab2" data-bs-toggle="tab">Profile</a></li>
<li class="nav-item"><a class="nav-link" href="#solid-rounded-justified-tab3" data-bs-toggle="tab">Messages</a></li>
</ul>
<div class="tab-content">
<div class="tab-pane show active" id="solid-rounded-justified-tab1">
Tab content 1
</div>
<div class="tab-pane" id="solid-rounded-justified-tab2">
Tab content 2
</div>
<div class="tab-pane" id="solid-rounded-justified-tab3">
Tab content 3
</div>
</div>
</div>
</div>
</div>
</div>
</section>


<section class="comp-section">
<div class="section-header">
<h3 class="section-title">Typography</h3>
<div class="line"></div>
</div>
<div class="row">
<div class="col-md-12">
<div class="card bg-white">
<div class="card-header">
<h5 class="card-title">Headings</h5>
</div>
<div class="card-body">
<h1>h1. Bootstrap heading</h1>
<h2>h2. Bootstrap heading</h2>
<h3>h3. Bootstrap heading</h3>
<h4>h4. Bootstrap heading</h4>
<h5>h5. Bootstrap heading</h5>
<h6>h6. Bootstrap heading</h6>
</div>
</div>
<div class="card bg-white">
<div class="card-header">
<h5 class="card-title">Blockquotes</h5>
</div>
<div class="card-body">
<blockquote>
<p class="mb-0">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Integer posuere erat a ante.</p>
</blockquote>
<blockquote class="blockquote mb-0">
<p class="mb-0">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Integer posuere erat a ante.</p>
</blockquote>
</div>
</div>
</div>
</div>
<div class="row">
<div class="col-md-4 d-flex">
<div class="card flex-fill bg-white">
<div class="card-header">
<h5 class="card-title">Text element</h5>
</div>
<div class="card-body">
<p>You can use the mark tag to <mark>highlight</mark> text.</p>
<p><del>This line of text is meant to be treated as deleted text.</del></p>
<p><s>This line of text is meant to be treated as no longer accurate.</s></p>
<p><ins>This line of text is meant to be treated as an addition to the document.</ins></p>
<p><u>This line of text will render as underlined</u></p>
<p><small>This line of text is meant to be treated as fine print.</small></p>
<p><strong>This line rendered as bold text.</strong></p>
<p><em>This line rendered as italicized text.</em></p>
<p class="text-monospace mb-0">This is in monospace</p>
</div>
</div>
</div>
<div class="col-md-4 d-flex">
<div class="card flex-fill bg-white">
<div class="card-header">
<h5 class="card-title">Coloured Link</h5>
</div>
<div class="card-body">
<p class="text-primary">.text-primary</p>
<p class="text-secondary">.text-secondary</p>
<p class="text-success">.text-success</p>
<p class="text-danger">.text-danger</p>
<p class="text-warning">.text-warning</p>
<p class="text-info">.text-info</p>
<p class="text-light bg-dark">.text-light</p>
<p class="text-dark">.text-dark</p>
<p class="text-muted">.text-muted</p>
<p class="text-white bg-dark mb-0">.text-white</p>
</div>
</div>
</div>
<div class="col-md-4 d-flex">
<div class="card flex-fill bg-white">
<div class="card-header">
<h5 class="card-title">Coloured text</h5>
</div>
<div class="card-body">
<p><a href="javascript:void(0);" class="text-primary">Primary link</a></p>
<p><a href="javascript:void(0);" class="text-secondary">Secondary link</a></p>
<p><a href="javascript:void(0);" class="text-success">Success link</a></p>
<p><a href="javascript:void(0);" class="text-danger">Danger link</a></p>
<p><a href="javascript:void(0);" class="text-warning">Warning link</a></p>
<p><a href="javascript:void(0);" class="text-info">Info link</a></p>
<p><a href="javascript:void(0);" class="text-light bg-dark">Light link</a></p>
<p><a href="javascript:void(0);" class="text-dark">Dark link</a></p>
<p><a href="javascript:void(0);" class="text-muted">Muted link</a></p>
<p><a href="javascript:void(0);" class="text-white bg-dark mb-0">White link</a></p>
</div>
</div>
</div>
</div>
<div class="row">
<div class="col-md-4 d-flex">
<div class="card flex-fill bg-white">
<div class="card-header">
<h5 class="card-title">Bullet Lists</h5>
</div>
<div class="card-body">
<ul class="mb-0 bullets">
<li>Lorem ipsum dolor sit amet</li>
<li>Consectetur adipiscing elit</li>
<li>Integer molestie lorem at massa</li>
<li>Facilisis in pretium nisl aliquet</li>
<li>Nulla volutpat aliquam velit
<ul>
<li>Phasellus iaculis neque</li>
<li>Purus sodales ultricies</li>
<li>Vestibulum laoreet porttitor sem</li>
<li>Ac tristique libero volutpat at</li>
</ul>
</li>
<li>Faucibus porta lacus fringilla vel</li>
<li>Aenean sit amet erat nunc</li>
<li>Eget porttitor lorem</li>
</ul>
</div>
</div>
</div>
<div class="col-md-4 d-flex">
<div class="card flex-fill bg-white">
<div class="card-header">
<h5 class="card-title">Bullet Lists</h5>
</div>
<div class="card-body">
<ol class="mb-0">
<li>Lorem ipsum dolor sit amet</li>
<li>Consectetur adipiscing elit</li>
<li>Integer molestie lorem at massa</li>
<li>Facilisis in pretium nisl aliquet</li>
<li>Nulla volutpat aliquam velit
<ul>
<li>Phasellus iaculis neque</li>
<li>Purus sodales ultricies</li>
<li>Vestibulum laoreet porttitor sem</li>
<li>Ac tristique libero volutpat at</li>
</ul>
</li>
<li>Faucibus porta lacus fringilla vel</li>
<li>Aenean sit amet erat nunc</li>
<li>Eget porttitor lorem</li>
</ol>
</div>
</div>
</div>
<div class="col-md-4 d-flex">
<div class="card flex-fill bg-white">
<div class="card-header">
<h5 class="card-title">Unstyled Lists</h5>
</div>
<div class="card-body">
<ul class="list-unstyled mb-0">
<li>Lorem ipsum dolor sit amet</li>
<li>Consectetur adipiscing elit</li>
<li>Integer molestie lorem at massa</li>
<li>Facilisis in pretium nisl aliquet</li>
<li>Nulla volutpat aliquam velit
<ul>
<li>Phasellus iaculis neque</li>
<li>Purus sodales ultricies</li>
<li>Vestibulum laoreet porttitor sem</li>
<li>Ac tristique libero volutpat at</li>
</ul>
</li>
<li>Faucibus porta lacus fringilla vel</li>
<li>Aenean sit amet erat nunc</li>
<li>Eget porttitor lorem</li>
</ul>
</div>
</div>
</div>
</div>
</section>

</div>
</div>
</div>
</div>


<script src="assets/js/jquery-3.6.0.min.js"></script>

<script src="assets/js/feather.min.js"></script>

<script src="assets/js/jquery.slimscroll.min.js"></script>

<script src="assets/js/jquery.dataTables.min.js"></script>
<script src="assets/js/dataTables.bootstrap4.min.js"></script>

<script src="assets/js/bootstrap.bundle.min.js"></script>

<script src="assets/plugins/apexchart/apexcharts.min.js"></script>
<script src="assets/plugins/apexchart/chart-data.js"></script>

<script src="assets/js/script.js"></script>
</body>
</html>