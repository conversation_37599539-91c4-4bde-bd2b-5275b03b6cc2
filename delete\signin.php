<?php
require("database.php");

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

// Load Composer's autoloader
require 'MAILER/vendor/autoload.php';

if (isset($_SESSION["user"])) {
    header("location: ./index.php");
}

if (isset($_POST["login"])) {
    $email = $_POST["email"];
    if (strpos($email, '@cllsystems.com') === false) {
        echo "<div class='alert alert-danger alert-dismissible fade show text-center' role='alert'>
                Only @cllsystems.com email addresses are allowed.
              </div>";
    } else {
        $sql = "SELECT * from useracc where email = '$email'";
        $result = mysqli_query($conn, $sql);
        if (!$result) {
            echo "<div class='alert alert-danger alert-dismissible fade show text-center' role='alert'>
                    Database query failed: " . mysqli_error($conn) . "
                </div>";
        } else {
            $num_rows = mysqli_num_rows($result);
            if ($num_rows > 0) {
                $row = mysqli_fetch_assoc($result);

                if ($row["verified"] == 0) {
                    echo "<div class='alert alert-danger alert-dismissible fade show text-center' role='alert'>
                            Please verify your email address before signing in.
                          </div>";
                } else {
                    if ($row["ban"] == "1") {
                        echo"<script> alert('Your account has been suspended, kindly contact the Technical Department for further help.');window.location='signin.php';</script>";
                    } else {
                        function generateOTP() {
                            $otpLength = 6;
                            $otp = rand(pow(10, $otpLength-1), pow(10, $otpLength)-1);
                            return $otp;
                        }
                        $randomOTP = generateOTP();
                        $permitted_chars = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
                        function generate_string($input, $strength = 16) {
                            $input_length = strlen($input);
                            $random_string = '';
                            for ($i = 0; $strength > $i; $i++) {
                                $random_character = $input[mt_rand(0, $input_length - 1)];
                                $random_string .= $random_character;
                            }
                            return $random_string;
                        }
                        $Token = generate_string($permitted_chars, 20);
                        $newotp = md5($randomOTP);
                        date_default_timezone_set('Asia/Kuala_Lumpur');
                        $malaysiaTimestamp = time();
                        $formattedMalaysiaTimestamp = date('Y-m-d H:i:s', $malaysiaTimestamp);
                        $sql2 = "UPDATE useracc SET token='$Token', otp='$newotp', expired='$formattedMalaysiaTimestamp' WHERE email = '$email'";
                        $result2 = mysqli_query($conn, $sql2);
                        if ($result2) {
                            $mail = new PHPMailer(true);
                            try {
                                $mail->isSMTP();
                                $mail->SMTPOptions = array(
                                    'ssl' => array(
                                        'verify_peer' => false,
                                        'verify_peer_name' => false,
                                        'allow_self_signed' => true
                                    )
                                );
                                $mail->Host       = 'smtp.office365.com';
                                $mail->SMTPAuth   = true;
                                $mail->Username   = '<EMAIL>';
                                $mail->Password   = '&[i3F6}0hOw6';
                                $mail->Port       = 587;
                                $mail->setFrom('<EMAIL>', 'CLLXWARE');
                                $mail->addAddress($email, 'Employee');
                                $mail->SMTPSecure = 'tls';
                                $mail->isHTML(true);
                                $mail->Subject = "CLLXWARE - One-Time PIN (OTP)";
                                $mail->Body  =  '
                                <!DOCTYPE html>
                                <html>
                                <head>
                                    <style>
                                        body {
                                            font-family: Arial, sans-serif;
                                            background-color: #f4f4f4;
                                            margin: 0;
                                            padding: 0;
                                        }
                                        .container {
                                            max-width: 600px;
                                            margin: 0 auto;
                                            padding: 20px;
                                            background-color: #ffffff;
                                            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
                                            border-radius: 10px;
                                             text-align: center;
                                        }
                                        .header {
                                            text-align: center;
                                            padding: 10px 0;
                                            background-color: #004080;
                                            border-radius: 10px 10px 0 0;
                                        }
                                        .header img {
                                            max-width: 150px;
                                            height: auto;
                                        }
                                        .content {
                                            padding: 20px;
                                            color: #333333;
                                        }
                                        .content p {
                                            margin: 0 0 15px;
                                            font-size: 14px;
                                            line-height: 1.5;
                                        }
                                        .otp {
                                            font-size: 36px; /* Increase the font size for the OTP */
                                            font-weight: bold;
                                            color: #333333;
                                            text-align: center;
                                            display: block;
                                            margin: 20px 0;
                                        }
                                        .footer {
                                            text-align: center;
                                            padding: 10px;
                                            background-color: #f4f4f4;
                                            color: #888888;
                                            font-size: 12px;
                                            border-bottom-left-radius: 10px;
                                            border-bottom-right-radius: 10px;
                                        }
                                        .footer a {
                                            color: #888888;
                                            text-decoration: none;
                                        }
                                    </style>
                                </head>
                                <body>
                                    <div class="container">
                                        <div class="header">
                                            <img src="https://www.cllsystems.com/wp-content/uploads/2016/09/cropped-cll-logo.png" alt="Company Logo">
                                        </div>
                                        <div class="content">
                                            <p>Dear employee,</p>
                                            <p>Your One-Time Password (OTP) for authentication is:</p>
                                            <p class="otp">'.$randomOTP.'</p>
                                            <p>This OTP is valid for 5 minutes and usable only once, thank you.</p>
                                            <p><b>CLL Systems Sdn Bhd</b></p>
                                        </div>
                                        <div class="footer">
                                            <p>CLL Systems Sdn Bhd | <a href="https://www.cllsystems.com">www.cllsystems.com</a></p>
                                        </div>
                                    </div>
                                </body>
                                </html>';
                                
                                
                                
                                if ($mail->send()) {                                       
                                    header("location: ./signin.php?Token=$Token");
                                } else {
                                   echo "Message could not be sent. Mailer Error: {$mail->ErrorInfo}";
                                }
                            } catch (Exception $e) {
                                echo "Message could not be sent. Mailer Error: {$mail->ErrorInfo}";
                            }
                        }
                    }
                }
            }
            else {
                echo "<div class='alert alert-danger alert-dismissible fade show text-center' role='alert'>
                     Please contact Technical Department for inquiries!
                    </div>";
            }
        }
    }
}

if (isset($_POST["login2"])) {
    $token = $_POST["Token"];
    $otp = $_POST["otp"];
    $sql = "SELECT * from useracc where token = '$token'";
    $result = mysqli_query($conn, $sql);
  if (!$result) {
    echo "<div class='alert alert-danger alert-dismissible fade show text-center' role='alert'>
            Database query failed: " . mysqli_error($conn) . "
        </div>";
} else {
    $hashed_otp = md5($otp);
    $sql = "SELECT * from useracc where token = '$token' and otp = '$hashed_otp'";
    $result = mysqli_query($conn, $sql);
    $rows = mysqli_num_rows($result);
    if ($rows == 1) {
        $row = mysqli_fetch_assoc($result);
        if ($row["ban"] == "1") {
            echo"<script> alert('Your account has been suspended, kindly contact the Technical Department for further help.');window.location='signin.php';</script>";
        } else {
            $_SESSION["user"] = $row["email"];
            $_SESSION["role_id"] = $row["role_id"];
            $_SESSION["acc_id"] = $row["acc_id"];
            $email = $_SESSION["user"];
            $sql3 = "UPDATE useracc SET token='', otp='',  expired='', attempt='0' WHERE email = '$email'";
            $result3 = mysqli_query($conn, $sql3);
            if ($result3) {
                header("location: ./index.php");
            }
        }
    } else {
        $sql = "SELECT * from useracc where token = '$token'";
        $result = mysqli_query($conn, $sql);
        $row = mysqli_fetch_assoc($result);
        $email = $row["email"];
        $attempts = $row["attempt"] + 1;
        if ($attempts <= 3) {
            // Update the attempt count
            $sqlUpdateAttempts = "UPDATE useracc SET attempt = '$attempts' WHERE email = '$email'";
            mysqli_query($conn, $sqlUpdateAttempts);
            $remainingAttempts = 3 - $attempts;
            echo "<div class='alert alert-danger alert-dismissible fade show text-center' role='alert'>
                    Invalid OTP! You have $remainingAttempts attempts remaining.
                </div>";
            // If the number of attempts reaches 3, also update the 'ban' status
            if ($attempts == 3) {
                $sqlUpdateBanStatus = "UPDATE useracc SET ban = '1' WHERE email = '$email'";
                mysqli_query($conn, $sqlUpdateBanStatus);
                echo "<script> alert('Your account has been suspended due to multiple incorrect attempts. Kindly contact Technical Department for further assistance.');window.location='signin.php';</script>";
            }
        } else {
            echo "<div class='alert alert-danger alert-dismissible fade show text-center' role='alert'>
                    Invalid OTP! Your account has been suspended. Kindly contact Technical Department for further assistance.
                </div>";
        }
    }
}
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0">
<meta name="description" content="CLL Warehouse Inventory System">
<meta name="author" content="Powered by CLL Systems Sdn Bhd">
<meta name="robots" content="noindex, nofollow">
<title>CLLXWARE-Login</title>
<link rel='icon' type='image/x-icon' href='https://www.cllsystems.com/wp-content/themes/cllsystems/img/favicon.png'>
<link rel="stylesheet" href="assets/css/bootstrap.min.css">
<link rel="stylesheet" href="assets/plugins/fontawesome/css/fontawesome.min.css">
<link rel="stylesheet" href="assets/plugins/fontawesome/css/all.min.css">
<link rel="stylesheet" href="assets/css/style.css">
</head>
<body class="account-page">
<div class="main-wrapper">
<div class="account-content">
<div class="login-wrapper">
<?php 
if (isset($_GET["Token"])) {
    $token = $_GET["Token"];
    $sql3 = "SELECT * from  useracc where token = '$token'";
    $result3 = mysqli_query($conn, $sql3);
    if (!$result3) {
        die("Query failed: " . mysqli_error($conn));
    }
    $rows3 = mysqli_num_rows($result3);
    $row3 = mysqli_fetch_row($result3);
    if ($row3[6] == "1") {
        echo"<script> alert('Your account has been suspended, kindly contact the Technical Department for further help.');window.location='signin.php';</script>";
    } else {
        if (!empty($rows3)) {
            date_default_timezone_set('Asia/Kuala_Lumpur');
            $databaseTimestamp = $row3[5];
            $databaseTimestampUnix = strtotime($databaseTimestamp);
            $currentTimestamp = time();
            $timeDifference = $currentTimestamp - $databaseTimestampUnix;
            if ($timeDifference <= 300) {
                echo "
                <div class='login-content'>
                <div class='login-userset'>
                <div class='login-logo'>
                <img src='assets/img/logo2.png' alt='img'>
                </div>
                <div class='login-userheading'>
                <h3>Sign In</h3>
                <p><b>One-Time PIN (OTP)</b><br>We have improved the login experience with Passwordless Authentication. Please enter the OTP that has been sent to your email ($row3[1]). </p>
                </div>
                <form action='#' class='login-form' method='POST'>
                <div class='form-login'>
                <label>OTP</label>
                <div class='form-addons'>
                <input type='text' placeholder='One-Time PIN (OTP)' id='otp' name='otp' required>
                </div>
                </div>";
                date_default_timezone_set('Asia/Kuala_Lumpur');
                $databaseTimestamp = $row3[5];
                $databaseTimestampUnix = strtotime($databaseTimestamp);
                $expirationTime = $databaseTimestampUnix + (5 * 60); // 5 minutes in seconds
                $currentTimestamp = time();
                $remainingTime = $expirationTime - $currentTimestamp;
                if ($remainingTime > 0) {
                    echo "Time remaining:<div id='countdown' data-time='$remainingTime'></div>";
                    echo "<script>
                            function formatRemainingTime(seconds) {
                                if (seconds < 60) {
                                    return seconds + ' seconds';
                                } else {
                                    var minutes = Math.floor(seconds / 60);
                                    seconds = seconds % 60;
                                    return minutes + ' minutes ' + seconds + ' seconds';
                                }
                            }
                            function updateCountdown() {
                                var countdownElement = document.getElementById('countdown');
                                var remainingTime = parseInt(countdownElement.getAttribute('data-time'));
                                if (remainingTime > 0) {
                                    remainingTime--;
                                    countdownElement.textContent = formatRemainingTime(remainingTime);
                                    countdownElement.setAttribute('data-time', remainingTime);
                                } else {
                                    countdownElement.textContent = 'Expired';
                                    setTimeout(function() {
                                        location.reload();
                                    }, 1000);
                                }
                            }
                            updateCountdown();
                            setInterval(updateCountdown, 1000);
                          </script>";
                } else {
                    echo "The OTP has expired.";
                }
                echo "
                <input type='hidden' name='Token' value='$token'>
                <div class='form-login'>
                <input type='submit' name='login2' value='Login' class='btn btn-login'>
                </div>
                </form>
                </div>
                </div>";
            } else {
                echo "<script> alert('Expired URL');window.location='signin.php';</script>";
            }
        } else {
            echo"<script> alert('Invalid/Used URL/Expired URL');window.location='signin.php';</script>";
        }
    }
} else {
echo "
<div class='login-content'>
<div class='login-userset'>
<div class='login-logo'>
<img src='assets/img/logo2.png' alt='img'>
</div>
<div class='login-userheading'>
<h3>Sign In</h3>
<h4>Please login to your account</h4>
</div>
<form action='#' class='login-form' method='POST'>
<div class='form-login'>
<label>Email</label>
<div class='form-addons'>
<input type='text' placeholder='Enter your email address' id='email' name='email' required>
<img src='assets/img/icons/mail.svg' alt='img'>
</div>
</div>
<div class='form-login'>
<input type='submit' name='login' value='Next' class='btn btn-login'>
</div>
</form>
<div class='form-login'>
<p>Don't have an account? <a href='register.php'>Register</a></p>
</div>
</div>
</div>";
}
?>
<div class="login-img">
<img src="assets/img/inventory.png" alt="img">
</div>
</div>
</div>
</div>
<script src="assets/js/jquery-3.6.0.min.js"></script>
<script src="assets/js/feather.min.js"></script>
<script src="assets/js/bootstrap.bundle.min.js"></script>
<script src="assets/js/script.js"></script>
</body>
</html>
