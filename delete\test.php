<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Ajax and jQuery Search and Select</title>
  <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
  <style>
    #product-list {
      width: 100%;
      max-width: 600px;
      border: 1px solid #ccc;
      padding: 10px;
      margin-bottom: 10px;
    }

    #product-list ul {
      list-style-type: none;
      padding: 0;
    }

    #product-list li {
      cursor: pointer;
      padding: 5px;
      margin-bottom: 5px;
      border: 1px solid #ccc;
    }

    #product-list li:hover {
      background-color: #eee;
    }

    table {
      width: 100%;
      border-collapse: collapse;
    }

    th, td {
      border: 1px solid #ccc;
      padding: 5px;
    }
  </style>
</head>
<body>
  <h1>Ajax and jQuery Search and Select</h1>

  <div class="col-lg-12 col-sm-6 col-12">
    <div class="form-group">
      <label>Product Model</label>
      <div class="input-groupicon">
        <input type="text" id="product-name" placeholder="Please type product model and select...">
      </div>
    </div>
  </div>

  <div id="product-list"></div>

  <div class="row">
    <div class="table-responsive mb-3">
      <table class="table">
        <thead>
          <tr>
            <th>No</th>
            <th>Product Model</th>
            <th>Product Part Number</th>
            <th>Product Serial Number</th>
            <th>QTY</th>
            <th>Brand</th>
            <th>Category</th>
            <th>Location</th>
            <th>Description</th>
            <th></th>
          </tr>
        </thead>
        <tbody>
        </tbody>
      </table>
    </div>
  </div>

  <script>
    $(document).ready(function() {
      $('#product-name').on('keyup', function() {
        var product_name = $(this).val();

        if (product_name.length >= 3) {
          $.ajax({
            url: 'search-product.php',
            method: 'GET',
            data: {
              product_name: product_name
            },
            success: function(data) {
              $('#product-list').html(data);
            }
          });
        } else {
          $('#product-list').html('');
        }
      });

      $(document).on('click', '#product-list li', function() {
    var product_name = $(this).text();

    $('#product-name').val(product_name);
    $('#product-list').html('');

    // Ajax call to get product details
    $.ajax({
        url: 'get-product-details.php',
        method: 'GET',
        data: {
            product_name: product_name
        },
        success: function(data) {
            // Check if the response contains an error
            if (data.error) {
                console.error('Error:', data.error);
                return;
            }

            // Parse the product details from the response
            var product_details = data.product_details;

            // Update the table with product details
            var table = $('table');
            var rowCount = table.find('tbody tr').length;
            var newRow = '<tr>' +
                '<td>' + (rowCount + 1) + '</td>' +
                '<td>' + product_details['product-name'] + '</td>' +
                '<td>' + product_details['part-number'] + '</td>' +
                '<td>' + product_details['serial-number'] + '</td>' +
                '<td><input type="text" value="1" style="width: 30px;"></td>' +
                '<td>' + product_details['brand_name'] + '</td>' +
                '<td>' + product_details['category_name'] + '</td>' +
                '<td> Room ' + product_details['location-id'] + '</td>' +
                '<td>' + product_details['des'] + '</td>' +
                '<td>' +
                '<a href="javascript:void(0);" class="delete-set"><img src="assets/img/icons/delete.svg" alt="svg"></a>' +
                '</td>' +
                '</tr>';

            table.find('tbody').append(newRow);
        },
        error: function(xhr, status, error) {
            console.error('Error:', xhr.responseText);
            alert('An error occurred while fetching product details');
        }
    });
});

    });
  </script>
</body>
</html>
