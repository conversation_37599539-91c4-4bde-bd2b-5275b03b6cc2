<?php
require("database.php");

// Start the session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if the user is logged in
if (!isset($_SESSION["user"])) {
    header("location: ./signin.php");
    exit;
}

// Get the user's role ID to check permissions
$role_id = $_SESSION["role_id"];
$sql_role = "SELECT `offboarding_all` FROM `role-list` WHERE `role-id` = ?";
$stmt_role = mysqli_prepare($conn, $sql_role);
mysqli_stmt_bind_param($stmt_role, "s", $role_id);
mysqli_stmt_execute($stmt_role);
$result_role = mysqli_stmt_get_result($stmt_role);

if ($row_role = mysqli_fetch_assoc($result_role)) {
    $has_permission = $row_role['offboarding_all'] == '1';
} else {
    // If role data not found, deny access
    echo "Error: Unable to verify user permissions.";
    exit;
}

mysqli_stmt_close($stmt_role);

// Check if the user has permission to delete
if (!$has_permission) {
    echo "Error: You do not have permission to delete this checklist.";
    exit;
}

// Check if the request is a POST request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Check if checklist_id is provided
    if (isset($_POST['checklist_id'])) {
        $checklist_id = trim($_POST['checklist_id']);

        // Validate and sanitize the checklist_id
        if (!empty($checklist_id)) {
            // Sanitize the input to prevent SQL injection
            $checklist_id = mysqli_real_escape_string($conn, $checklist_id);

            // Prepare the DELETE statement
            $sql_delete = "DELETE FROM `offboarding_checklist` WHERE `checklist_id` = ?";
            $stmt_delete = mysqli_prepare($conn, $sql_delete);
            mysqli_stmt_bind_param($stmt_delete, "s", $checklist_id);

            if (mysqli_stmt_execute($stmt_delete)) {
                // Check if any rows were affected
                if (mysqli_stmt_affected_rows($stmt_delete) > 0) {
                    // Successfully deleted
                    echo "Success: Checklist item deleted.";
                } else {
                    // Checklist item not found
                    echo "Error: Checklist item not found or already deleted.";
                }
            } else {
                // Error executing the query
                echo "Error: Could not execute deletion. Please try again later.";
            }

            mysqli_stmt_close($stmt_delete);
        } else {
            // Invalid checklist_id
            echo "Error: Invalid checklist ID.";
        }
    } else {
        // checklist_id not provided
        echo "Error: Checklist ID not provided.";
    }
} else {
    // Invalid request method
    echo "Error: Invalid request method.";
}

mysqli_close($conn);
?>
