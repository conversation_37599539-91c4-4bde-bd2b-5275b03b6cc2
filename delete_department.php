<?php
require("database.php");

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isset($_SESSION["user"])) {
    echo json_encode(['status' => 'error', 'message' => 'Unauthorized access. Please log in.']);
    exit;
}

// Check if department_id is provided
if (isset($_POST['department_id'])) {
    $department_id = $_POST['department_id'];

    // Verify if the department exists
    $check_sql = "SELECT * FROM `department_list` WHERE `department_id` = ?";
    $check_stmt = mysqli_prepare($conn, $check_sql);
    mysqli_stmt_bind_param($check_stmt, "s", $department_id);
    mysqli_stmt_execute($check_stmt);
    $check_result = mysqli_stmt_get_result($check_stmt);

    if ($check_result && mysqli_num_rows($check_result) > 0) {
        // Delete the department
        $delete_sql = "DELETE FROM `department_list` WHERE `department_id` = ?";
        $delete_stmt = mysqli_prepare($conn, $delete_sql);
        mysqli_stmt_bind_param($delete_stmt, "s", $department_id);

        if (mysqli_stmt_execute($delete_stmt)) {
            echo json_encode(['status' => 'success', 'message' => 'Department deleted successfully.']);
        } else {
            echo json_encode(['status' => 'error', 'message' => 'Failed to delete the department.']);
        }

        mysqli_stmt_close($delete_stmt);
    } else {
        echo json_encode(['status' => 'error', 'message' => 'Department not found.']);
    }

    mysqli_stmt_close($check_stmt);
} else {
    echo json_encode(['status' => 'error', 'message' => 'Invalid request. Department ID is missing.']);
}
?>
