<?php
require("database.php");

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['hr_inq_id'])) {
        $hr_inq_id = $_POST['hr_inq_id'];

        // Prepare and execute the SQL delete statement
        $sql = "DELETE FROM `hrinq_list` WHERE `hr_inq_id` = ?";
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, "s", $hr_inq_id);

        if (mysqli_stmt_execute($stmt)) {
            // Successfully deleted the record
            echo "HR inquiry deleted successfully.";
        } else {
            // Error occurred while deleting the record
            echo "Error deleting HR inquiry: " . mysqli_error($conn);
        }

        mysqli_stmt_close($stmt);
    } else {
        // HR inquiry ID not provided
        echo "No HR inquiry ID provided.";
    }
} else {
    // Invalid request method
    echo "Invalid request method.";
}

mysqli_close($conn);
?>
