<?php
// Check if the request method is POST
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Check if the inventory_id parameter is set
    if (isset($_POST["inventory_id"])) {
        // Include your database connection file
        include "database.php"; // Adjust the path as per your file structure
        
        // Sanitize the input to prevent SQL injection
        $inventory_id = mysqli_real_escape_string($conn, $_POST["inventory_id"]);
        
        // Construct the SQL delete query
        $sql = "DELETE FROM `inventory-list` WHERE `inventory-id` = '$inventory_id'";
        
        // Execute the delete query
        if (mysqli_query($conn, $sql)) {
            // If deletion is successful, send a success response
            http_response_code(200);
            echo "Item deleted successfully.";
        } else {
            // If deletion fails, send an error response
            http_response_code(500);
            echo "Error deleting item: " . mysqli_error($conn);
        }
        
        // Close the database connection
        mysqli_close($conn);
    } else {
        // If inventory_id parameter is not set, send a bad request response
        http_response_code(400);
        echo "Error: Inventory ID parameter is missing.";
    }
} else {
    // If the request method is not POST, send a method not allowed response
    http_response_code(405);
    echo "Error: Method not allowed.";
}
?>
