<?php
// Check if the request method is POST
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST["location_id"])) {
        // Include your database connection file
        include "database.php"; // Adjust the path as per your file structure
        
        // Sanitize the input to prevent SQL injection
        $location_id = mysqli_real_escape_string($conn, $_POST["location_id"]);
        list($room, $rack) = explode(' | ', $location_id);
        // Construct the SQL delete query
        $sql = "DELETE FROM `location-list` WHERE `room` = '$room' AND `rack` = '$rack'";
        
        // Execute the delete query
        if (mysqli_query($conn, $sql)) {
            // If deletion is successful, send a success response
            http_response_code(200);
            echo "Location deleted successfully.";
        } else {
            // If deletion fails, send an error response
            http_response_code(500);
            echo "Error deleting item: " . mysqli_error($conn);
        }
        
        // Close the database connection
        mysqli_close($conn);
    } else {
        // If inventory_id parameter is not set, send a bad request response
        http_response_code(400);
        echo "Error: Location ID parameter is missing.";
    }
} else {
    // If the request method is not POST, send a method not allowed response
    http_response_code(405);
    echo "Error: Method not allowed.";
}
?>
