<?php
require("database.php");

// Check if the session is already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if the user is logged in
if (!isset($_SESSION["user"])) {
    header("location: ./signin.php");
    exit;
}

// Check if the request is a POST request
if ($_SERVER["REQUEST_METHOD"] === "POST") {
    if (isset($_POST["manager_id"])) {
        $manager_id = $_POST["manager_id"];

        // Check if the manager exists
        $check_sql = "SELECT * FROM `manager_list` WHERE `manager_id` = ?";
        $check_stmt = mysqli_prepare($conn, $check_sql);
        mysqli_stmt_bind_param($check_stmt, "s", $manager_id);
        mysqli_stmt_execute($check_stmt);
        $result = mysqli_stmt_get_result($check_stmt);

        if (mysqli_num_rows($result) > 0) {
            // Delete the manager
            $delete_sql = "DELETE FROM `manager_list` WHERE `manager_id` = ?";
            $delete_stmt = mysqli_prepare($conn, $delete_sql);
            mysqli_stmt_bind_param($delete_stmt, "s", $manager_id);

            if (mysqli_stmt_execute($delete_stmt)) {
                echo "Manager deleted successfully.";
            } else {
                http_response_code(500);
                echo "Error deleting manager: " . mysqli_error($conn);
            }

            mysqli_stmt_close($delete_stmt);
        } else {
            http_response_code(400);
            echo "Invalid manager ID.";
        }

        mysqli_stmt_close($check_stmt);
    } else {
        http_response_code(400);
        echo "Manager ID is required.";
    }
} else {
    http_response_code(405);
    echo "Invalid request method.";
}
?>
