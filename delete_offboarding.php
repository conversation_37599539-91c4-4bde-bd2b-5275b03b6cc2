<?php
require("database.php");

// Check if the user is logged in
if (isset($_SESSION["user"])) {
    if ($_SERVER["REQUEST_METHOD"] === "POST") {
        // Get the offboarding ID from the request
        if (isset($_POST['offboarding_id'])) {
            $offboarding_id = $_POST['offboarding_id'];

            // Prepare the SQL query to delete the offboarding record
            $sql = "DELETE FROM `offboarding_list` WHERE `offboarding_id` = ?";
            $stmt = mysqli_prepare($conn, $sql);

            if ($stmt) {
                // Bind the offboarding ID to the query
                mysqli_stmt_bind_param($stmt, "s", $offboarding_id);

                // Execute the query
                if (mysqli_stmt_execute($stmt)) {
                    echo "Record deleted successfully";
                } else {
                    echo "Error deleting record: " . mysqli_error($conn);
                }

                // Close the prepared statement
                mysqli_stmt_close($stmt);
            } else {
                echo "Failed to prepare the SQL statement.";
            }
        } else {
            echo "No offboarding ID provided.";
        }
    } else {
        echo "Invalid request method.";
    }
} else {
    echo "Unauthorized access.";
}

// Close the database connection
mysqli_close($conn);
?>
