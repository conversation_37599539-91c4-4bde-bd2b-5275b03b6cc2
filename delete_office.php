<?php
require("database.php");

if (isset($_POST['office_id'])) {
    $office_id = $_POST['office_id'];

    // Prepare SQL statement to delete the office entry
    $sql = "DELETE FROM `office_list` WHERE `office_id` = ?";
    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, "s", $office_id);

    if (mysqli_stmt_execute($stmt)) {
        echo "Office entry deleted successfully.";
    } else {
        http_response_code(500);
        echo "Error: " . mysqli_error($conn);
    }

    mysqli_stmt_close($stmt);
} else {
    http_response_code(400);
    echo "Invalid request.";
}
?>
