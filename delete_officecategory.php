<?php
require("database.php");

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST['office_category_id'])) {
        $officeCategoryId = $_POST['office_category_id'];

        // Prepare and execute the delete query
        $sql = "DELETE FROM `office_category_list` WHERE `office_category_id` = ?";
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, "i", $officeCategoryId);

        if (mysqli_stmt_execute($stmt)) {
            echo "Success";
        } else {
            echo "Error: " . mysqli_error($conn);
        }

        mysqli_stmt_close($stmt);
    } else {
        echo "Invalid request";
    }
} else {
    echo "Invalid request method";
}

mysqli_close($conn);
?>
