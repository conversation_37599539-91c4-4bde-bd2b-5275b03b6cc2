<?php
require("database.php");

// Ensure user logged in
if (!isset($_SESSION["user"])) {
    http_response_code(401);
    echo "Unauthorized";
    exit;
}

if ($_SERVER["REQUEST_METHOD"] !== "POST") {
    http_response_code(405);
    echo "Invalid request method";
    exit;
}

if (!isset($_POST['onboarding_id'])) {
    http_response_code(400);
    echo "Missing onboarding_id";
    exit;
}

$onboarding_id = $_POST['onboarding_id'];

// Optional: delete associated image file
$sql_get = "SELECT personal_image FROM `onboarding_list` WHERE `onboarding_id` = ?";
$stmt_get = mysqli_prepare($conn, $sql_get);
mysqli_stmt_bind_param($stmt_get, "s", $onboarding_id);
mysqli_stmt_execute($stmt_get);
$res_get = mysqli_stmt_get_result($stmt_get);
$img = '';
if ($row = mysqli_fetch_assoc($res_get)) { $img = $row['personal_image']; }
mysqli_stmt_close($stmt_get);

$sql = "DELETE FROM `onboarding_list` WHERE `onboarding_id` = ?";
$stmt = mysqli_prepare($conn, $sql);
if (!$stmt) { http_response_code(500); echo "Failed to prepare"; exit; }
mysqli_stmt_bind_param($stmt, "s", $onboarding_id);
if (mysqli_stmt_execute($stmt)) {
    if (!empty($img) && file_exists($img)) { @unlink($img); }
    echo "Success";
} else {
    http_response_code(500);
    echo "Error: " . mysqli_error($conn);
}
mysqli_stmt_close($stmt);
?>

