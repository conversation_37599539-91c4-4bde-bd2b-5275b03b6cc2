<?php
session_start();
require("database.php");

// Check if the user is logged in
if (!isset($_SESSION["user"])) {
    echo json_encode(['status' => 'error', 'message' => 'Unauthorized access.']);
    exit;
}

// Check if person_in_charge_id is provided
if (!isset($_POST['person_in_charge_id'])) {
    echo json_encode(['status' => 'error', 'message' => 'No Person In Charge ID provided.']);
    exit;
}

$personInChargeId = $_POST['person_in_charge_id'];

// Verify if the current user has the required permissions
$role_id = $_SESSION["role_id"];
$sqlRole = "SELECT offboarding_all FROM `role-list` WHERE `role-id` = ?";
$stmtRole = mysqli_prepare($conn, $sqlRole);
mysqli_stmt_bind_param($stmtRole, "s", $role_id);
mysqli_stmt_execute($stmtRole);
$resultRole = mysqli_stmt_get_result($stmtRole);

if ($rowRole = mysqli_fetch_assoc($resultRole)) {
    if ($rowRole['offboarding_all'] != '1') {
        echo json_encode(['status' => 'error', 'message' => 'Permission denied.']);
        exit;
    }
} else {
    echo json_encode(['status' => 'error', 'message' => 'Role data not found.']);
    exit;
}

// Delete the person in charge
$sqlDelete = "DELETE FROM `person_in_charge_list` WHERE `person_in_charge_id` = ?";
$stmtDelete = mysqli_prepare($conn, $sqlDelete);
mysqli_stmt_bind_param($stmtDelete, "s", $personInChargeId);

if ($stmtDelete->execute()) {
    echo json_encode(['status' => 'success', 'message' => 'Person in charge deleted successfully.']);
} else {
    echo json_encode(['status' => 'error', 'message' => 'Failed to delete person in charge.']);
}

// Close database connections
mysqli_stmt_close($stmtRole);
mysqli_stmt_close($stmtDelete);
mysqli_close($conn);
?>
