<?php
require("database.php");

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if the user is logged in
if (!isset($_SESSION["user"])) {
    http_response_code(401);
    echo json_encode(['status' => 'error', 'message' => 'Unauthorized access. Please log in.']);
    exit;
}

// Check if the position ID is provided
if ($_SERVER["REQUEST_METHOD"] === "POST" && isset($_POST["position_id"])) {
    $position_id = $_POST["position_id"];

    // Validate the position ID
    if (empty($position_id)) {
        http_response_code(400);
        echo json_encode(['status' => 'error', 'message' => 'Invalid position ID.']);
        exit;
    }

    // Check if the position exists and get department info
    $sql_check = "SELECT p.*, d.department_name 
                  FROM `position_list` p 
                  LEFT JOIN `department_list` d ON p.department_id = d.department_id 
                  WHERE p.position_id = ?";
    $stmt_check = mysqli_prepare($conn, $sql_check);
    mysqli_stmt_bind_param($stmt_check, "s", $position_id);
    mysqli_stmt_execute($stmt_check);
    $result_check = mysqli_stmt_get_result($stmt_check);
    $position_data = mysqli_fetch_assoc($result_check);

    if (!$position_data) {
        mysqli_stmt_close($stmt_check);
        http_response_code(404);
        echo json_encode(['status' => 'error', 'message' => 'Position not found.']);
        exit;
    }

    // Check if the position is being used in offboarding_list
    $sql_check_usage = "SELECT COUNT(*) as count FROM `offboarding_list` WHERE `position_id` = ?";
    $stmt_check_usage = mysqli_prepare($conn, $sql_check_usage);
    mysqli_stmt_bind_param($stmt_check_usage, "s", $position_id);
    mysqli_stmt_execute($stmt_check_usage);
    $result_check_usage = mysqli_stmt_get_result($stmt_check_usage);
    $usage_data = mysqli_fetch_assoc($result_check_usage);

    if ($usage_data['count'] > 0) {
        mysqli_stmt_close($stmt_check_usage);
        http_response_code(400);
        echo json_encode(['status' => 'error', 
                         'message' => 'Cannot delete this position as it is being used in offboarding records.']);
        exit;
    }
    mysqli_stmt_close($stmt_check_usage);

    // Delete the position
    $sql_delete = "DELETE FROM `position_list` WHERE `position_id` = ?";
    $stmt_delete = mysqli_prepare($conn, $sql_delete);
    mysqli_stmt_bind_param($stmt_delete, "s", $position_id);

    if (mysqli_stmt_execute($stmt_delete)) {
        mysqli_stmt_close($stmt_delete);
        http_response_code(200);
        $success_message = 'Position "' . htmlspecialchars($position_data['position_name']) . '" ';
        $success_message .= 'from department "' . htmlspecialchars($position_data['department_name']) . '" ';
        $success_message .= 'has been deleted successfully.';
        echo json_encode(['status' => 'success', 'message' => $success_message]);
    } else {
        mysqli_stmt_close($stmt_delete);
        http_response_code(500);
        echo json_encode(['status' => 'error', 'message' => 'Unable to delete the position.']);
    }
} else {
    http_response_code(400);
    echo json_encode(['status' => 'error', 'message' => 'Invalid request.']);
}
?>
