<?php
// Check if the request method is POST
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST["acc_id"])) {
        // Include your database connection file
        include "database.php"; // Adjust the path as per your file structure
        
        // Sanitize the input to prevent SQL injection
        $acc_id = mysqli_real_escape_string($conn, $_POST["acc_id"]);
        
        // Construct the SQL delete query for user-list table
        $sql = "DELETE FROM `user-list` WHERE `acc_id` = '$acc_id'";
        
        // Construct the SQL delete query for useracc table
        $sql2 = "DELETE FROM `useracc` WHERE `acc_id` = '$acc_id'";
        
        // Execute the delete queries
        if (mysqli_query($conn, $sql) && mysqli_query($conn, $sql2)) {
            // If deletion is successful, send a success response
            http_response_code(200);
            echo "User deleted successfully.";
        } else {
            // If deletion fails, send an error response
            http_response_code(500);
            echo "Error deleting user: " . mysqli_error($conn);
        }
        
        // Close the database connection
        mysqli_close($conn);
    } else {
        // If acc_id parameter is not set, send a bad request response
        http_response_code(400);
        echo "Error: User ID parameter is missing.";
    }
} else {
    // If the request method is not POST, send a method not allowed response
    http_response_code(405);
    echo "Error: Method not allowed.";
}
?>
