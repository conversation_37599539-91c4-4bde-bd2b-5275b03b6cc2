<?php 
require("database.php");

if (isset($_SESSION["user"])) {
    $email = $_SESSION["user"];
    $acc_id = $_SESSION["acc_id"];

    // Fetch user details
    $sql4 = "SELECT * FROM `user-list` WHERE `acc_id` = ?"; 
    $stmt = mysqli_prepare($conn, $sql4);
    mysqli_stmt_bind_param($stmt, "s", $acc_id);
    mysqli_stmt_execute($stmt);
    $result4 = mysqli_stmt_get_result($stmt);

    if ($row4 = mysqli_fetch_assoc($result4)) { 
        $fullname = $row4['name']; 
        $email2 = $row4['email']; 
        if (is_null($fullname) || trim($fullname) === "") {
            $fullname = $email2; 
        }
    } else {
        echo "No record found!";
        exit;
    }

    // Fetch the existing checklist details
    if (isset($_GET['checklist_id'])) {
        $checklist_id = $_GET['checklist_id'];

        $sql_fetch = "SELECT * FROM `offboarding_checklist` WHERE `checklist_id` = ?";
        $stmt_fetch = mysqli_prepare($conn, $sql_fetch);
        mysqli_stmt_bind_param($stmt_fetch, "s", $checklist_id);
        mysqli_stmt_execute($stmt_fetch);
        $result_fetch = mysqli_stmt_get_result($stmt_fetch);

        if ($row_fetch = mysqli_fetch_assoc($result_fetch)) {
            $checklist_name = $row_fetch['checklist_name'];
            $description = $row_fetch['description'];
        } else {
            echo "<script>alert('Checklist not found.'); window.location='checklist_list.php';</script>";
            exit;
        }
    } else {
        echo "<script>alert('No checklist ID provided.'); window.location='checklist_list.php';</script>";
        exit;
    }

    // Handle form submission for updating checklist
    if (isset($_POST["submit"])) {
        $checklist_name = $_POST["checklist_name"];
        $description = $_POST["description"];

        $update_sql = "UPDATE `offboarding_checklist` SET `checklist_name` = ?, `description` = ? WHERE `checklist_id` = ?";
        $stmt_update = mysqli_prepare($conn, $update_sql);
        mysqli_stmt_bind_param($stmt_update, "sss", $checklist_name, $description, $checklist_id);

        if (mysqli_stmt_execute($stmt_update)) {
            echo "<script>alert('The checklist has been updated successfully.'); window.location='checklist_list.php';</script>";
        } else {
            echo "<script>alert('Error updating checklist: " . mysqli_error($conn) . "');</script>";
        }

        mysqli_stmt_close($stmt_update);
    }
?>

<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0">
<meta name="description" content="CLL Ticketing System">
<meta name="author" content="Powered by CLL Systems Sdn Bhd">
<meta name="robots" content="noindex, nofollow">
<title>CLLXWARE - Edit Checklist</title>

<link rel='icon' type='image/x-icon' href='https://www.cllsystems.com/wp-content/themes/cllsystems/img/favicon.png'>
<link rel="stylesheet" href="assets/css/bootstrap.min.css">
<link rel="stylesheet" href="assets/css/animate.css">
<link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">
<link rel="stylesheet" href="assets/css/dataTables.bootstrap4.min.css">
<link rel="stylesheet" href="assets/plugins/fontawesome/css/fontawesome.min.css">
<link rel="stylesheet" href="assets/plugins/fontawesome/css/all.min.css">
<link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
<div id="global-loader">
    <div class="whirly-loader"></div>
</div>

<div class="main-wrapper">

<?php 
$role_id = $_SESSION["role_id"];
$sql20 = "SELECT * FROM `role-list` WHERE `role-id` = ?";
$stmt20 = mysqli_prepare($conn, $sql20);
mysqli_stmt_bind_param($stmt20, "s", $role_id);
mysqli_stmt_execute($stmt20);
$result20 = mysqli_stmt_get_result($stmt20);

if ($row20 = mysqli_fetch_assoc($result20)) {
    $all = $row20['offboarding_all'];
    if ($all != '1') {
        header("location: ./index.php");
        exit;
    }
} else {
    echo "<script>alert('Role data not found')</script>";
    exit;
}

mysqli_stmt_close($stmt20);
include("header.php");
?>

</div>

<div class="page-wrapper">
    <div class="content">
        <div class="page-header">
            <div class="page-title">
                <h4>Edit Checklist</h4>
                <h6>Update Checklist Information</h6>
            </div>
        </div>

        <div class="card">
            <div class="card-body">
                <div class="row">
                    <form action="#" method="post">
                        <div class="col-lg-3 col-sm-6 col-12">
                            <div class="form-group">
                                <label>Checklist Name</label>
                                <input type="text" name="checklist_name" required class="form-control" value="<?php echo htmlspecialchars($checklist_name); ?>">
                            </div>
                        </div>
                        <div class="col-lg-12">
                            <div class="form-group">
                                <label>Description</label>
                                <textarea class="form-control" name="description"><?php echo htmlspecialchars($description); ?></textarea>
                            </div>
                        </div>
                        <div class="col-lg-12">
                            <input type='submit' class='btn btn-submit me-2' name='submit' value='Update'>
                            <a href="checklist_list.php" class="btn btn-cancel">Cancel</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

    </div>
</div>
</div>

<script src="assets/js/jquery-3.6.0.min.js"></script>
<script src="assets/js/feather.min.js"></script>
<script src="assets/js/jquery.slimscroll.min.js"></script>
<script src="assets/js/jquery.dataTables.min.js"></script>
<script src="assets/js/dataTables.bootstrap4.min.js"></script>
<script src="assets/js/bootstrap.bundle.min.js"></script>
<script src="assets/plugins/select2/js/select2.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalert2.all.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalerts.min.js"></script>
<script src="assets/js/script.js"></script>
</body>
</html>
<?php
} else {
    header("location: ./signin.php");
    exit;
}
?>
