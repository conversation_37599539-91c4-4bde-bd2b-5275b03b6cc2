<?php 
require("database.php");

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (isset($_SESSION["user"])) {
    $email = $_SESSION["user"];
    $acc_id = $_SESSION["acc_id"];

    // Fetch user details
    $sql4 = "SELECT * FROM `user-list` WHERE `acc_id` = ?"; 
    $stmt = mysqli_prepare($conn, $sql4);
    mysqli_stmt_bind_param($stmt, "s", $acc_id);
    mysqli_stmt_execute($stmt);
    $result4 = mysqli_stmt_get_result($stmt);

    if ($row4 = mysqli_fetch_assoc($result4)) { 
        $fullname = $row4['name']; 
        $email2 = $row4['email']; 

        if (is_null($fullname) || trim($fullname) === "") {
            $fullname = $email2; 
        }
    } else {
        echo "No record found!";
    }

    // Fetch offboarding details for editing
    if (isset($_GET['offboarding_id'])) {
        $offboarding_id = $_GET['offboarding_id'];

        // Enhanced SQL Query with JOINs
        $sql_edit = "
            SELECT 
                ol.*, 
                pl.position_name, 
                dl.department_name, 
                ml.manager_name 
            FROM 
                `offboarding_list` ol
            LEFT JOIN 
                `position_list` pl ON ol.position_id = pl.position_id
            LEFT JOIN 
                `department_list` dl ON ol.department_id = dl.department_id
            LEFT JOIN 
                `manager_list` ml ON ol.reporting_manager_id = ml.manager_id
            WHERE 
                ol.offboarding_id = ?
        ";
        $stmt_edit = mysqli_prepare($conn, $sql_edit);
        mysqli_stmt_bind_param($stmt_edit, "s", $offboarding_id);
        mysqli_stmt_execute($stmt_edit);
        $result_edit = mysqli_stmt_get_result($stmt_edit);

        if ($row_edit = mysqli_fetch_assoc($result_edit)) {
            $employee_id = $row_edit["employee_id"];
            $employee_email = $row_edit["employee_email"]; // New Field
            $name = $row_edit["name"];
            $position = $row_edit["position_name"]; // Updated
            $department = $row_edit["department_name"]; // Updated
            $personal_email = $row_edit["personal_email"];
            $physical_last_day = $row_edit["physical_last_day"];
            $official_last_day = $row_edit["official_last_day"];
            $reporting_manager = $row_edit["manager_name"]; // Updated
            $remark = $row_edit["remark"];
        } else {
            echo "<script>alert('Offboarding record not found!'); window.location='offboarding_list.php';</script>";
            exit;
        }

        // Fetch checklist items
        $sql_checklist = "SELECT * FROM `offboarding_checklist`";
        $result_checklist = mysqli_query($conn, $sql_checklist);

        // Fetch current checklist status
        $checklist_status = [];
        $sql_checklist_status = "SELECT * FROM `offboarding_checklist_status` WHERE `offboarding_id` = ?";
        $stmt_checklist_status = mysqli_prepare($conn, $sql_checklist_status);
        mysqli_stmt_bind_param($stmt_checklist_status, "s", $offboarding_id);
        mysqli_stmt_execute($stmt_checklist_status);
        $result_checklist_status = mysqli_stmt_get_result($stmt_checklist_status);

        while ($row_checklist_status = mysqli_fetch_assoc($result_checklist_status)) {
            $checklist_status[$row_checklist_status['checklist_id']] = $row_checklist_status['is_checked'];
        }

        // Decode existing file paths
        $existingFiles = [];
        if (!empty($row_edit['file_path'])) {
            $existingFiles = json_decode($row_edit['file_path'], true);
        }
    }

    // Handle file deletion
    if (isset($_GET['delete_file'])) {
        $file_to_delete = $_GET['delete_file'];

        // Remove the file from the existing files array
        if (($key = array_search($file_to_delete, $existingFiles)) !== false) {
            unset($existingFiles[$key]);

            // Delete the file from the server
            if (file_exists($file_to_delete)) {
                unlink($file_to_delete);
            }

            // Update the database with the new file list
            $filePathsJson = json_encode(array_values($existingFiles));
            $sql_update_files = "UPDATE `offboarding_list` SET `file_path` = ? WHERE `offboarding_id` = ?";
            $stmt_update_files = mysqli_prepare($conn, $sql_update_files);
            mysqli_stmt_bind_param($stmt_update_files, "ss", $filePathsJson, $offboarding_id);
            mysqli_stmt_execute($stmt_update_files);

            echo "<script>alert('File deleted successfully.'); window.location='edit_offboarding.php?offboarding_id=$offboarding_id';</script>";
            exit;
        }
    }

    // Fetch data for dropdowns
    $sql_positions = "SELECT position_id, position_name FROM `position_list`";
    $result_positions = mysqli_query($conn, $sql_positions);

    $sql_departments = "SELECT department_id, department_name FROM `department_list`";
    $result_departments = mysqli_query($conn, $sql_departments);

    $sql_managers = "SELECT manager_id, manager_name FROM `manager_list`";
    $result_managers = mysqli_query($conn, $sql_managers);

    // Fetch persons in charge for multi-select (if applicable)
    $sql_persons_in_charge = "SELECT person_in_charge_id, person_in_charge_name, email_address FROM `person_in_charge_list`";
    $result_persons_in_charge = mysqli_query($conn, $sql_persons_in_charge);

    // Handle form submission
    if (isset($_POST["submit"])) {
        $employee_id = $_POST["employee_id"];
        $employee_email = $_POST["employee_email"]; // New Field
        $name = $_POST["name"];
        $position = $_POST["position"];
        $department = $_POST["department"];
        $personal_email = $_POST["personal_email"];
        $physical_last_day = $_POST["physical_last_day"];
        $official_last_day = $_POST["official_last_day"];
        $reporting_manager = $_POST["reporting_manager"];
        $remark = $_POST["remark"]; // Handle remark

        // Handle file uploads
        $fileDestinations = $existingFiles; // Start with existing files
        if (isset($_FILES['uploaded_files']) && $_FILES['uploaded_files']['error'][0] !== UPLOAD_ERR_NO_FILE) {
            // Define maximum file size (250MB in bytes)
            $maxFileSize = 250 * 1024 * 1024; // 250MB

            // Allowed file extensions
            $allowed = array('jpg', 'jpeg', 'png', 'gif', 'pdf', 'xls', 'xlsx', 'csv');

            // Loop through each uploaded file
            $totalFiles = count($_FILES['uploaded_files']['name']);
            for ($i = 0; $i < $totalFiles; $i++) {
                // Get file details
                $fileName = $_FILES['uploaded_files']['name'][$i];
                $fileTmpName = $_FILES['uploaded_files']['tmp_name'][$i];
                $fileSize = $_FILES['uploaded_files']['size'][$i];
                $fileError = $_FILES['uploaded_files']['error'][$i];
                $fileType = $_FILES['uploaded_files']['type'][$i];

                // Get file extension
                $fileExt = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));

                // Check if file extension is allowed
                if (in_array($fileExt, $allowed)) {
                    // Check file size
                    if ($fileSize <= $maxFileSize) {
                        if ($fileError === 0) {
                            // Generate unique file name
                            $newFileName = uniqid('', true) . "." . $fileExt;
                            // Define upload directory
                            $uploadDirectory = 'uploads/offboarding_files/';
                            // Create directory if it doesn't exist
                            if (!is_dir($uploadDirectory)) {
                                mkdir($uploadDirectory, 0755, true);
                            }
                            // Move uploaded file
                            $fileDestination = $uploadDirectory . $newFileName;
                            if (move_uploaded_file($fileTmpName, $fileDestination)) {
                                $fileDestinations[] = $fileDestination;
                            } else {
                                echo "<script>alert('Error uploading the file: $fileName'); window.location='edit_offboarding.php?offboarding_id=$offboarding_id';</script>";
                                exit;
                            }
                        } else {
                            echo "<script>alert('Error with the file upload: $fileName. Error code: $fileError'); window.location='edit_offboarding.php?offboarding_id=$offboarding_id';</script>";
                            exit;
                        }
                    } else {
                        echo "<script>alert('File size exceeds 250MB limit for file: $fileName'); window.location='edit_offboarding.php?offboarding_id=$offboarding_id';</script>";
                        exit;
                    }
                } else {
                    echo "<script>alert('Invalid file type for file: $fileName. Allowed types: JPG, JPEG, PNG, GIF, PDF, XLS, XLSX, CSV.'); window.location='edit_offboarding.php?offboarding_id=$offboarding_id';</script>";
                    exit;
                }
            }
        }

        // Encode file paths as JSON
        $filePathsJson = json_encode(array_values($fileDestinations));

        // Update the offboarding details
        $sql_update = "
            UPDATE `offboarding_list` 
            SET 
                `employee_id` = ?, 
                `employee_email` = ?, 
                `name` = ?, 
                `position_id` = ?, 
                `department_id` = ?, 
                `personal_email` = ?, 
                `physical_last_day` = ?, 
                `official_last_day` = ?, 
                `reporting_manager_id` = ?, 
                `remark` = ?, 
                `file_path` = ?
            WHERE 
                `offboarding_id` = ?
        ";
        $stmt_update = mysqli_prepare($conn, $sql_update);
        mysqli_stmt_bind_param($stmt_update, "ssssssssssss", 
            $employee_id, 
            $employee_email, // Bind the new field
            $name, 
            $position, 
            $department, 
            $personal_email, 
            $physical_last_day, 
            $official_last_day, 
            $reporting_manager, 
            $remark, 
            $filePathsJson, 
            $offboarding_id
        );

        if (mysqli_stmt_execute($stmt_update)) {
            // Delete old checklist status and insert new ones
            $sql_delete_checklist_status = "DELETE FROM `offboarding_checklist_status` WHERE `offboarding_id` = ?";
            $stmt_delete_checklist_status = mysqli_prepare($conn, $sql_delete_checklist_status);
            mysqli_stmt_bind_param($stmt_delete_checklist_status, "s", $offboarding_id);
            mysqli_stmt_execute($stmt_delete_checklist_status);
            mysqli_stmt_close($stmt_delete_checklist_status);

            if (isset($_POST['checklist'])) {
                foreach ($_POST['checklist'] as $checklist_id => $value) {
                    $is_checked = 1; 
                    $sql_checklist_status = "
                        REPLACE INTO `offboarding_checklist_status` 
                        (`offboarding_id`, `checklist_id`, `is_checked`) 
                        VALUES (?, ?, ?)
                    ";
                    $stmt_checklist_status = mysqli_prepare($conn, $sql_checklist_status);
                    mysqli_stmt_bind_param($stmt_checklist_status, "ssi", $offboarding_id, $checklist_id, $is_checked);
                    mysqli_stmt_execute($stmt_checklist_status);
                    mysqli_stmt_close($stmt_checklist_status);
                }
            }

            echo "<script>alert('The offboarding has been updated successfully.'); window.location='offboarding_list.php';</script>";
        } else {
            echo "<script>alert('Error: " . mysqli_error($conn) . "'); window.location='edit_offboarding.php?offboarding_id=$offboarding_id';</script>";
        }

        mysqli_stmt_close($stmt_update);
    }
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0">
    <meta name="description" content="CLL Ticketing System">
    <meta name="author" content="Powered by CLL Systems Sdn Bhd">
    <meta name="robots" content="noindex, nofollow">
    <title>CLLXWARE - Edit Offboarding Form</title>

    <!-- Favicon -->
    <link rel='icon' type='image/x-icon' href='https://www.cllsystems.com/wp-content/themes/cllsystems/img/favicon.png'>

    <!-- Stylesheets -->
    <link rel="stylesheet" href="assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="assets/css/animate.css">
    <link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">
    <link rel="stylesheet" href="assets/css/dataTables.bootstrap4.min.css">
    <link rel="stylesheet" href="assets/plugins/fontawesome/css/fontawesome.min.css">
    <link rel="stylesheet" href="assets/plugins/fontawesome/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">

    <!-- Custom CSS for better form design -->
    <style>
        .form-check-input {
            margin-left: 0;
            margin-top: 0.3rem;
        }
        .form-check-label {
            margin-left: 1.5rem;
        }
        .form-section-title {
            margin-bottom: 20px;
            font-size: 18px;
            font-weight: bold;
            color: #000;
            text-transform: uppercase;
        }
        .card {
            border: 1px solid #e0e0e0;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .existing-files img {
            max-width: 100px;
            max-height: 100px;
        }
        .delete-file {
            margin-left: 15px;
            color: red;
            text-decoration: none;
            font-weight: bold;
        }
        .delete-file:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
<div id="global-loader">
        <div class="whirly-loader"></div>
    </div>

<div class="main-wrapper">
<?php 
    // Role-based access control
    $role_id = $_SESSION["role_id"];
    $sql20 = "SELECT * FROM `role-list` WHERE `role-id` = ?";
    $stmt20 = mysqli_prepare($conn, $sql20);
    mysqli_stmt_bind_param($stmt20, "s", $role_id);
    mysqli_stmt_execute($stmt20);
    $result20 = mysqli_stmt_get_result($stmt20);

    if ($row20 = mysqli_fetch_assoc($result20)) {
        $edit = $row20['offboarding_edit'];
        $all = $row20['offboarding_all'];
        if($edit != '1' and $all !='1'){
            header("location: ./index.php");
            exit;
        }
    } else {
        echo "<script>alert('Role data not found');</script>";
        exit;
    }

    mysqli_stmt_close($stmt20);
    include("header.php");
?>

    <div class="page-wrapper">
        <div class="content">
            <div class="page-header">
                <div class="page-title">
                    <h4>Edit Offboarding Form</h4>
                    <h6>Update the details below to complete the offboarding process.</h6>
                </div>
            </div>

            <form action="#" method="post" enctype="multipart/form-data">
                <div class="card">
                    <div class="card-body">
                        <div class="form-section-title">Employee Information</div>
                        <div class="row">
                            <!-- Employee ID -->
                            <div class="col-md-6 col-sm-12">
                                <div class="form-group">
                                    <label>Employee ID *</label>
                                    <input type="text" name="employee_id" required class="form-control" value="<?php echo htmlspecialchars($employee_id); ?>">
                                </div>
                            </div>

                            <!-- Name -->
                            <div class="col-md-6 col-sm-12">
                                <div class="form-group">
                                    <label>Name *</label>
                                    <input type="text" name="name" required class="form-control" value="<?php echo htmlspecialchars($name); ?>">
                                </div>
                            </div>

                             <!-- Position -->
                             <div class="col-md-6 col-sm-12">
                                <div class="form-group">
                                    <label>Position *</label>
                                    <select name="position" class="form-control select" required>
                                        <option value="" disabled>Select Position</option>
                                        <?php 
                                            mysqli_data_seek($result_positions, 0);
                                            while ($row_position = mysqli_fetch_assoc($result_positions)) : 
                                        ?>
                                            <option value="<?php echo htmlspecialchars($row_position['position_id']); ?>" 
                                                <?php echo ($row_edit['position_id'] == $row_position['position_id']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($row_position['position_name']); ?>
                                            </option>
                                        <?php endwhile; ?>
                                    </select>
                                </div>
                            </div>

                            <!-- Department -->
                            <div class="col-md-6 col-sm-12">
                                <div class="form-group">
                                    <label>Department *</label>
                                    <select name="department" class="form-control select" required>
                                        <option value="" disabled>Select Department</option>
                                        <?php 
                                            mysqli_data_seek($result_departments, 0);
                                            while ($row_department = mysqli_fetch_assoc($result_departments)) : 
                                        ?>
                                            <option value="<?php echo htmlspecialchars($row_department['department_id']); ?>" 
                                                <?php echo ($row_edit['department_id'] == $row_department['department_id']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($row_department['department_name']); ?>
                                            </option>
                                        <?php endwhile; ?>
                                    </select>
                                </div>
                            </div>

                            <!-- Employee Email -->
                            <div class="col-md-6 col-sm-12">
                                <div class="form-group">
                                    <label>Employee Email *</label>
                                    <input type="email" name="employee_email" required class="form-control" value="<?php echo htmlspecialchars($employee_email); ?>">
                                </div>
                            </div>
                            
                            <!-- Personal Email -->
                            <div class="col-md-6 col-sm-12">
                                <div class="form-group">
                                    <label>Personal Email *</label>
                                    <input type="email" name="personal_email" required class="form-control" value="<?php echo htmlspecialchars($personal_email); ?>">
                                </div>
                            </div>

                            <!-- Physical Last Day -->
                            <div class="col-md-6 col-sm-12">
                                <div class="form-group">
                                    <label>Physical Last Day *</label>
                                    <input type="date" name="physical_last_day" required class="form-control" value="<?php echo htmlspecialchars($physical_last_day); ?>">
                                </div>
                            </div>

                            <!-- Official Last Day -->
                            <div class="col-md-6 col-sm-12">
                                <div class="form-group">
                                    <label>Official Last Day *</label>
                                    <input type="date" name="official_last_day" required class="form-control" value="<?php echo htmlspecialchars($official_last_day); ?>">
                                </div>
                            </div>
                        </div>

                            <!-- Reporting Manager -->
                            <div class="col-md-6 col-sm-12">
                                <div class="form-group">
                                    <label>Reporting Manager *</label>
                                    <select name="reporting_manager" class="form-control select" required>
                                        <option value="" disabled>Select Reporting Manager</option>
                                        <?php 
                                            mysqli_data_seek($result_managers, 0);
                                            while ($row_manager = mysqli_fetch_assoc($result_managers)) : 
                                        ?>
                                            <option value="<?php echo htmlspecialchars($row_manager['manager_id']); ?>" 
                                                <?php echo ($row_edit['reporting_manager_id'] == $row_manager['manager_id']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($row_manager['manager_name']); ?>
                                            </option>
                                        <?php endwhile; ?>
                                    </select>
                                </div>
                            </div>

                        <div class="form-section-title">Offboarding Checklist</div>
                        <div class="row">
                            <?php 
                                mysqli_data_seek($result_checklist, 0);
                                while ($checklist_item = mysqli_fetch_assoc($result_checklist)) : 
                            ?>
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" name="checklist[<?php echo htmlspecialchars($checklist_item['checklist_id']); ?>]" 
                                            <?php echo isset($checklist_status[$checklist_item['checklist_id']]) && $checklist_status[$checklist_item['checklist_id']] ? 'checked' : ''; ?>>
                                        <label class="form-check-label"><?php echo htmlspecialchars($checklist_item['checklist_name']); ?></label>
                                    </div>
                                </div>
                            <?php endwhile; ?>
                        </div>
                        <br>

                           <!-- Existing Files Display -->
                           <?php if (!empty($existingFiles)): ?>
                                <div class="col-lg-12">
                                    <div class="form-group">
                                        <label>Existing Files</label>
                                        <div class="existing-files">
                                            <?php foreach ($existingFiles as $file_path): ?>
                                                <?php
                                                    $file_name = basename($file_path);
                                                    $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));
                                                ?>
                                                <div style="margin-bottom: 10px; display: flex; align-items: center;">
                                                    <?php if (in_array($file_ext, ['jpg', 'jpeg', 'png', 'gif'])): ?>
                                                        <a href="<?php echo htmlspecialchars($file_path); ?>" download>
                                                            <img src="<?php echo htmlspecialchars($file_path); ?>" alt="<?php echo htmlspecialchars($file_name); ?>">
                                                        </a>
                                                    <?php else: ?>
                                                        <a href="<?php echo htmlspecialchars($file_path); ?>" download><?php echo htmlspecialchars($file_name); ?></a>
                                                    <?php endif; ?>
                                                    <!-- Delete link -->
                                                    <a href="edit_offboarding.php?offboarding_id=<?php echo $offboarding_id; ?>&delete_file=<?php echo urlencode($file_path); ?>" class="delete-file">Delete</a>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>

                        <!-- File or Image Upload Section -->
                        <div class="col-lg-12">
                            <div class="form-group">
                                <label>File or Image</label>
                                <div class="image-upload">
                                    <input type="file" name="uploaded_files[]" id="imageInput" multiple 
                                           onchange="handleFileUpload(event)" 
                                           accept=".jpg, .jpeg, .png, .gif, .pdf, .xls, .xlsx, .csv">
                                    <div class="image-uploads" id="uploadPlaceholder">
                                        <img src="assets/img/icons/upload.svg" alt="img">
                                        <h4>Drag and drop files to upload</h4>
                                    </div>
                                    <div id="uploadedFiles" style="margin-top: 10px; display: none;"></div>
                                </div>
                                <small>Maximum file size: 250MB per file. Allowed types: JPG, JPEG, PNG, GIF, PDF, XLS, XLSX, CSV.</small>
                            </div>
                        </div>

                        <!-- Remark Section -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label>Remark</label>
                                    <textarea name="remark" class="form-control" rows="4"><?php echo htmlspecialchars($remark); ?></textarea>
                                </div>
                            </div>
                        </div>
                        <br>

                        <!-- Submit Button -->
                        <div class="col-lg-12">
                            <input type='submit' class='btn btn-primary me-2' name='submit' value='Update'>
                            <a href="offboarding_list.php" class="btn btn-secondary">Cancel</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Scripts -->
<script src="assets/js/jquery-3.6.0.min.js"></script>
<script src="assets/js/feather.min.js"></script>
<script src="assets/js/jquery.slimscroll.min.js"></script>
<script src="assets/js/jquery.dataTables.min.js"></script>
<script src="assets/js/dataTables.bootstrap4.min.js"></script>
<script src="assets/js/bootstrap.bundle.min.js"></script>
<script src="assets/plugins/select2/js/select2.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalert2.all.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalerts.min.js"></script>
<script src="assets/js/script.js"></script>

<!-- JavaScript for Handling File Uploads and Downloads -->
<script>
    // Initialize Select2 for better multi-select UI
    $(document).ready(function() {
        $('.select').select2({
            placeholder: "Select an option",
            allowClear: true
        });
    });

    function handleFileUpload(event) {
        const files = event.target.files;
        const uploadedFilesDiv = document.getElementById("uploadedFiles");
        const uploadPlaceholder = document.getElementById("uploadPlaceholder");

        if (files.length > 0) {
            // Hide placeholder
            uploadPlaceholder.style.display = "none";

            // Show uploaded files
            uploadedFilesDiv.style.display = "block";
            uploadedFilesDiv.innerHTML = ""; // Clear previous content

            Array.from(files).forEach((file) => {
                const fileDiv = document.createElement("div");
                fileDiv.style.marginBottom = "10px";

                // Create a download link for each file
                const downloadLink = document.createElement("a");
                downloadLink.href = URL.createObjectURL(file);
                downloadLink.download = file.name; // Sets the filename for download
                downloadLink.style.textDecoration = "none";
                downloadLink.style.color = "blue";
                downloadLink.style.display = "flex";
                downloadLink.style.alignItems = "center";

                const fileType = file.type.split('/')[0];
                const fileExt = file.name.split('.').pop().toLowerCase();

                if (fileType === "image") {
                    const img = document.createElement("img");
                    img.src = URL.createObjectURL(file);
                    img.style.maxWidth = "100px";
                    img.style.maxHeight = "100px";
                    img.style.marginRight = "10px";
                    downloadLink.appendChild(img);
                } else if (fileExt === "csv") {
                    // Add a CSV icon
                    const csvIcon = document.createElement("img");
                    csvIcon.src = "assets/img/icons/csv-icon.svg"; // Ensure you have a CSV icon
                    csvIcon.alt = "CSV Icon";
                    csvIcon.style.width = "24px";
                    csvIcon.style.height = "24px";
                    csvIcon.style.marginRight = "10px";
                    downloadLink.appendChild(csvIcon);
                } else {
                    // Add a generic file icon
                    const fileIcon = document.createElement("img");
                    fileIcon.src = "assets/img/icons/file-icon.svg"; // Ensure you have a file icon
                    fileIcon.alt = "File Icon";
                    fileIcon.style.width = "24px";
                    fileIcon.style.height = "24px";
                    fileIcon.style.marginRight = "10px";
                    downloadLink.appendChild(fileIcon);
                }

                const fileName = document.createElement("span");
                fileName.textContent = file.name;
                fileName.style.marginLeft = "10px";
                downloadLink.appendChild(fileName);

                fileDiv.appendChild(downloadLink);
                uploadedFilesDiv.appendChild(fileDiv);
            });
        } else {
            // Show placeholder if no files are selected
            uploadPlaceholder.style.display = "block";
            uploadedFilesDiv.style.display = "none";
        }
    }
</script>

</body>
</html>
<?php
} else {
    header("location: ./signin.php");
    exit;
}
?>
