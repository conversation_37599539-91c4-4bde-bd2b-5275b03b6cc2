<?php
require("database.php");

if (isset($_SESSION["user"])) {
    $email = $_SESSION["user"];
    $acc_id = $_SESSION["acc_id"];

    // Fetch user details
    $sql4 = "SELECT * FROM `user-list` WHERE `acc_id` = ?";
    $stmt = mysqli_prepare($conn, $sql4);
    mysqli_stmt_bind_param($stmt, "s", $acc_id);
    mysqli_stmt_execute($stmt);
    $result4 = mysqli_stmt_get_result($stmt);

    if ($row4 = mysqli_fetch_assoc($result4)) {
        $fullname = $row4['name'];
        $email2 = $row4['email'];

        // Use email if full name is empty
        if (is_null($fullname) || trim($fullname) === "") {
            $fullname = $email2;
        }
    } else {
        echo "No record found!";
    }

    // Fetch onboarding details for editing
    if (isset($_GET['onboarding_id'])) {
        $onboarding_id = $_GET['onboarding_id'];

        $sql_edit = "SELECT ol.*,
                            dl.department_name as department,
                            ml.manager_name as reporting_manager,
                            ofl.office_name as office_name
                     FROM `onboarding_list` ol
                     LEFT JOIN `department_list` dl ON ol.department_id = dl.department_id
                     LEFT JOIN `manager_list` ml ON ol.manager_id = ml.manager_id
                     LEFT JOIN `office_list` ofl ON ol.office_id = ofl.office_id
                     WHERE ol.`onboarding_id` = ?";
        $stmt_edit = mysqli_prepare($conn, $sql_edit);
        mysqli_stmt_bind_param($stmt_edit, "s", $onboarding_id);
        mysqli_stmt_execute($stmt_edit);
        $result_edit = mysqli_stmt_get_result($stmt_edit);

        if ($row_edit = mysqli_fetch_assoc($result_edit)) {
            // Store the current data
            $current_data = $row_edit;
        } else {
            echo "<script>alert('Onboarding record not found.'); window.location='onboarding_list.php';</script>";
            exit;
        }
        mysqli_stmt_close($stmt_edit);
    } else {
        echo "<script>alert('No onboarding ID provided.'); window.location='onboarding_list.php';</script>";
        exit;
    }

    // Handle form submission for update
    if (isset($_POST["submit"])) {
        // Ensure required columns exist
        function ensure_column_exists($conn, $table, $column, $definition) {
            $result = mysqli_query($conn, "SHOW COLUMNS FROM `$table` LIKE '$column'");
            if (mysqli_num_rows($result) == 0) {
                mysqli_query($conn, "ALTER TABLE `$table` ADD COLUMN `$column` $definition");
            }
        }

        ensure_column_exists($conn, 'onboarding_list', 'company_email_password', 'varchar(255) NULL');
        ensure_column_exists($conn, 'onboarding_list', 'company_email_set', 'TINYINT(1) NOT NULL DEFAULT 0');
        ensure_column_exists($conn, 'onboarding_list', 'setup_token', 'varchar(255) NULL');
        ensure_column_exists($conn, 'onboarding_list', 'laptop_recipients', 'TEXT NULL');
        ensure_column_exists($conn, 'onboarding_list', 'laptop_email_sent', 'TINYINT(1) NOT NULL DEFAULT 0');

        // Get form input data
        $full_name = $_POST["full_name"];
        $employee_id = $_POST["employee_id"];
        $preferred_name = $_POST["preferred_name"];
        $department = $_POST["department"];
        $ic_number = $_POST["ic_number"];
        $gender = $_POST["gender"];
        $job_title = $_POST["job_title"];
        $personal_email = $_POST["personal_email"];
        $address = $_POST["address"];
        $phone_number = $_POST["phone_number"];
        $company_email = $_POST["company_email"];
        $reporting_manager = $_POST["reporting_manager"];
        $onboarding_date = $_POST["onboarding_date"];
        $office_name = $_POST["office_name"];

        // Handle file upload or deletion for personal image
        $personal_image = $current_data['personal_image']; // Keep existing image by default
        $delete_image = (isset($_POST['delete_image']) && $_POST['delete_image'] === '1');

        if (isset($_FILES['personal_image']) && $_FILES['personal_image']['error'] == 0) {
            $target_dir = "uploads/onboarding_images/";
            if (!file_exists($target_dir)) {
                mkdir($target_dir, 0777, true);
            }
            $file_extension = pathinfo($_FILES['personal_image']['name'], PATHINFO_EXTENSION);
            $unique_filename = uniqid() . '.' . $file_extension;
            $target_file = $target_dir . $unique_filename;
            if (move_uploaded_file($_FILES['personal_image']['tmp_name'], $target_file)) {
                // Delete old image if it exists
                if (!empty($current_data['personal_image']) && file_exists($current_data['personal_image'])) {
                    @unlink($current_data['personal_image']);
                }
                $personal_image = $target_file;
            }
        } elseif ($delete_image) {
            // Delete without uploading a new one
            if (!empty($current_data['personal_image']) && file_exists($current_data['personal_image'])) {
                @unlink($current_data['personal_image']);
            }
            $personal_image = '';
        }

        // Update onboarding data in the database
        $sql = "UPDATE `onboarding_list` SET `full_name` = ?, `employee_id` = ?, `preferred_name` = ?, `department_id` = ?, `ic_number` = ?, `gender` = ?, `job_title` = ?, `personal_email` = ?, `address` = ?, `phone_number` = ?, `company_email` = ?, `manager_id` = ?, `onboarding_date` = ?, `office_id` = ?, `personal_image` = ? WHERE `onboarding_id` = ?";
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, "ssssssssssssssss", $full_name, $employee_id, $preferred_name, $department, $ic_number, $gender, $job_title, $personal_email, $address, $phone_number, $company_email, $reporting_manager, $onboarding_date, $office_name, $personal_image, $onboarding_id);

        if (mysqli_stmt_execute($stmt)) {
            echo "<script>alert('The onboarding has been updated.'); window.location='onboarding_list.php';</script>";
        } else {
            echo "<script>alert('Error: " . mysqli_error($conn) . "'); window.location='edit_onboarding.php?onboarding_id=$onboarding_id';</script>";
        }

        mysqli_stmt_close($stmt);
    }
} else {
    header("location: ./signin.php");
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0">
    <meta name="description" content="CLL Warehouse Inventory System">
    <meta name="author" content="Powered by CLL Systems Sdn Bhd">
    <meta name="robots" content="noindex, nofollow">
    <title>CLLXWARE - Edit Onboarding</title>

    <link rel='icon' type='image/x-icon' href='https://www.cllsystems.com/wp-content/themes/cllsystems/img/favicon.png'>
    <link rel="stylesheet" href="assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="assets/css/animate.css">
    <link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">
    <link rel="stylesheet" href="assets/css/dataTables.bootstrap4.min.css">
    <link rel="stylesheet" href="assets/plugins/fontawesome/css/fontawesome.min.css">
    <link rel="stylesheet" href="assets/plugins/fontawesome/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .file-name {
            margin-top: 8px;
            padding: 6px 10px;
            font-size: 13px;
            color: #28a745;
            background-color: #f8f9fa;
            border: 1px solid #d4edda;
            border-radius: 4px;
            font-weight: 500;
        }
    </style>
</head>
<body>
<div id="global-loader">
    <div class="whirly-loader"> </div>
</div>

<div class="main-wrapper">
    <?php
    $role_id = $_SESSION["role_id"];
    $sql20 = "SELECT * FROM `role-list` WHERE `role-id` = ?";
    $stmt20 = mysqli_prepare($conn, $sql20);
    mysqli_stmt_bind_param($stmt20, "s", $role_id);
    mysqli_stmt_execute($stmt20);
    $result20 = mysqli_stmt_get_result($stmt20);

    if ($row20 = mysqli_fetch_assoc($result20)) {
        $all = $row20['onboarding_all'];
        $edit = $row20['onboarding_edit'];
        if($edit != '1' and $all !='1'){
            header("location: ./index.php");
            exit;
        }
    } else {
        echo "<script>alert('Role data not found');</script>";
        exit;
    }

    mysqli_stmt_close($stmt20);
    include("header.php");
    ?>

    <div class="page-wrapper">
        <div class="content">
            <div class="page-header">
                <div class="page-title">
                    <h4>Edit Onboarding</h4>
                    <h6>Update onboarding information</h6>
                </div>
            </div>

            <form action="#" method="post" enctype="multipart/form-data">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <!-- Email account setup status -->
                            <?php if (!empty($current_data['company_email_set'])): ?>
                                <div class="col-12">
                                    <div class="alert alert-success py-2">Company email/password has been set.</div>
                                </div>
                            <?php endif; ?>

                        <div class="row">
                            <!-- Full Name -->
                            <div class="col-lg-6 col-sm-6 col-12">
                                <div class="form-group">
                                    <label>Full Name *</label>
                                    <input type="text" name="full_name" required class="form-control" value="<?php echo htmlspecialchars($current_data['full_name']); ?>">
                                </div>
                            </div>

                            <!-- Employee ID -->
                            <div class="col-lg-6 col-sm-6 col-12">
                                <div class="form-group">
                                    <label>Employee ID *</label>
                                    <input type="text" name="employee_id" required class="form-control" value="<?php echo htmlspecialchars($current_data['employee_id']); ?>">
                                </div>
                            </div>

                            <!-- Preferred Name -->
                            <div class="col-lg-6 col-sm-6 col-12">
                                <div class="form-group">
                                    <label>Preferred Name</label>
                                    <input type="text" name="preferred_name" class="form-control" value="<?php echo htmlspecialchars($current_data['preferred_name']); ?>">
                                </div>
                            </div>

                            <!-- Department -->
                            <div class="col-lg-6 col-sm-6 col-12">
                                <div class="form-group">
                                    <label>Department *</label>
                                    <select class="select" name="department" required>
                                        <option value="" disabled>Choose Department</option>
                                        <?php
                                        $dept_sql = "SELECT * FROM `department_list` ORDER BY `department_name` ASC";
                                        $dept_result = mysqli_query($conn, $dept_sql);
                                        if ($dept_result && mysqli_num_rows($dept_result) > 0) {
                                            while ($dept_row = mysqli_fetch_assoc($dept_result)) {
                                                $selected = ($dept_row['department_id'] == $current_data['department_id']) ? 'selected' : '';
                                                echo "<option value='" . $dept_row['department_id'] . "' $selected>" . $dept_row['department_name'] . "</option>";
                                            }
                                        }
                                        ?>
                                    </select>
                                </div>
                            </div>

                            <!-- IC Number -->
                            <div class="col-lg-6 col-sm-6 col-12">
                                <div class="form-group">
                                    <label>IC Number *</label>
                                    <input type="text" name="ic_number" required class="form-control" value="<?php echo htmlspecialchars($current_data['ic_number']); ?>">
                                </div>
                            </div>

                            <!-- Gender -->
                            <div class="col-lg-6 col-sm-6 col-12">
                                <div class="form-group">
                                    <label>Gender *</label>
                                    <select class="select" name="gender" required>
                                        <option value="" disabled>Choose Gender</option>
                                        <option value="Male" <?php echo ($current_data['gender'] == 'Male') ? 'selected' : ''; ?>>Male</option>
                                        <option value="Female" <?php echo ($current_data['gender'] == 'Female') ? 'selected' : ''; ?>>Female</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Job Title -->
                            <div class="col-lg-6 col-sm-6 col-12">
                                <div class="form-group">
                                    <label>Job Title *</label>
                                    <input type="text" name="job_title" required class="form-control" value="<?php echo htmlspecialchars($current_data['job_title']); ?>">
                                </div>
                            </div>

                            <!-- Personal Email -->
                            <div class="col-lg-6 col-sm-6 col-12">
                                <div class="form-group">
                                    <label>Personal Email *</label>
                                    <input type="email" name="personal_email" required class="form-control" value="<?php echo htmlspecialchars($current_data['personal_email']); ?>">
                                </div>
                            </div>

                            <!-- Address -->
                            <div class="col-lg-12">
                                <div class="form-group">
                                    <label>Address</label>
                                    <textarea class="form-control" name="address" rows="3"><?php echo htmlspecialchars($current_data['address']); ?></textarea>
                                </div>
                            </div>

                            <!-- Phone Number -->
                            <div class="col-lg-6 col-sm-6 col-12">
                                <div class="form-group">
                                    <label>Phone Number *</label>
                                    <input type="text" name="phone_number" required class="form-control" value="<?php echo htmlspecialchars($current_data['phone_number']); ?>">
                                </div>
                            </div>

                            <!-- Company Email -->
                            <div class="col-lg-6 col-sm-6 col-12">
                                <div class="form-group">
                                    <label>Company Email</label>
                                    <input type="email" name="company_email" class="form-control" value="<?php echo htmlspecialchars($current_data['company_email']); ?>">
                                </div>
                            </div>

                            <!-- Reporting Manager -->
                            <div class="col-lg-6 col-sm-6 col-12">
                                <div class="form-group">
                                    <label>Reporting Manager</label>
                                    <select class="select" name="reporting_manager">
                                        <option value="" disabled>Choose Manager</option>
                                        <?php
                                        $manager_sql = "SELECT * FROM `manager_list` ORDER BY `manager_name` ASC";
                                        $manager_result = mysqli_query($conn, $manager_sql);
                                        if ($manager_result && mysqli_num_rows($manager_result) > 0) {
                                            while ($manager_row = mysqli_fetch_assoc($manager_result)) {
                                                $selected = ($manager_row['manager_id'] == $current_data['manager_id']) ? 'selected' : '';
                                                echo "<option value='" . $manager_row['manager_id'] . "' $selected>" . $manager_row['manager_name'] . "</option>";
                                            }
                                        }
                                        ?>
                                    </select>
                                </div>
                            </div>

                            <!-- On Boarding Date -->
                            <div class="col-lg-6 col-sm-6 col-12">
                                <div class="form-group">
                                    <label>On Boarding Date *</label>
                                    <input type="date" name="onboarding_date" required class="form-control" value="<?php echo htmlspecialchars($current_data['onboarding_date']); ?>">
                                </div>
                            </div>

                            <!-- Office Name -->
                            <div class="col-lg-6 col-sm-6 col-12">
                                <div class="form-group">
                                    <label>Office Name</label>
                                    <select class="select" name="office_name">
                                        <option value="" disabled>Choose Office</option>
                                        <?php
                                        $office_sql = "SELECT * FROM `office_list` ORDER BY `office_name` ASC";
                                        $office_result = mysqli_query($conn, $office_sql);
                                        if ($office_result && mysqli_num_rows($office_result) > 0) {
                                            while ($office_row = mysqli_fetch_assoc($office_result)) {
                                                $selected = ($office_row['office_id'] == $current_data['office_id']) ? 'selected' : '';
                                                echo "<option value='" . $office_row['office_id'] . "' $selected>" . $office_row['office_name'] . "</option>";
                                            }
                                        }
                                        ?>
                                    </select>
                                </div>
                            </div>

                            <!-- Personal Image -->
                            <div class="col-lg-6 col-sm-6 col-12">
                                <div class="form-group">
                                    <label>Personal Image</label>
                                    <?php if (!empty($current_data['personal_image']) && file_exists($current_data['personal_image'])): ?>
                                        <div class="mb-2 d-flex align-items-start gap-2">
                                            <img src="<?php echo $current_data['personal_image']; ?>" alt="Current Image" style="max-width: 120px; max-height: 120px; border-radius:8px;">
                                            <button type="button" id="mark_delete_image" class="btn btn-sm btn-danger" title="Remove image">
                                                <i class="fa fa-times"></i>
                                            </button>
                                        </div>
                                        <input type="hidden" id="delete_image" name="delete_image" value="0">
                                    <?php endif; ?>
                                    <div class="image-upload">
                                        <input type="file" id="personal_image" name="personal_image" accept="image/*">
                                        <div class="image-uploads">
                                            <img src="assets/img/icons/upload.svg" alt="img">
                                            <h4>Drag and drop a file to upload</h4>
                                        </div>
                                        <div id="personal_image_name" class="file-name" style="display:none;"></div>
                                    </div>
                                </div>
                            </div>




                            <!-- Submit Button -->

                            <div class="col-lg-12">
                                <input type="submit" class="btn btn-submit me-2" name="submit" value="Update">
                                <a href="onboarding_list.php" class="btn btn-cancel">Cancel</a>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
(function(){
  const input=document.getElementById('personal_image');
  const nameBox=document.getElementById('personal_image_name');
  if(input&&nameBox){
    input.addEventListener('change',function(){
      if(this.files&&this.files.length>0){
        nameBox.textContent=this.files[0].name;
        nameBox.style.display='block';
      }else{ nameBox.textContent=''; nameBox.style.display='none'; }
    });
  }
})();



// Mark delete image only when user clicks X; actual deletion occurs on Update
const delBtn = document.getElementById('mark_delete_image');
const delField = document.getElementById('delete_image');
if(delBtn && delField){
  delBtn.addEventListener('click', function(){
    delField.value = '1';
    delBtn.classList.add('disabled');
    delBtn.innerHTML = '<i class="fa fa-check"></i>';
  });
}
</script>

<script src="assets/js/jquery-3.6.0.min.js"></script>
<script src="assets/js/feather.min.js"></script>
<script src="assets/js/jquery.slimscroll.min.js"></script>
<script src="assets/js/jquery.dataTables.min.js"></script>
<script src="assets/js/dataTables.bootstrap4.min.js"></script>
<script src="assets/js/bootstrap.bundle.min.js"></script>
<script src="assets/plugins/select2/js/select2.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalert2.all.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalerts.min.js"></script>
<script src="assets/js/script.js"></script>

</body>
</html>
