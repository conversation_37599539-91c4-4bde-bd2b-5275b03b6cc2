<?php 
require("database.php");
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (isset($_SESSION["user"])) {
    $email = $_SESSION["user"];
    $acc_id = $_SESSION["acc_id"];

    // Fetch user details
    $sql4 = "SELECT * FROM `user-list` WHERE `acc_id` = ?"; 
    $stmt = mysqli_prepare($conn, $sql4);
    mysqli_stmt_bind_param($stmt, "s", $acc_id);
    mysqli_stmt_execute($stmt);
    $result4 = mysqli_stmt_get_result($stmt);

    if ($row4 = mysqli_fetch_assoc($result4)) { 
        $fullname = $row4['name']; 
        $email2 = $row4['email']; 
        if (is_null($fullname) || trim($fullname) === "") {
            $fullname = $email2; 
        }
    } else {
        echo "<script>alert('No record found!'); window.location='signin.php';</script>";
        exit;
    }

    // Fetch position details for editing
    if (isset($_GET['position_id'])) {
        $position_id = $_GET['position_id'];

        $sql_position = "SELECT * FROM `position_list` WHERE `position_id` = ?";
        $stmt_position = mysqli_prepare($conn, $sql_position);
        mysqli_stmt_bind_param($stmt_position, "s", $position_id);
        mysqli_stmt_execute($stmt_position);
        $result_position = mysqli_stmt_get_result($stmt_position);

        if ($row_position = mysqli_fetch_assoc($result_position)) {
            $position_name = $row_position['position_name'];
            $description = $row_position['description'];
            $department_id = $row_position['department_id'];
        } else {
            echo "<script>alert('Position not found!'); window.location='position_list.php';</script>";
            exit;
        }
        mysqli_stmt_close($stmt_position);
    } else {
        echo "<script>alert('No position ID provided!'); window.location='position_list.php';</script>";
        exit;
    }

    // Fetch departments for dropdown
    $result_departments = mysqli_query($conn, "SELECT * FROM `department_list`");

    // Handle form submission for updating the position
    if (isset($_POST["submit"])) {
        $position_name = $_POST["position_name"];
        $description = $_POST["description"];
        $department_id = $_POST["department_id"];

        // Update position details
        $sql_update = "UPDATE `position_list` 
                       SET `position_name` = ?, `description` = ?, `department_id` = ? 
                       WHERE `position_id` = ?";
        $stmt_update = mysqli_prepare($conn, $sql_update);
        mysqli_stmt_bind_param($stmt_update, "ssss", $position_name, $description, $department_id, $position_id);

        if (mysqli_stmt_execute($stmt_update)) {
            echo "<script>alert('The position has been updated successfully.'); window.location='position_list.php';</script>";
        } else {
            echo "<script>alert('Error: " . mysqli_error($conn) . "'); window.location='edit_position.php?position_id=$position_id';</script>";
        }
        mysqli_stmt_close($stmt_update);
    }
?>

<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0">
<meta name="description" content="CLL Ticketing System">
<meta name="author" content="Powered by CLL Systems Sdn Bhd">
<meta name="robots" content="noindex, nofollow">
<title>CLLXWARE - Edit Position</title>

<link rel='icon' type='image/x-icon' href='https://www.cllsystems.com/wp-content/themes/cllsystems/img/favicon.png'>
<link rel="stylesheet" href="assets/css/bootstrap.min.css">
<link rel="stylesheet" href="assets/css/animate.css">
<link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">
<link rel="stylesheet" href="assets/css/dataTables.bootstrap4.min.css">
<link rel="stylesheet" href="assets/plugins/fontawesome/css/fontawesome.min.css">
<link rel="stylesheet" href="assets/plugins/fontawesome/css/all.min.css">
<link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
<div id="global-loader">
    <div class="whirly-loader"></div>
</div>

<div class="main-wrapper">
<?php 
$role_id = $_SESSION["role_id"];
$sql20 = "SELECT * FROM `role-list` WHERE `role-id` = ?";
$stmt20 = mysqli_prepare($conn, $sql20);
mysqli_stmt_bind_param($stmt20, "s", $role_id);
mysqli_stmt_execute($stmt20);
$result20 = mysqli_stmt_get_result($stmt20);

if ($row20 = mysqli_fetch_assoc($result20)) {
    $all = $row20['offboarding_all'];
    if ($all != '1') {
        header("location: ./index.php");
        exit;
    }
} else {
    echo "<script>alert('Role data not found')</script>";
    exit;
}
mysqli_stmt_close($stmt20);
include("header.php");
?>
</div>

<div class="page-wrapper">
    <div class="content">
        <div class="page-header">
            <div class="page-title">
                <h4>Edit Position</h4>
                <h6>Update Position Details</h6>
            </div>
        </div>

        <div class="card">
            <div class="card-body">
                <form action="#" method="post">
                    <div class="row">
                        <div class="col-lg-3 col-sm-6 col-12">
                            <div class="form-group">
                                <label>Department *</label>
                                <select name="department_id" class="form-control select" required>
                                    <option value="" disabled>Select Department</option>
                                    <?php 
                                    mysqli_data_seek($result_departments, 0);
                                    while ($row_dept = mysqli_fetch_assoc($result_departments)): 
                                        $selected = ($department_id == $row_dept['department_id']) ? 'selected' : '';
                                    ?>
                                        <option value="<?php echo htmlspecialchars($row_dept['department_id']); ?>" <?php echo $selected; ?>>
                                            <?php echo htmlspecialchars($row_dept['department_name']); ?>
                                        </option>
                                    <?php endwhile; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-lg-3 col-sm-6 col-12">
                            <div class="form-group">
                                <label>Position Name *</label>
                                <input type="text" name="position_name" required class="form-control" value="<?php echo htmlspecialchars($position_name); ?>">
                            </div>
                        </div>
                        <div class="col-lg-12">
                            <div class="form-group">
                                <label>Description</label>
                                <textarea class="form-control" name="description"><?php echo htmlspecialchars($description); ?></textarea>
                            </div>
                        </div>
                        <div class="col-lg-12">
                            <input type='submit' class='btn btn-submit me-2' name='submit' value='Update'>
                            <a href="position_list.php" class="btn btn-cancel">Cancel</a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
</div>

<script src="assets/js/jquery-3.6.0.min.js"></script>
<script src="assets/js/feather.min.js"></script>
<script src="assets/js/jquery.slimscroll.min.js"></script>
<script src="assets/js/jquery.dataTables.min.js"></script>
<script src="assets/js/dataTables.bootstrap4.min.js"></script>
<script src="assets/js/bootstrap.bundle.min.js"></script>
<script src="assets/plugins/select2/js/select2.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalert2.all.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalerts.min.js"></script>
<script src="assets/js/script.js"></script>
</body>
</html>
<?php
} else {
    header("location: ./signin.php");
    exit;
}
?>
