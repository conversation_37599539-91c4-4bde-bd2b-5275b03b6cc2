<?php
require("database.php");

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

require 'MAILER/vendor/autoload.php';

if (!isset($_SESSION["user"])) {
    header("location: ./signin.php");
    exit();
}

$role_id = $_SESSION["role_id"];
$acc_id = $_SESSION["acc_id"];

// Fetch role permissions
$sql_role = "SELECT * FROM `role-list` WHERE `role-id` = ?";
$stmt_role = mysqli_prepare($conn, $sql_role);
mysqli_stmt_bind_param($stmt_role, "s", $role_id);
mysqli_stmt_execute($stmt_role);
$result_role = mysqli_stmt_get_result($stmt_role);

if ($row_role = mysqli_fetch_assoc($result_role)) {
    $is_admin = $row_role['section_all'] == '1';
    $hr = $row_role['hr'] == '1';
    $finance = $row_role['finance'] == '1';
    $it_support = $row_role['it_support'] == '1';
    $anonymous = $row_role['anonymous'] == '1';

    if (!$is_admin && !$hr && !$finance && !$it_support && !$anonymous) {
        echo "<script>alert('Access denied.'); window.location='index.php';</script>";
        exit();
    }
} else {
    echo "<script>alert('Role data not found'); window.location='index.php';</script>";
    exit();
}

mysqli_stmt_close($stmt_role);

$inq_id = $_GET['inq_id'] ?? null;
if (!$inq_id) {
    echo "No inquiry ID provided!";
    exit();
}

$sql = "SELECT * FROM `sections` WHERE `inq_id` = ?";
$stmt = mysqli_prepare($conn, $sql);
mysqli_stmt_bind_param($stmt, "s", $inq_id);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

if ($row = mysqli_fetch_assoc($result)) {
    $section_name = $row['section_name'];
    $status = $row['status'];
    $priority_level = $row['priority_level'];
    $created_by = ($section_name == 'Anonymous Inquiry') ? 'Anonymous' : $row['acc_id']; // Store acc_id for later use
    $involved_hr = $row['involved_hr'];
    $involved_finance = $row['involved_finance'];
    $involved_it_support = $row['involved_it_support'];
    $involved_anonymous = $row['involved_anonymous'];
    $comments = $row['comments'];
    // Assuming existing_images is fetched from specific tables below.
    $existing_images = '';
    
    // Fetch existing images from the respective table based on section name
    switch ($section_name) {
        case 'Assets Faulty':
            $sql_images = "SELECT `image` FROM `assets_faulty_list` WHERE `inq_id` = ?";
            break;
        case 'Finance':
            $sql_images = "SELECT `image` FROM `finance_list` WHERE `inq_id` = ?";
            break;
        case 'Procurement':
            $sql_images = "SELECT `image` FROM `procurement_list` WHERE `inq_id` = ?";
            break;
        case 'IT Support':
            $sql_images = "SELECT `image` FROM `itsupport_list` WHERE `inq_id` = ?";
            break;
        case 'Anonymous Inquiry':
            $sql_images = "SELECT `image` FROM `anonymous_inquiry_list` WHERE `inq_id` = ?";
            break;
        case 'HR Inquiry': // Add this case for HR Inquiry
            $sql_images = "SELECT `image` FROM `hr_list` WHERE `inq_id` = ?";
            break;
        default:
            echo "<script>alert('Invalid section name.'); window.location='index.php';</script>";
            exit();
    }

    $stmt_images = mysqli_prepare($conn, $sql_images);
    mysqli_stmt_bind_param($stmt_images, "s", $inq_id);
    mysqli_stmt_execute($stmt_images);
    $result_images = mysqli_stmt_get_result($stmt_images);

    if ($row_images = mysqli_fetch_assoc($result_images)) {
        $existing_images = $row_images['image'];
    }
    mysqli_stmt_close($stmt_images);
    
    // Role-based access control
    if (!$is_admin) {
        $hasAccess = false;
        if ($hr && $involved_hr) {
            $hasAccess = true;
        }
        if ($finance && $involved_finance) {
            $hasAccess = true;
        }
        if ($it_support && $involved_it_support) {
            $hasAccess = true;
        }
        if ($anonymous && $involved_anonymous) {
            $hasAccess = true;
        }
        if (!$hasAccess) {
            echo "<script>alert('Access denied.'); window.location='index.php';</script>";
            exit();
        }
    }
} else {
    echo "<script>alert('No record found.'); window.location='index.php';</script>";
    exit();
}
mysqli_stmt_close($stmt);

// If the section is not 'Anonymous Inquiry', fetch the user's name and email
if ($section_name != 'Anonymous Inquiry') {
    $sql_user = "SELECT `name`, `email` FROM `user-list` WHERE `acc_id` = ?";
    $stmt_user = mysqli_prepare($conn, $sql_user);
    mysqli_stmt_bind_param($stmt_user, "s", $created_by);
    mysqli_stmt_execute($stmt_user);
    $result_user = mysqli_stmt_get_result($stmt_user);

    if ($row_user = mysqli_fetch_assoc($result_user)) {
        $created_by_name = $row_user['name'];
        $created_by_email = $row_user['email'];
    } else {
        $created_by_name = "Unknown User";
        $created_by_email = "";
    }
    mysqli_stmt_close($stmt_user);
} else {
    $created_by_name = "Anonymous";
    $created_by_email = "";
}

// Define fetchUserName function
function fetchUserName($acc_id) {
    global $conn;
    $sql_user = "SELECT `name` FROM `user-list` WHERE `acc_id` = ?";
    $stmt_user = mysqli_prepare($conn, $sql_user);
    mysqli_stmt_bind_param($stmt_user, "s", $acc_id);
    mysqli_stmt_execute($stmt_user);
    $result_user = mysqli_stmt_get_result($stmt_user);

    if ($row_user = mysqli_fetch_assoc($result_user)) {
        $name = $row_user['name'];
    } else {
        $name = "Unknown User";
    }
    mysqli_stmt_close($stmt_user);

    return $name;
}

// Fetch the current user's role
$current_user_role = fetchUserRole($conn, $acc_id);

switch ($section_name) {
    case 'Assets Faulty':
        $sql = "SELECT * FROM `assets_faulty_list` WHERE `inq_id` = ?";
        break;
    case 'Finance':
        $sql = "SELECT * FROM `finance_list` WHERE `inq_id` = ?";
        break;
    case 'Procurement':
        $sql = "SELECT * FROM `procurement_list` WHERE `inq_id` = ?";
        break;
    case 'IT Support':
        $sql = "SELECT * FROM `itsupport_list` WHERE `inq_id` = ?";
        break;
    case 'Anonymous Inquiry':
        $sql = "SELECT * FROM `anonymous_inquiry_list` WHERE `inq_id` = ?";
        break;
    case 'HR Inquiry': // Add this case for HR Inquiry
        $sql = "SELECT * FROM `hr_list` WHERE `inq_id` = ?";
        break;
    default:
        echo "<script>alert('Invalid section name.'); window.location='index.php';</script>";
        exit();
}

$stmt = mysqli_prepare($conn, $sql);
mysqli_stmt_bind_param($stmt, "s", $inq_id);
mysqli_stmt_execute($stmt);
$report_result = mysqli_stmt_get_result($stmt);

if ($report_row = mysqli_fetch_assoc($report_result)) {
    $report_details = $report_row;
} else {
    echo "<script>alert('No report details found.'); window.location='index.php';</script>";
    exit();
}
mysqli_stmt_close($stmt);

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $new_status = $_POST['status'] ?? $status;
    $new_priority_level = $_POST['priority_level'] ?? $priority_level;
    $comments = $_POST['comments'] ?? $comments;
    $involved_hr = isset($_POST['involved_hr']) ? 1 : 0;
    $involved_finance = isset($_POST['involved_finance']) ? 1 : 0;
    $involved_it_support = isset($_POST['involved_it_support']) ? 1 : 0;

    $sql = "UPDATE `sections` SET `status` = ?, `priority_level` = ?, `involved_hr` = ?, `involved_finance` = ?, `involved_it_support` = ?, `comments` = ? WHERE `inq_id` = ?";
    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, "ssiiiss", $new_status, $new_priority_level, $involved_hr, $involved_finance, $involved_it_support, $comments, $inq_id);

    if (mysqli_stmt_execute($stmt)) {
        $image = handleImageUpload($existing_images, $inq_id);

        switch ($section_name) {
            case 'Assets Faulty':
                $sql = "UPDATE `assets_faulty_list` SET `remarks` = ?, `image` = ?, `office_area_id` = ?, `office_category_id` = ?, `fault_category_id` = ? WHERE `inq_id` = ?";
                $stmt = mysqli_prepare($conn, $sql);
                mysqli_stmt_bind_param($stmt, "ssssss", $_POST['remarks'], $image, $_POST['office_area'], $_POST['office_category'], $_POST['fault_category'], $inq_id);
                break;
            case 'Finance':
                $sql = "UPDATE `finance_list` SET `finance_inq_id` = ?, `des` = ?, `remarks` = ?, `image` = ? WHERE `inq_id` = ?";
                $stmt = mysqli_prepare($conn, $sql);
                mysqli_stmt_bind_param($stmt, "sssss", $_POST['financeinq'], $_POST['des'], $_POST['remarks'], $image, $inq_id);
                break;
            case 'Procurement':
                $sql = "UPDATE `procurement_list` SET `inquiry` = ?, `remarks` = ?, `image` = ? WHERE `inq_id` = ?";
                $stmt = mysqli_prepare($conn, $sql);
                mysqli_stmt_bind_param($stmt, "ssss", $_POST['inquiry'], $_POST['remarks'], $image, $inq_id);
                break;
            case 'IT Support':
                $sql = "UPDATE `itsupport_list` SET `itsupport_inq_id` = ?, `itsupport_category_id` = ?, `brand_id` = ?, `des` = ?, `remarks` = ?, `image` = ? WHERE `inq_id` = ?";
                $stmt = mysqli_prepare($conn, $sql);
                mysqli_stmt_bind_param($stmt, "sssssss", $_POST['itsupportinquiry'], $_POST['itsupportcategory'], $_POST['brandcategory'], $_POST['des'], $_POST['remarks'], $image, $inq_id);
                break;
            case 'Anonymous Inquiry':
                $sql = "UPDATE `anonymous_inquiry_list` SET `inquiry` = ?, `remarks` = ?, `image` = ? WHERE `inq_id` = ?";
                $stmt = mysqli_prepare($conn, $sql);
                mysqli_stmt_bind_param($stmt, "ssss", $_POST['inquiry'], $_POST['remarks'], $image, $inq_id);
                break;
            case 'HR Inquiry': // Add this case for HR Inquiry
                $sql = "UPDATE `hr_list` SET `hr_inq_id` = ?, `des` = ?, `remarks` = ?, `image` = ? WHERE `inq_id` = ?";
                $stmt = mysqli_prepare($conn, $sql);
                mysqli_stmt_bind_param($stmt, "sssss", $_POST['hrinquiry'], $_POST['des'], $_POST['remarks'], $image, $inq_id);
                break;
            default:
                echo "<script>alert('Invalid section name.'); window.location='index.php';</script>";
                exit();
        }

        if (mysqli_stmt_execute($stmt)) {
            if ($new_status != $status) {
                sendStatusUpdateEmail($created_by_email, $created_by_name, $section_name, $new_status);
            }

            if ($involved_hr && $current_user_role !== 'hr') {
                sendEmailsToRole($conn, 'hr', $section_name, $current_user_role, $involved_hr);
            }
            if ($involved_finance && $current_user_role !== 'finance') {
                sendEmailsToRole($conn, 'finance', $section_name, $current_user_role, $involved_finance);
            }
            if ($involved_it_support && $current_user_role !== 'it_support') {
                sendEmailsToRole($conn, 'it_support', $section_name, $current_user_role, $involved_it_support);
            }

            echo "<script>alert('Section updated successfully.'); window.location='section_list_admin.php';</script>";
        } else {
            echo "<script>alert('Error updating section.');</script>";
        }
        mysqli_stmt_close($stmt);
    } else {
        echo "<script>alert('Error updating section status.');</script>";
    }
    mysqli_stmt_close($stmt);
}


function handleImageUpload($existing_images, $inq_id) {
    $image_paths = $existing_images;
    $maxTotalSize = 20 * 1024 * 1024; // 20 MB in bytes
    $totalSize = 0;

    if (isset($_FILES["image"]["name"]) && $_FILES["image"]["name"][0] !== "") {
        $target_dir = "uploads/";
        if (!file_exists($target_dir)) {
            mkdir($target_dir, 0777, true);
        }
        if (!is_writable($target_dir)) {
            echo "<script>alert('Error: Upload directory is not writable.');</script>";
            return $existing_images;
        }
        foreach ($_FILES["image"]["tmp_name"] as $key => $tmp_name) {
            $totalSize += $_FILES["image"]["size"][$key];
            if ($totalSize > $maxTotalSize) {
                echo "<script>alert('Error: Total size of selected files exceeds the 20MB limit.'); window.location='edit_section.php?inq_id=$inq_id';</script>";
                return $existing_images;
            }
        }

        $uploaded_images = [];
        foreach ($_FILES["image"]["tmp_name"] as $key => $tmp_name) {
            $file_extension = pathinfo($_FILES["image"]["name"][$key], PATHINFO_EXTENSION);
            $unique_file_name = uniqid() . '.' . $file_extension;
            $target_file = $target_dir . $unique_file_name;

            if (move_uploaded_file($tmp_name, $target_file)) {
                $uploaded_images[] = $target_file;
            } else {
                echo "<script>alert('Error: Failed to upload image.'); window.location='edit_section.php?inq_id=$inq_id';</script>";
                return $existing_images;
            }
        }
        $image_paths = empty($existing_images) ? implode(',', $uploaded_images) : $existing_images . ',' . implode(',', $uploaded_images);
    }
    return $image_paths;
}


function sendEmailsToRole($conn, $role_column, $section_name, $submitter_role, $involved_role) {
    if ($involved_role == 1) {
        $emails = fetchEmailsByRole($conn, $role_column, $submitter_role);
        foreach ($emails as $email) {
            $formatted_role = formatRoleName($role_column);
            sendEmailNotification($email, "$formatted_role Department", $section_name, $submitter_role, true, true);
        }
    }
}

function fetchEmailsByRole($conn, $role_column, $exclude_role) {
    $sql = "SELECT u.email 
            FROM useracc u
            JOIN `role-list` r ON u.role_id = r.`role-id`
            WHERE r.$role_column = 1 AND r.role <> ?";
    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, "s", $exclude_role);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);

    $emails = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $emails[] = $row['email'];
    }

    mysqli_stmt_close($stmt);
    return $emails;
}

function fetchUserRole($conn, $acc_id) {
    $sql = "SELECT r.role 
            FROM useracc u
            JOIN `role-list` r ON u.role_id = r.`role-id`
            WHERE u.acc_id = ?";
    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, "s", $acc_id);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);

    if ($row = mysqli_fetch_assoc($result)) {
        return $row['role'];
    } else {
        return null;
    }
}

function formatRoleName($role) {
    $role_map = [
        'hr' => 'HR',
        'finance' => 'Finance',
        'it_support' => 'IT Support'
    ];
    return $role_map[$role] ?? ucfirst(str_replace('_', ' ', $role));
}

function sendEmailNotification($email, $fullname, $section_name, $submitter_role = null, $isInvolved = false, $isUpdate = false) {
    $mail = new PHPMailer(true);
    try {
        $mail->isSMTP();
        $mail->Host       = 'smtp.office365.com';
        $mail->SMTPAuth   = true;
        $mail->Username   = '<EMAIL>';
        $mail->Password   = '&[i3F6}0hOw6';
        $mail->SMTPSecure = 'tls';
        $mail->Port       = 587;

        $mail->setFrom('<EMAIL>', 'CLLXWARE');
        $mail->addAddress($email, $fullname);

        $mail->isHTML(true);
        $mail->Subject = 'CLLXWARE - Form Submission Notification';
        $mail->Body = "
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    background-color: #f4f4f4;
                    margin: 0;
                    padding: 0;
                }
                .container {
                    max-width: 600px;
                    margin: 0 auto;
                    padding: 20px;
                    background-color: #ffffff;
                    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
                    border-radius: 10px;
                }
                .header {
                    text-align: center;
                    padding: 20px;
                    background-color: #004085;
                    color: white;
                    border-top-left-radius: 10px;
                    border-top-right-radius: 10px;
                }
                .header img {
                    max-width: 150px;
                    height: auto;
                }
                .content {
                    padding: 20px;
                    color: #333333;
                }
                .content strong {
                    color: #007bff;
                }
                .content p {
                    margin: 0 0 15px;
                    font-size: 14px;
                    line-height: 1.5;
                }
                .content a {
                    display: inline-block;
                    padding: 10px 20px;
                    color: #ffffff;
                    background-color: #004085;
                    text-decoration: none;
                    border-radius: 5px;
                    margin-top: 10px;
                    text-align: center;
                }
                .footer {
                    text-align: center;
                    padding: 10px;
                    background-color: #f4f4f4;
                    color: #888888;
                    font-size: 12px;
                    border-bottom-left-radius: 10px;
                    border-bottom-right-radius: 10px;
                }
                .footer a {
                    color: #888888;
                    text-decoration: none;
                }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <img src='https://www.cllsystems.com/wp-content/uploads/2016/09/cropped-cll-logo.png' alt='Company Logo'>
                </div>
                <div class='content'>
                    <h2>Form Submission Notification</h2>
                    <p>Dear $fullname,</p>";

        if ($submitter_role) {
            if ($isUpdate) {
                $mail->Body .= "
                    <p><strong>" . formatRoleName($submitter_role) . "</strong> has updated the <strong>$section_name</strong> ticket form.</p>
                    <p>You have been involved in this process. Please review the updated submission at your earliest convenience.</p>
                    <div class='button-container'>
                        <a href='https://cis.cllsystems.com:9443/staging/section_list_admin.php' class='button'>Visit Our Website</a>
                    </div>";
            } else {
                $mail->Body .= "
                    <p><strong>" . formatRoleName($submitter_role) . "</strong> has submitted the <strong>$section_name</strong> ticket form.</p>
                    <p>You have been involved in this process. Please review the submission at your earliest convenience.</p>
                    <div class='button-container'>
                        <a href='https://cis.cllsystems.com:9443/staging/section_list_admin.php' class='button'>Visit Our Website</a>
                    </div>";
            }
        } else {
            $mail->Body .= "
                    <p>Your submission for <strong>$section_name</strong> ticket has been successfully received.</p>
                    <p>Thank you for your input. We will process it as soon as possible.</p>
                    <div class='button-container'>
                        <a href='https://cis.cllsystems.com:9443/staging/section_list_user.php' class='button'>Visit Our Website</a>
                    </div>";
        }
        
        $mail->Body .= "
                </div>
                <div class='footer'>
                    <p>CLLXWARE | Powered by CLL Systems Sdn Bhd</p>
                    <p>For support, please contact <a href='mailto:<EMAIL>'><EMAIL></a></p>
                </div>
            </div>
        </body>
        </html>";
        
        $mail->send();
    } catch (Exception $e) {
        error_log("Message could not be sent. Mailer Error: {$mail->ErrorInfo}");
    }
}

function sendStatusUpdateEmail($email, $fullname, $section_name, $status) {
    $mail = new PHPMailer(true);
    try {
        $mail->isSMTP();
        $mail->Host       = 'smtp.office365.com';
        $mail->SMTPAuth   = true;
        $mail->Username   = '<EMAIL>';
        $mail->Password   = '&[i3F6}0hOw6';
        $mail->SMTPSecure = 'tls';
        $mail->Port       = 587;

        $mail->setFrom('<EMAIL>', 'CLLXWARE');
        $mail->addAddress($email, $fullname);

        $mail->isHTML(true);
        $mail->Subject = 'CLLXWARE - Status Update Notification';
        $mail->Body = "
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    background-color: #f4f4f4;
                    margin: 0;
                    padding: 0;
                }
                .container {
                    max-width: 600px;
                    margin: 0 auto;
                    padding: 20px;
                    background-color: #ffffff;
                    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
                    border-radius: 10px;
                }
                .header {
                    text-align: center;
                    padding: 20px;
                    background-color: #004085;
                    color: white;
                    border-top-left-radius: 10px;
                    border-top-right-radius: 10px;
                }
                .header img {
                    max-width: 150px;
                    height: auto;
                }
                .content {
                    padding: 20px;
                    color: #333333;
                }
                .content strong {
                    color: #007bff;
                }
                .content p {
                    margin: 0 0 15px;
                    font-size: 14px;
                    line-height: 1.5;
                }
                .content a {
                    display: inline-block;
                    padding: 10px 20px;
                    color: #ffffff;
                    background-color: #007bff;
                    text-decoration: none;
                    border-radius: 5px;
                    margin-top: 10px;
                    text-align: center;
                }
                .footer {
                    text-align: center;
                    padding: 10px;
                    background-color: #f4f4f4;
                    color: #888888;
                    font-size: 12px;
                    border-bottom-left-radius: 10px;
                    border-bottom-right-radius: 10px;
                }
                .footer a {
                    color: #007bff;
                    text-decoration: none;
                }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <img src='https://www.cllsystems.com/wp-content/uploads/2016/09/cropped-cll-logo.png' alt='Company Logo'>
                </div>
                <div class='content'>
                    <h2>Status Update Notification</h2>
                    <p>Dear $fullname,</p>
                    <p>The status of your submission for <strong>$section_name</strong> has been updated to <strong>$status</strong>.</p>
                    <p>Thank you for your attention.</p>
                    <div class='button-container'>
                        <a href='https://cis.cllsystems.com:9443/staging/section_list_user.php' class='button'>Visit Our Website</a>
                    </div>
                </div>
                <div class='footer'>
                    <p>CLLXWARE | Powered by CLL Systems Sdn Bhd</p>
                    <p>For support, please contact <a href='mailto:<EMAIL>'><EMAIL></a></p>
                </div>
            </div>
        </body>
        </html>";
        
        $mail->send();
    } catch (Exception $e) {
        error_log("Message could not be sent. Mailer Error: {$mail->ErrorInfo}");
    }
}

function fetchSelectOptions($table, $id_column, $name_column, $selected_value = null) {
    global $conn;
    $options = '';
    $sql = "SELECT * FROM `$table`";
    $result = mysqli_query($conn, $sql);
    while ($row = mysqli_fetch_assoc($result)) {
        $selected = ($row[$id_column] == $selected_value) ? 'selected' : '';
        $options .= "<option value='{$row[$id_column]}' $selected>{$row[$name_column]}</option>";
    }
    return $options;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0">
    <meta name="description" content="CLL Ticketing System">
    <meta name="author" content="Powered by CLL Systems Sdn Bhd">
    <meta name="robots" content="noindex, nofollow">
    <title>Edit Requests List</title>

    <link rel="shortcut icon" type="image/x-icon" href="https://www.cllsystems.com/wp-content/themes/cllsystems/img/favicon.png">
    <link rel="stylesheet" href="assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="assets/css/animate.css">
    <link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">
    <link rel="stylesheet" href="assets/css/dataTables.bootstrap4.min.css">
    <link rel="stylesheet" href="assets/plugins/fontawesome/css/fontawesome.min.css">
    <link rel="stylesheet" href="assets/plugins/fontawesome/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <script src="assets/js/jquery-3.6.0.min.js"></script>
</head>
<body>
    <div id="global-loader">
        <div class="whirly-loader"></div>
    </div>
    <div class="main-wrapper">
        <?php include("header.php"); ?>
        <div class="page-wrapper">
            <div class="content">
                <div class="page-header">
                    <div class="page-title">
                        <h4>Edit Requests List</h4>
                        <h6>Update your report details</h6>
                    </div>
                </div>
                <div class="card">
                    <div class="card-body">
                        <form action="" method="POST" enctype="multipart/form-data" onsubmit="return validateUpload()">
                            <div class="row">
                                <div class="col-lg-12">
                                    <div class="form-group">
                                        <label>Status</label>
                                        <select class="select" name="status" id="status" required>
                                            <option value="" disabled selected>Choose Status</option>
                                            <option value="New Ticket" <?php echo $status == 'New Ticket' ? 'selected' : ''; ?>>New Ticket</option>
                                            <option value="In Progress" <?php echo $status == 'In Progress' ? 'selected' : ''; ?>>In Progress</option>
                                            <option value="Rejected" <?php echo $status == 'Rejected' ? 'selected' : ''; ?>>Rejected</option>
                                            <option value="Completed" <?php echo $status == 'Completed' ? 'selected' : ''; ?>>Completed</option>
                                            <option value="Withdrawn" <?php echo $status == 'Withdrawn' ? 'selected' : ''; ?>>Withdrawn</option>
                                        </select>
                                    </div>
                                </div>
                                <?php if ($section_name != 'Anonymous Inquiry') { ?>
                                <div class="col-lg-12">
                                    <div class="form-group">
                                        <label>Priority Level</label>
                                        <select class="select" name="priority_level" required>
                                            <option value="" disabled selected>Choose Priority Level</option>
                                            <option value="Medium" <?php echo $priority_level == 'Medium' ? 'selected' : ''; ?>>Medium</option>
                                            <option value="High" <?php echo $priority_level == 'High' ? 'selected' : ''; ?>>High</option>
                                        </select>
                                    </div>
                                </div>
                                <?php } ?>
                                <div class="col-lg-12">
                                    <div class="form-group">
                                        <label>Section Name</label>
                                        <input type="text" class="form-control" value="<?php echo htmlspecialchars($section_name); ?>" readonly>
                                    </div>
                                </div>
                                <?php if ($section_name == 'Assets Faulty') { ?>
                                <div class="col-lg-12">
                                    <div class="form-group">
                                        <label for="office">Office</label>
                                        <select class="select" name="office" id="office" required>
                                            <?php echo fetchSelectOptions('office_list', 'office_id', 'office_name', $report_details['office_id'] ?? ''); ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-lg-4">
                                    <div class="form-group">
                                        <label for="office_area">Office Area</label>
                                        <select class="select" name="office_area" id="office_area" required>
                                            <?php echo fetchSelectOptions('office_area_list', 'office_area_id', 'office_area', $report_details['office_area_id'] ?? ''); ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-lg-3 col-sm-6 col-12">
                                    <div class="form-group">
                                        <label for="office_category">Category</label>
                                        <select class="select" name="office_category" id="office_category" required>
                                            <?php echo fetchSelectOptions('office_category_list', 'office_category_id', 'office_category', $report_details['office_category_id'] ?? ''); ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-lg-3 col-sm-6 col-12">
                                    <div class="form-group">
                                        <label for="fault_category">Fault Category</label>
                                        <select class="select" name="fault_category" id="fault_category" required>
                                            <?php echo fetchSelectOptions('fault_category_list', 'fault_category_id', 'fault_category', $report_details['fault_category_id'] ?? ''); ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-lg-12">
                                    <div class="form-group">
                                        <label>Remarks</label>
                                        <textarea class="form-control" name="remarks"><?php echo htmlspecialchars($report_details['remarks'] ?? ''); ?></textarea>
                                    </div>
                                </div>
                                <?php } elseif ($section_name == 'Finance') { ?>
                                <div class="col-lg-12">
                                    <div class="form-group">
                                        <label for="financeinq">Finance Category</label>
                                        <select class="select" name="financeinq" id="financeinq" required>
                                            <?php echo fetchSelectOptions('financeinq_list', 'finance_inq_id', 'finance_inq', $report_details['finance_inq_id'] ?? ''); ?>
                                        </select>
                                    </div>
                                </div>   
                                <div class="col-lg-12">
                                    <div class="form-group">
                                        <label>Description</label>
                                        <textarea class="form-control" name="des"><?php echo htmlspecialchars($report_details['des'] ?? ''); ?></textarea>
                                    </div>
                                </div>
                                <div class="col-lg-12">
                                    <div class="form-group">
                                        <label>Remarks</label>
                                        <textarea class="form-control" name="remarks"><?php echo htmlspecialchars($report_details['remarks'] ?? ''); ?></textarea>
                                    </div>
                                </div>
                                <?php } elseif ($section_name == 'Procurement') { ?>
                                <div class="col-lg-12">
                                    <div class="form-group">
                                        <label>Procurement Inquiry</label>
                                        <textarea class="form-control" name="inquiry"><?php echo htmlspecialchars($report_details['inquiry'] ?? ''); ?></textarea>
                                    </div>
                                </div>
                                <div class="col-lg-12">
                                    <div class="form-group">
                                        <label>Remarks</label>
                                        <textarea class="form-control" name="remarks"><?php echo htmlspecialchars($report_details['remarks'] ?? ''); ?></textarea>
                                    </div>
                                </div>
                                <?php } elseif ($section_name == 'IT Support') { ?>
                                <div class="col-lg-12">
                                    <div class="form-group">
                                    <label for="itsupportinquiry">IT Support Inquiry</label>
                                        <select class="select" name="itsupportinquiry" id="itsupportinquiry" required>
                                            <?php echo fetchSelectOptions('itsupportinq_list', 'itsupport_inq_id', 'itsupport_inq', $report_details['itsupport_inq_id'] ?? ''); ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-lg-3 col-sm-6 col-12">
                                    <div class="form-group">
                                        <label for="itsupportcategory">Category</label>
                                        <select class="select" name="itsupportcategory" id="itsupportcategory" required>
                                            <?php echo fetchSelectOptions('itsupport_category_list', 'itsupport_category_id', 'itsupport_category', $report_details['itsupport_category_id'] ?? ''); ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-lg-3 col-sm-6 col-12">
                                    <div class="form-group">
                                        <label for="brandcategory">Brand Category</label>
                                        <select class="select" name="brandcategory" id="brandcategory" required>
                                            <?php echo fetchSelectOptions('brand-list', 'brand-id', 'brand-name', $report_details['brand_id'] ?? ''); ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-lg-12">
                                    <div class="form-group">
                                        <label>Description</label>
                                        <textarea class="form-control" name="des"><?php echo htmlspecialchars($report_details['des'] ?? ''); ?></textarea>
                                    </div>
                                </div>
                                <div class="col-lg-12">
                                    <div class="form-group">
                                        <label>Remarks</label>
                                        <textarea class="form-control" name="remarks"><?php echo htmlspecialchars($report_details['remarks'] ?? ''); ?></textarea>
                                    </div>
                                </div>
                                <?php } elseif ($section_name == 'Anonymous Inquiry') { ?>
                                <div class="col-lg-12">
                                    <div class="form-group">
                                        <label>Anonymous Inquiry</label>
                                        <textarea class="form-control" name="inquiry"><?php echo htmlspecialchars($report_details['inquiry'] ?? ''); ?></textarea>
                                    </div>
                                </div>
                                <div class="col-lg-12">
                                    <div class="form-group">
                                        <label>Remarks</label>
                                        <textarea class="form-control" name="remarks"><?php echo htmlspecialchars($report_details['remarks'] ?? ''); ?></textarea>
                                    </div>
                                </div>
                                <?php } elseif ($section_name == 'HR Inquiry') { ?> <!-- Add this section for HR Inquiry -->
                                <div class="col-lg-12">
                                    <div class="form-group">
                                        <label for="hrinquiry">HR Inquiry</label>
                                        <select class="select" name="hrinquiry" id="hrinquiry" required>
                                            <?php echo fetchSelectOptions('hrinq_list', 'hr_inq_id', 'hr_inq', $report_details['hr_inq_id'] ?? ''); ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-lg-12">
                                    <div class="form-group">
                                        <label>Description</label>
                                        <textarea class="form-control" name="des"><?php echo htmlspecialchars($report_details['des'] ?? ''); ?></textarea>
                                    </div>
                                </div>
                                <div class="col-lg-12">
                                    <div class="form-group">
                                        <label>Remarks</label>
                                        <textarea class="form-control" name="remarks"><?php echo htmlspecialchars($report_details['remarks'] ?? ''); ?></textarea>
                                    </div>
                                </div>
                                <?php } ?>
                                <div class="col-lg-12">
                                    <div class="form-group">
                                        <label>Images</label>
                                        <div class="image-upload">
                                            <input type="file" name="image[]" id="image" multiple onchange="previewImages(event, 'uploadedImages', 'imageUploadsContainer')">
                                            <div id="imageUploadsContainer" class="image-uploads">
                                                <img src="assets/img/icons/upload.svg" alt="img">
                                                <h4>Drag and drop files to upload</h4>
                                            </div>
                                            <div id="uploadedImages" style="margin-top: 10px;">
                                                <?php
                                                if (!empty($existing_images)) {
                                                    $images = explode(',', $existing_images);
                                                    foreach ($images as $image) {
                                                        echo "<div style='display: inline-block; margin-right: 10px; text-align: center;'>
                                                                <a href='$image' download>$image</a>
                                                            </div>";
                                                    }
                                                }
                                                ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-12">
                                    <div class="form-group">
                                        <label>Created by</label>
                                        <input type="text" class="form-control" value="<?php echo htmlspecialchars($created_by_name); ?>" readonly>
                                    </div>
                                </div>
                                <div class="col-lg-12">
                                    <div class="form-group">
                                        <h6>Involved:</h6>
                                        <br>
                                        <div class="input-checkset">
                                            <ul>
                                                <li>
                                                    <label class="inputcheck">HR
                                                        <input type="checkbox" name="involved_hr" <?php echo $involved_hr ? 'checked' : ''; ?>>
                                                        <span class="checkmark"></span>
                                                    </label>
                                                </li>
                                                <li>
                                                    <label class="inputcheck">Finance
                                                        <input type="checkbox" name="involved_finance" <?php echo $involved_finance ? 'checked' : ''; ?>>
                                                        <span class="checkmark"></span>
                                                    </label>
                                                </li>
                                                <li>
                                                    <label class="inputcheck">IT Support
                                                        <input type="checkbox" name="involved_it_support" <?php echo $involved_it_support ? 'checked' : ''; ?>>
                                                        <span class="checkmark"></span>
                                                    </label>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-12">
                                    <div class="form-group">
                                        <label>Comments</label>
                                        <textarea class="form-control" name="comments"><?php echo htmlspecialchars($comments); ?></textarea>
                                    </div>
                                </div>
                                <div class="col-lg-12">
                                    <button type="submit" class="btn btn-submit me-2">Update</button>
                                    <a href="section_list_admin.php" class="btn btn-cancel">Cancel</a>
                                </div>
                            </div>
                            <input type="hidden" name="inq_id" value="<?php echo htmlspecialchars($inq_id); ?>">
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function validateUpload() {
    const maxSize = 20 * 1024 * 1024; // 20MB in bytes
    const files = document.getElementById('image').files;
    let totalSize = 0;

    for (let i = 0; i < files.length; i++) {
        totalSize += files[i].size;
    }

    console.log("Total size: " + totalSize + " bytes");

    if (totalSize > maxSize) {
        alert('The total upload size should not exceed 20MB.');
        const inq_id = "<?php echo $inq_id; ?>"; // Fetch inq_id from PHP
        window.location = `edit_section.php?inq_id=${inq_id}`;
        return false; // Prevent form submission
    }

    return true; // Allow form submission
}

function previewImages(event, uploadedImagesContainerId, imageUploadsContainerId) {
    var files = event.target.files;
    var uploadedImagesContainer = document.getElementById(uploadedImagesContainerId);
    var imageUploadsContainer = document.getElementById(imageUploadsContainerId);
    uploadedImagesContainer.innerHTML = ''; // Clear any existing images

    var maxTotalSize = 20 * 1024 * 1024; // 20 MB in bytes
    var totalSize = 0;

    if (files.length > 0) {
        imageUploadsContainer.style.display = 'none';
    } else {
        imageUploadsContainer.style.display = 'block';
    }

    for (var i = 0; i < files.length; i++) {
        totalSize += files[i].size;
        if (totalSize > maxTotalSize) {
            alert('Total size of selected files exceeds the 20MB limit.');
            event.target.value = ''; // Clear the input
            uploadedImagesContainer.innerHTML = ''; // Clear the preview container
            imageUploadsContainer.style.display = 'block';
            return;
        }
        var reader = new FileReader();
        reader.onload = (function(f) {
            return function(e) {
                var linkElement = document.createElement('a');
                linkElement.href = e.target.result;
                linkElement.download = f.name;
                linkElement.textContent = f.name;
                linkElement.style.display = 'block';
                uploadedImagesContainer.appendChild(linkElement);
            };
        })(files[i]);
        reader.readAsDataURL(files[i]);
    }
}

$(document).ready(function() {
    $('#office').change(function() {
        var officeId = $(this).val();
        fetchOptions('fetch_office_area.php', { office_id: officeId }, 'office_area', 'Choose office area');
        fetchOptions('fetch_fault_category.php', { office_id: officeId }, 'fault_category', 'Choose Fault Category');
        $('#office_category').html('<option value="" disabled selected>Choose category</option>');
    });

    $('#office_area').change(function() {
        var officeAreaId = $(this).val();
        fetchOptions('fetch_office_category.php', { office_area_id: officeAreaId }, 'office_category', 'Choose category');
    });

    var selectedOfficeId = "<?php echo $report_details['office_id'] ?? ''; ?>";
    if (selectedOfficeId) {
        fetchOptions('fetch_office_area.php', { office_id: selectedOfficeId }, 'office_area', 'Choose office area', "<?php echo $report_details['office_area_id'] ?? ''; ?>");
        fetchOptions('fetch_office_category.php', { office_area_id: "<?php echo $report_details['office_area_id'] ?? ''; ?>" }, 'office_category', 'Choose category', "<?php echo $report_details['office_category_id'] ?? ''; ?>");
        fetchOptions('fetch_fault_category.php', { office_id: selectedOfficeId }, 'fault_category', 'Choose Fault Category', "<?php echo $report_details['fault_category_id'] ?? ''; ?>");
    }

    function fetchOptions(endpoint, payload, targetElementId, defaultOption, selectedValue = null) {
        $('#' + targetElementId).html('<option value="" disabled selected>' + defaultOption + '</option>');
        $.post(endpoint, payload, function(data) {
            $('#' + targetElementId).html(data);
            if (selectedValue) {
                $('#' + targetElementId).val(selectedValue);
            }
        }).fail(function() {
            $('#' + targetElementId).html('<option value="" disabled selected>' + defaultOption + '</option>');
        });
    }      
});
</script>

<script>
$(document).ready(function() {
    $('#itsupportinquiry').change(function() {
        var itsupportInquiryId = $(this).val();
        fetchOptions2('fetch_itsupport_category.php', { itsupport_inq_id: itsupportInquiryId }, 'itsupportcategory', 'Choose Category');
    });

    var selectedITSupportInquiryId = "<?php echo $report_details['itsupport_inq_id'] ?? ''; ?>";
    if (selectedITSupportInquiryId) {
        fetchOptions2('fetch_itsupport_category.php', { itsupport_inq_id: selectedITSupportInquiryId }, 'itsupportcategory', 'Choose Category', "<?php echo $report_details['itsupport_category_id'] ?? ''; ?>");
    }

    function fetchOptions2(endpoint, payload, targetElementId, defaultOption, selectedValue = null) {
        $('#' + targetElementId).html('<option value="" disabled selected>' + defaultOption + '</option>');
        $.post(endpoint, payload, function(data) {
            $('#' + targetElementId).html(data);
            if (selectedValue) {
                $('#' + targetElementId).val(selectedValue);
            }
        }).fail(function() {
            console.error("Error fetching data from:", endpoint);
            $('#' + targetElementId).html('<option value="" disabled selected>' + defaultOption + '</option>');
        });
    }
});

document.addEventListener('DOMContentLoaded', function() {
    const statusDropdown = document.getElementById('status');

    function updateStatusOptions(selectedValue) {
        const options = {
            "New Ticket": ["New Ticket", "In Progress", "Rejected"],
            "In Progress": ["In Progress", "Rejected", "Completed"],
            "Rejected": ["Rejected"],
            "Completed": ["Completed"],
            "Withdrawn": ["Withdrawn"],
        };

        // Clear current options except the first one
        while (statusDropdown.options.length > 1) {
            statusDropdown.remove(1);
        }

        // Add allowed options based on selected value
        if (options[selectedValue]) {
            options[selectedValue].forEach(status => {
                const option = document.createElement('option');
                option.value = status;
                option.textContent = status;
                statusDropdown.appendChild(option);
            });
        }
    }

    // Listen for changes
    statusDropdown.addEventListener('change', function() {
        const selectedValue = this.value;
        updateStatusOptions(selectedValue);
    });

    // Trigger the change event to apply the allowed statuses on page load
    const initialSelectedValue = statusDropdown.value;
    updateStatusOptions(initialSelectedValue);
});

</script>

<script src="assets/js/feather.min.js"></script>
<script src="assets/js/jquery.slimscroll.min.js"></script>
<script src="assets/js/jquery.dataTables.min.js"></script>
<script src="assets/js/dataTables.bootstrap4.min.js"></script>
<script src="assets/js/bootstrap.bundle.min.js"></script>
<script src="assets/plugins/select2/js/select2.min.js"></script>
<script src="assets/js/moment.min.js"></script>
<script src="assets/js/bootstrap-datetimepicker.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalert2.all.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalerts.min.js"></script>
<script src="assets/js/script.js"></script>
</body>
</html>
