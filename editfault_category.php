<?php
require("database.php");

if (isset($_SESSION["user"])) {
    $email = $_SESSION["user"];

    if (isset($_POST["submit"])) {
        $office = $_POST["office"];
        $fault_category = $_POST["fault_category"];
        $des = $_POST["des"];
        $fault_category_id = $_POST['fault_category_id'];

        $sql = "UPDATE `fault_category_list` SET `office_id` = ?, `fault_category` = ?, `des` = ? WHERE `fault_category_id` = ?";
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, "ssss", $office, $fault_category, $des, $fault_category_id);

        if (mysqli_stmt_execute($stmt)) {
            echo "<script>alert('The fault category $fault_category has been modified.'); window.location='fault_category_list.php';</script>";
        } else {
            echo "<script>alert('Error: " . mysqli_error($conn) . "'); window.location='fault_category_list.php';</script>";
        }
        mysqli_stmt_close($stmt);
    }

    if (isset($_GET['fault_category_id'])) {
        $fault_category_id = $_GET['fault_category_id'];
        $sql4 = "SELECT * FROM `fault_category_list` WHERE `fault_category_id` = ?";
        $stmt4 = mysqli_prepare($conn, $sql4);
        mysqli_stmt_bind_param($stmt4, "s", $fault_category_id);
        mysqli_stmt_execute($stmt4);
        $result4 = mysqli_stmt_get_result($stmt4);

        if ($row4 = mysqli_fetch_assoc($result4)) {
            $current_office = $row4['office_id'];
            $current_fault_category = $row4['fault_category'];
            $current_des = $row4['des'];
        } else {
            echo "<script>alert('No fault category found with the provided ID.'); window.location='fault_category_list.php';</script>";
            exit;
        }
        mysqli_stmt_close($stmt4);
    } else {
        echo "<script>alert('Fault Category ID is not provided in the URL.'); window.location='fault_category_list.php';</script>";
        exit;
    }
?>

<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0">
<meta name="description" content="CLL Ticketing System">
<meta name="author" content="Powered by CLL Systems Sdn Bhd">
<meta name="robots" content="noindex, nofollow">
<title>CLLXWARE-Edit Fault Category</title>

<link rel='icon' type='image/x-icon' href='https://www.cllsystems.com/wp-content/themes/cllsystems/img/favicon.png'>
<link rel="stylesheet" href="assets/css/bootstrap.min.css">
<link rel="stylesheet" href="assets/css/animate.css">
<link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">
<link rel="stylesheet" href="assets/css/dataTables.bootstrap4.min.css">
<link rel="stylesheet" href="assets/plugins/fontawesome/css/fontawesome.min.css">
<link rel="stylesheet" href="assets/plugins/fontawesome/css/all.min.css">
<link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
<div id="global-loader">
<div class="whirly-loader"> </div>
</div>

<div class="main-wrapper">

<?php 
$role_id = $_SESSION["role_id"];
$sql20 = "SELECT * FROM `role-list` WHERE `role-id` = ?";
$stmt20 = mysqli_prepare($conn, $sql20);
mysqli_stmt_bind_param($stmt20, "s", $role_id);
mysqli_stmt_execute($stmt20);
$result20 = mysqli_stmt_get_result($stmt20);

if ($row20 = mysqli_fetch_assoc($result20)) {
    $all = $row20['ticket_all'];
    if ($all != '1') {
        header("location: ./index.php");
        exit;
    }
} else {
    echo "<script>alert('Role data not found')</script>";
    exit;
}

mysqli_stmt_close($stmt20);
include("header.php");
?>
</div>

<div class="page-wrapper">
    <div class="content">
        <div class="page-header">
            <div class="page-title">
                <h4>Edit Fault Category</h4>
                <h6>Update Your Fault Category</h6>
            </div>
        </div>
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <form action="#" method="post">
                        <input type="hidden" name="fault_category_id" value="<?php echo htmlspecialchars($fault_category_id); ?>">
                        <div class="col-lg-3 col-sm-6 col-12">
                            <div class="form-group">
                                <?php
                                $sql = "SELECT * FROM `office_list`";
                                $result = mysqli_query($conn, $sql);
                                ?>
                                <label>Office</label>
                                <select class="select" name="office" required>
                                    <option value='' disabled>Choose Office</option>
                                    <?php
                                    while ($row = mysqli_fetch_assoc($result)) {
                                        $selected = ($row['office_id'] == $current_office) ? "selected" : "";
                                        echo "<option value='" . htmlspecialchars($row['office_id']) . "' $selected>" . htmlspecialchars($row['office_name']) . "</option>";
                                    }
                                    ?>
                                </select>
                            </div>
                        </div>

                        <div class="col-lg-3 col-sm-6 col-12">
                            <div class="form-group">
                                <label>Fault Category</label>
                                <input type="text" name="fault_category" value="<?php echo htmlspecialchars($current_fault_category); ?>" required class="form-control">                            </div>
                        </div>

                        <div class="col-lg-12">
                            <div class="form-group">
                                <label>Description</label>
                                <textarea class="form-control" name="des"><?php echo htmlspecialchars($current_des); ?></textarea>                            </div>
                        </div>

                        <div class="col-lg-12">
                            <input type="submit" class="btn btn-submit me-2" name="submit" value="Submit">
                            <a href="fault_category_list.php" class="btn btn-cancel">Cancel</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="assets/js/jquery-3.6.0.min.js"></script>
<script src="assets/js/feather.min.js"></script>
<script src="assets/js/jquery.slimscroll.min.js"></script>
<script src="assets/js/jquery.dataTables.min.js"></script>
<script src="assets/js/dataTables.bootstrap4.min.js"></script>
<script src="assets/js/bootstrap.bundle.min.js"></script>
<script src="assets/plugins/select2/js/select2.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalert2.all.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalerts.min.js"></script>
<script src="assets/js/script.js"></script>
</body>
</html>
<?php
} else {
    header("location: ./signin.php");
    exit;
}
?>
