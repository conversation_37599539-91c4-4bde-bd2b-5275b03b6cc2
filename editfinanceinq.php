<?php
require("database.php");
if(isset($_SESSION["user"])){
    $email=$_SESSION["user"];
if (isset($_POST["submit"])) {
    $finance_inq = $_POST["finance_inq"];
    $des = $_POST["des"];
    $finance_inq_id = $_GET['finance_inq_id'];
   
    $sql = "UPDATE `financeinq_list` SET `finance_inq` = ?, `des` = ? WHERE `finance_inq_id` = ?";
    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, "sss", $finance_inq, $des, $finance_inq_id);

    if (mysqli_stmt_execute($stmt)) {
        echo "<script>alert('The finance inquiry $finance_inq has been modified.'); window.location='financeinq_list.php';</script>";
    } else {
        echo "<script>alert('Error: " . mysqli_error($conn) . "'); window.location='financeinq_list.php';</script>";
    }
    mysqli_stmt_close($stmt);
    }
?>

<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0">
<meta name="description" content="CLL Ticketing System">
<meta name="author" content="Powered by CLL Systems Sdn Bhd">
<meta name="robots" content="noindex, nofollow">
<title>CLLXWARE-Edit Finance - Inquiry Category</title>

<link rel='icon' type='image/x-icon' href='https://www.cllsystems.com/wp-content/themes/cllsystems/img/favicon.png'>
<link rel="stylesheet" href="assets/css/bootstrap.min.css">
<link rel="stylesheet" href="assets/css/animate.css">
<link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">
<link rel="stylesheet" href="assets/css/dataTables.bootstrap4.min.css">
<link rel="stylesheet" href="assets/plugins/fontawesome/css/fontawesome.min.css">
<link rel="stylesheet" href="assets/plugins/fontawesome/css/all.min.css">
<link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
<div id="global-loader">
<div class="whirly-loader"> </div>
</div>

<div class="main-wrapper">

<?php 
$role_id = $_SESSION["role_id"];
$sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
$result20 = mysqli_query($conn, $sql20);

if ($result20) {
    $row20 = mysqli_fetch_assoc($result20);
    if ($row20) {
        $all = $row20['ticket_all'];
        if( $all !='1'){
        header("location: ./index.php");
       }
    } else {
        echo "<script>alert('Role data not found')</script>";
    }
} else {
    echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
}
include("header.php");?>
</div>

<div class="page-wrapper">
<div class="content">
<div class="page-header">
<div class="page-title">
<h4>Edit Finance - Inquiry Category</h4>
<h6>Update Your Finance - Inquiry Category</h6>
</div>
</div>
<form action="#" method="post">
<div class="card">
<div class="card-body">
<div class="row">
<?php
if(isset($_GET['finance_inq_id'])) {
    $finance_inq_id = $_GET['finance_inq_id'];
    $sql4 = "SELECT * FROM `financeinq_list` WHERE `finance_inq_id` = '$finance_inq_id'";
    $result4 = mysqli_query($conn, $sql4);
    if ($result4 && mysqli_num_rows($result4) > 0) {
        $row4 = mysqli_fetch_assoc($result4);
echo"
<div class='col-lg-3 col-sm-6 col-12'>
<div class='form-group'>
<label>Finance Inquiry</label>
<input type='text' name='finance_inq'  value='{$row4['finance_inq']}' required class='form-control'>
</div>
</div>
<div class='col-lg-12'>
<div class='form-group'>
<label>Description</label>
<textarea class='form-control' name='des'>{$row4['des']}</textarea>
</div>
</div>

<div class='col-lg-12'>
<input type='submit'  class='btn btn-submit me-2' name='submit' value='Submit'>
<a href='financeinq_list.php' class='btn btn-cancel'>Cancel</a>
</div>"
;

} else {
    // Handle case where no inventory item with the provided inventory-id is found
    echo "<p>No finance inquiry found with the provided ID.</p>";
  }

} else {
    // Handle case where inventory-id is not provided in the URL
    echo "<p>Finance Inquiry ID is not provided in the URL.</p>";
    }
?>

</div>
</div>
</div>
</form>
</div>
</div>
</div>


<script src="assets/js/jquery-3.6.0.min.js"></script>

<script src="assets/js/feather.min.js"></script>

<script src="assets/js/jquery.slimscroll.min.js"></script>

<script src="assets/js/jquery.dataTables.min.js"></script>
<script src="assets/js/dataTables.bootstrap4.min.js"></script>

<script src="assets/js/bootstrap.bundle.min.js"></script>

<script src="assets/plugins/select2/js/select2.min.js"></script>

<script src="assets/plugins/sweetalert/sweetalert2.all.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalerts.min.js"></script>

<script src="assets/js/script.js"></script>
</body>
</html>
<?php
}else{
    header("location: ./signin.php");
}
?>