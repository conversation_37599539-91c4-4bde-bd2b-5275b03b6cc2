<?php 
require("database.php");
if(isset($_SESSION["user"])){
  $email=$_SESSION["user"];
if (isset($_POST["submit"])) {
  $name= $_POST["name"];
  $brand= $_POST["brand"];
  $category = $_POST["category"];
  $location = $_POST["location"];
  $part_number = $_POST["part_number"];
  $serial_number = $_POST["serial_number"];
  $qty = $_POST["qty"];
  $ownership = $_POST["ownership"];
  $des = $_POST["des"];
  $status = $_POST["status"];
  $remark = $_POST["remark"];
  $inventory_id = $_GET['inventory-id'];
  $sql = "UPDATE `inventory-list` 
  SET `product-name` = ?, 
      `brand-id` = ?, 
      `category-id` = ?, 
      `location-id` = ?, 
      `part-number` = ?, 
      `serial-number` = ?, 
      `quantity` = ?, 
      `ownership` = ?, 
      `des` = ?, 
      `status` = ?, 
      `remark` = ?
  WHERE `inventory-id` = ?";
$stmt = mysqli_prepare($conn, $sql);
mysqli_stmt_bind_param($stmt, "ssssssississ", $name, $brand, $category, $location, $part_number, $serial_number, $qty, $ownership, $des, $status, $remark, $inventory_id);

  
  if (mysqli_stmt_execute($stmt)) {
      echo "<script>alert('The inventory $name has been modified.'); window.location='inventory-details.php?inventory-id=$inventory_id';</script>";
  } else {
      echo "<script>alert('Error: " . mysqli_error($conn) . "'); window.location='inventorylist.php';</script>";
  }
  mysqli_stmt_close($stmt);
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0">
<meta name="description" content="CLL Warehouse Inventory System">
<meta name="author" content="Powered by CLL Systems Sdn Bhd">
<meta name="robots" content="noindex, nofollow">
<title>CLLXWARE-Edit Inventory</title>

<link rel='icon' type='image/x-icon' href='https://www.cllsystems.com/wp-content/themes/cllsystems/img/favicon.png'>

<link rel="stylesheet" href="assets/css/bootstrap.min.css">

<link rel="stylesheet" href="assets/css/animate.css">

<link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">

<link rel="stylesheet" href="assets/css/dataTables.bootstrap4.min.css">

<link rel="stylesheet" href="assets/plugins/fontawesome/css/fontawesome.min.css">
<link rel="stylesheet" href="assets/plugins/fontawesome/css/all.min.css">

<link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
<div id="global-loader">
<div class="whirly-loader"> </div>
</div>
<div class="main-wrapper">

<?php 
$role_id = $_SESSION["role_id"];
$sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
$result20 = mysqli_query($conn, $sql20);

if ($result20) {
    $row20 = mysqli_fetch_assoc($result20);
    if ($row20) {
       $edit = $row20['inventory_edit'];
       $all = $row20['inventory_all'];
       if($edit != '1' and $all !='1'){
        header("location: ./index.php");
       }
    } else {
        echo "<script>alert('Role data not found')</script>";
    }
} else {
    echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
}
include("header.php");?>
</div>

<div class="page-wrapper">
<div class="content">
<div class="page-header">
<div class="page-title">
<h4>Inventory Edit</h4>
<h6>Modify inventory data</h6>
</div>
</div>
<form action='#' method='post'>
<div class="card">
<div class="card-body">
<div class="row">
<?php
$sql = "SELECT * FROM `brand-list` WHERE `hide` !='1'";
$result = mysqli_query($conn, $sql);

$sql2 = "SELECT * FROM `category-list` WHERE `hide` !='1'";
$result2 = mysqli_query($conn, $sql2);

$sql3 = "SELECT * FROM `location-list`";
$result3 = mysqli_query($conn, $sql3);

if(isset($_GET['inventory-id'])) {
  $inventory_id = $_GET['inventory-id'];
  $sql4 = "SELECT * FROM `inventory-list` WHERE `inventory-id` = '$inventory_id'";
  $result4 = mysqli_query($conn, $sql4);

  if ($result4 && mysqli_num_rows($result4) > 0) {
      $row4 = mysqli_fetch_assoc($result4);
      $brand_name = "Unknown Brand"; 
      if (isset($row4['brand-id'])) {
          $brand_id = $row4['brand-id'];
          $sql5 = "SELECT `brand-name` FROM `brand-list` WHERE `brand-id` = '$brand_id'";
          $result5 = mysqli_query($conn, $sql5);
          if ($result5 && mysqli_num_rows($result5) > 0) {
              $row5 = mysqli_fetch_assoc($result5);
              $brand_name = $row5['brand-name'];
          }
      }

      $category_name = "Unknown Category";
      if (isset($row4['category-id'])) {
          $category_id = $row4['category-id'];
          $sql6 = "SELECT `category-name` FROM `category-list` WHERE `category-id` = '$category_id'";
          $result6 = mysqli_query($conn, $sql6);
          if ($result6 && mysqli_num_rows($result6) > 0) {
              $row6 = mysqli_fetch_assoc($result6);
              $category_name = $row6['category-name'];
          }
      }
echo"

<div class='col-lg-3 col-sm-6 col-12'>
<div class='form-group'>
<label>Product Model</label>
<input type='text' name='name' value='{$row4['product-name']}' required>
</div>
</div>";?>
<div class='col-lg-3 col-sm-6 col-12'>
    <div class='form-group'>
        <label>Brand</label>
        <select class='select' name='brand' required>
            <option value='' disabled selected>Choose Brand</option>
            <?php
         while ($row = mysqli_fetch_assoc($result)) {
          echo "<option ";
          if ($row4['brand-id'] == $row['brand-id']) {
              echo "selected ";
          }
          echo "value='" . $row['brand-id'] . "'>" . $row['brand-name'] . "</option>";
      }
      
            ?>
        </select>
    </div>
</div>

<div class='col-lg-3 col-sm-6 col-12'>
<div class='form-group'>
<label>Category</label>
<select class='select' name='category' required>
<option value='' disabled selected>Choose Category</option>
<?php
       while ($row2 = mysqli_fetch_assoc($result2)) {
        echo "<option ";
        if ($row4['category-id'] == $row2['category-id']) {
            echo "selected ";
        }
        echo "value='" . $row2['category-id'] . "'>" . $row2['category-name'] . "</option>";
    }
    
            ?>
</select>
</div>
</div>
<div class='col-lg-3 col-sm-6 col-12'>
<div class='form-group'>
<label>Location</label>
<select class='select' name='location' required>
<option value='' disabled selected>Choose Location</option>
<?php
           while ($row3 = mysqli_fetch_assoc($result3)) {
            $room = isset($row3['room']) ? $row3['room'] : '';
            $rack = isset($row3['rack']) ? $row3['rack'] : '';
            $location = $room . " | " . $rack;
            echo "<option value='" . $location . "'";
            // Check if the location matches the value from the inventory list
            if ($location == $row4['location-id']) {
                echo " selected";
            }
            echo ">Room: " . $room . " | Rack: " . $rack . "</option>";
        }
            ?>
</select>
</div>
</div><?php 
echo"
<div class='col-lg-3 col-sm-6 col-12'>
<div class='form-group'>
<label>Product Part Number</label>
<input type='text' name='part_number' value='{$row4['part-number']}'>
</div>
</div>
<div class='col-lg-3 col-sm-6 col-12'>
<div class='form-group'>
<label>Product Serial Number</label>
<input type='text' name='serial_number' value='{$row4['serial-number']}'>
</div>
</div>
<div class='col-lg-3 col-sm-6 col-12'>
<div class='form-group'>
<label>Quantity</label>
<input type='text' pattern='[0-9]*' name='qty' value='{$row4['quantity']}' required>
</div>
</div>
<div class='col-lg-3 col-sm-6 col-12'>
<div class='form-group'>
<label>Ownership</label>
<input type='text' name='ownership' value='{$row4['ownership']}'>
</div>
</div>
<div class='col-lg-12'>
<div class='form-group'>
<label>Description</label>
<textarea class='form-control' name='des' >{$row4['des']}</textarea>
</div>
</div>
<div class='col-lg-3 col-sm-6 col-12'>
  <div class='form-group'>
    <label>Status</label>
    <select class='select' id='statusSelect' name='status' required>
      <option value='' disabled selected>Choose Status</option>";
      $current_status=$row4['status'];
      if($current_status=='1'){
        echo"
      <option selected value='1'>Available</option>
        ";
      }else{
      echo"
      <option value='1'>Available</option>";}
      echo"
    </select>
  </div>
</div>

<div class='col-lg-3 col-sm-6 col-12'>
  <div class='form-group' id='remarkColumn' >
    <label>Remark</label>
    <input type='text' name='remark' value='{$row4['remark']}'>
  </div>
</div>

<div class='col-lg-12'>
<input type='submit' class='btn btn-submit me-2' name='submit' value='Submit'>
<a href='inventorylist.php' class='btn btn-cancel'>Cancel</a>
</div>

";
} else {
  // Handle case where no inventory item with the provided inventory-id is found
  echo "<p>No inventory item found with the provided ID.</p>";
}
} else {
// Handle case where inventory-id is not provided in the URL
echo "<p>Inventory ID is not provided in the URL.</p>";
}
?>
</div>
</div>
</div>
</form>
</div>
</div>
</div>

<script src="assets/js/jquery-3.6.0.min.js"></script>

<script src="assets/js/feather.min.js"></script>

<script src="assets/js/jquery.slimscroll.min.js"></script>

<script src="assets/js/jquery.dataTables.min.js"></script>
<script src="assets/js/dataTables.bootstrap4.min.js"></script>

<script src="assets/js/bootstrap.bundle.min.js"></script>

<script src="assets/plugins/select2/js/select2.min.js"></script>

<script src="assets/plugins/sweetalert/sweetalert2.all.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalerts.min.js"></script>

<script src="assets/js/script.js"></script>
</body>
</html>
<?php
}else{
    header("location: ./signin.php");
}
?>