<?php 
require("database.php");

if(isset($_SESSION["user"])) {
    $email = $_SESSION["user"];
    if (isset($_POST["submit"])) {
        $itsupport_inq_id = $_POST["itsupport_inq"];
        $itsupport_category = $_POST["itsupport_category"];
        $des = $_POST["des"];
        $itsupport_category_id = $_GET['itsupport_category_id'];

        $sql = "UPDATE `itsupport_category_list` SET `itsupport_inq_id` = ?, `itsupport_category` = ?, `des` = ? WHERE `itsupport_category_id` = ?";
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, "ssss", $itsupport_inq_id, $itsupport_category, $des, $itsupport_category_id);

        if (mysqli_stmt_execute($stmt)) {
            echo "<script>alert('The IT support category $itsupport_category has been modified.'); window.location='itsupport_category_list.php';</script>";
        } else {
            echo "<script>alert('Error: " . mysqli_error($conn) . "'); window.location='itsupport_category_list.php';</script>";
        }
        mysqli_stmt_close($stmt);
    }

    if (isset($_GET['itsupport_category_id'])) {
        $itsupport_category_id = $_GET['itsupport_category_id'];
        $sql4 = "SELECT * FROM `itsupport_category_list` WHERE `itsupport_category_id` = ?";
        $stmt4 = mysqli_prepare($conn, $sql4);
        mysqli_stmt_bind_param($stmt4, "s", $itsupport_category_id);
        mysqli_stmt_execute($stmt4);
        $result4 = mysqli_stmt_get_result($stmt4);
        
        if ($row4 = mysqli_fetch_assoc($result4)) {
            $current_itsupport_inq_id = $row4['itsupport_inq_id'];
            $current_itsupport_category = $row4['itsupport_category'];
            $current_des = $row4['des'];
        } else {
            echo "<script>alert('No IT support category found with the provided ID.'); window.location='itsupport_category_list.php';</script>";
            exit;
        }
        mysqli_stmt_close($stmt4);
    } else {
        echo "<script>alert('IT Support Category ID is not provided in the URL.'); window.location='itsupport_category_list.php';</script>";
        exit;
    }
?>

<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0">
<meta name="description" content="CLL Ticketing System">
<meta name="author" content="Powered by CLL Systems Sdn Bhd">
<meta name="robots" content="noindex, nofollow">
<title>CLLXWARE-Edit IT Support - Brand</title>

<link rel='icon' type='image/x-icon' href='https://www.cllsystems.com/wp-content/themes/cllsystems/img/favicon.png'>
<link rel="stylesheet" href="assets/css/bootstrap.min.css">
<link rel="stylesheet" href="assets/css/animate.css">
<link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">
<link rel="stylesheet" href="assets/css/dataTables.bootstrap4.min.css">
<link rel="stylesheet" href="assets/plugins/fontawesome/css/fontawesome.min.css">
<link rel="stylesheet" href="assets/plugins/fontawesome/css/all.min.css">
<link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
<div id="global-loader">
    <div class="whirly-loader"></div>
</div>

<div class="main-wrapper">
<?php 
$role_id = $_SESSION["role_id"];
$sql20 = "SELECT * FROM `role-list` WHERE `role-id` = ?";
$stmt20 = mysqli_prepare($conn, $sql20);
mysqli_stmt_bind_param($stmt20, "s", $role_id);
mysqli_stmt_execute($stmt20);
$result20 = mysqli_stmt_get_result($stmt20);

if ($row20 = mysqli_fetch_assoc($result20)) {
    $all = $row20['ticket_all'];
    if ( $all != '1') {
        header("location: ./index.php");
        exit;
    }
} else {
    echo "<script>alert('Role data not found')</script>";
}

mysqli_stmt_close($stmt20);
include("header.php");
?>
</div>

<div class="page-wrapper">
    <div class="content">
        <div class="page-header">
            <div class="page-title">
                <h4>Edit IT Support - Brand</h4>
                <h6>Update Your IT Support - Brand</h6>
            </div>
        </div>
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <form action="#" method="post">
                        <input type="hidden" name="itsupport_category_id" value="<?php echo $itsupport_category_id; ?>">

                        <div class="col-lg-3 col-sm-6 col-12">
                            <div class="form-group">
                                <?php
                                $sql = "SELECT * FROM `itsupportinq_list`";
                                $result = mysqli_query($conn, $sql);
                                ?>
                                <label>IT Support Inquiry</label>
                                <select class="select" name="itsupport_inq" required>
                                    <option value='' disabled>Choose IT Support Inquiry</option>
                                    <?php
                                    while ($row = mysqli_fetch_assoc($result)) {
                                        $selected = ($row['itsupport_inq_id'] == $current_itsupport_inq_id) ? "selected" : "";
                                        echo "<option value='" . $row['itsupport_inq_id'] . "' $selected>" . $row['itsupport_inq'] . "</option>";
                                    }
                                    ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-lg-3 col-sm-6 col-12">
                            <div class="form-group">
                                <label>IT Support Category</label>
                                <input type="text" name="itsupport_category" value="<?php echo $current_itsupport_category; ?>" required class="form-control">
                            </div>
                        </div>

                        <div class="col-lg-12">
                            <div class="form-group">
                                <label>Description</label>
                                <textarea class="form-control" name="des"><?php echo $current_des; ?></textarea>
                            </div>
                        </div>

                        <div class="col-lg-12">
                            <input type="submit" class="btn btn-submit me-2" name="submit" value="Submit">
                            <a href="itsupport_category_list.php" class="btn btn-cancel">Cancel</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="assets/js/jquery-3.6.0.min.js"></script>
<script src="assets/js/feather.min.js"></script>
<script src="assets/js/jquery.slimscroll.min.js"></script>
<script src="assets/js/jquery.dataTables.min.js"></script>
<script src="assets/js/dataTables.bootstrap4.min.js"></script>
<script src="assets/js/bootstrap.bundle.min.js"></script>
<script src="assets/plugins/select2/js/select2.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalert2.all.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalerts.min.js"></script>
<script src="assets/js/script.js"></script>
</body>
</html>
<?php
} else {
    header("location: ./signin.php");
}
?>
