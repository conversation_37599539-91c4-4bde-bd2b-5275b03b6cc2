<?php
require("database.php");

if (isset($_SESSION["user"])) {
    $email = $_SESSION["user"];

    // Check if form is submitted
    if (isset($_POST["submit"])) {
        $office_id = $_POST["office_id"];
        $office_name = $_POST["office_name"];
        $location = $_POST["location"];

        // Check if office name already exists
        $check_sql = "SELECT * FROM `office_list` WHERE `office_name` = ? AND `office_id` != ?";
        $check_stmt = mysqli_prepare($conn, $check_sql);
        mysqli_stmt_bind_param($check_stmt, "ss", $office_name, $office_id);
        mysqli_stmt_execute($check_stmt);
        mysqli_stmt_store_result($check_stmt);

        if (mysqli_stmt_num_rows($check_stmt) > 0) {
            echo "<script>alert('Office name already exists.'); window.location='editoffice.php?office_id={$office_id}';</script>";
        } else {
            $sql = "UPDATE `office_list` SET `office_name` = ?, `location` = ? WHERE `office_id` = ?";
            $stmt = mysqli_prepare($conn, $sql);
            mysqli_stmt_bind_param($stmt, "sss", $office_name, $location, $office_id);

            if (mysqli_stmt_execute($stmt)) {
                echo "<script>alert('The office has been updated.'); window.location='office_list.php';</script>";
            } else {
                echo "<script>alert('Error: " . mysqli_error($conn) . "'); window.location='editoffice.php?office_id={$office_id}';</script>";
            }
            mysqli_stmt_close($stmt);
        }
        
        mysqli_stmt_close($check_stmt);
    }

    // Fetch office details if office_id is provided
    if (isset($_GET['office_id'])) {
        $office_id = $_GET['office_id'];
        $sql4 = "SELECT * FROM `office_list` WHERE `office_id` = ?";
        $stmt4 = mysqli_prepare($conn, $sql4);
        mysqli_stmt_bind_param($stmt4, "s", $office_id);
        mysqli_stmt_execute($stmt4);
        $result4 = mysqli_stmt_get_result($stmt4);

        if ($row4 = mysqli_fetch_assoc($result4)) {
            $current_office_name = $row4['office_name'];
            $current_office_location = $row4['location'];
        } else {
            echo "<script>alert('No office found with the provided ID.'); window.location='office_list.php';</script>";
            exit;
        }
        mysqli_stmt_close($stmt4);
    } else {
        echo "<script>alert('Office ID is not provided in the URL.'); window.location='office_list.php';</script>";
        exit;
    }
?>

<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0">
<meta name="description" content="CLL Ticketing System">
<meta name="author" content="Powered by CLL Systems Sdn Bhd">
<meta name="robots" content="noindex, nofollow">
<title>CLLXWARE-Edit Office</title>

<link rel='icon' type='image/x-icon' href='https://www.cllsystems.com/wp-content/themes/cllsystems/img/favicon.png'>
<link rel="stylesheet" href="assets/css/bootstrap.min.css">
<link rel="stylesheet" href="assets/css/animate.css">
<link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">
<link rel="stylesheet" href="assets/css/dataTables.bootstrap4.min.css">
<link rel="stylesheet" href="assets/plugins/fontawesome/css/fontawesome.min.css">
<link rel="stylesheet" href="assets/plugins/fontawesome/css/all.min.css">
<link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
<div id="global-loader">
    <div class="whirly-loader"></div>
</div>

<div class="main-wrapper">
<?php 
$role_id = $_SESSION["role_id"];
$sql20 = "SELECT * FROM `role-list` WHERE `role-id` = ?";
$stmt20 = mysqli_prepare($conn, $sql20);
mysqli_stmt_bind_param($stmt20, "s", $role_id);
mysqli_stmt_execute($stmt20);
$result20 = mysqli_stmt_get_result($stmt20);

if ($row20 = mysqli_fetch_assoc($result20)) {
    $all = $row20['ticket_all'];
    if ( $all != '1') {
        header("location: ./index.php");
        exit;
    }
} else {
    echo "<script>alert('Role data not found');</script>";
    exit;
}
mysqli_stmt_close($stmt20);
include("header.php");
?>
</div>

<div class="page-wrapper">
    <div class="content">
        <div class="page-header">
            <div class="page-title">
                <h4>Edit Office</h4>
                <h6>Update Office Details</h6>
            </div>
        </div>
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <form action="#" method="post">
                        <input type="hidden" name="office_id" value="<?php echo htmlspecialchars($office_id); ?>">
                        <div class="col-lg-6 col-sm-12">
                            <div class="form-group">
                                <label>Office Name</label>
                                <input type="text" name="office_name" value="<?php echo htmlspecialchars($current_office_name); ?>" required class="form-control">
                            </div>
                        </div>

                        <div class="col-lg-6 col-sm-12">
                            <div class="form-group">
                                <label>Office Location</label>
                                <input type="text" name="location" value="<?php echo htmlspecialchars($current_office_location); ?>" required class="form-control">
                            </div>
                        </div>

                        <div class="col-lg-12">
                            <input type="submit" class="btn btn-primary me-2" name="submit" value="Submit">
                            <a href="office_list.php" class="btn btn-secondary">Cancel</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="assets/js/jquery-3.6.0.min.js"></script>
<script src="assets/js/feather.min.js"></script>
<script src="assets/js/jquery.slimscroll.min.js"></script>
<script src="assets/js/jquery.dataTables.min.js"></script>
<script src="assets/js/dataTables.bootstrap4.min.js"></script>
<script src="assets/js/bootstrap.bundle.min.js"></script>
<script src="assets/plugins/select2/js/select2.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalert2.all.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalerts.min.js"></script>
<script src="assets/js/script.js"></script>
</body>
</html>
<?php
} else {
    header("location: ./signin.php");
    exit;
}
?>
