<?php
require("database.php");

if (isset($_SESSION["user"])) {
    $email = $_SESSION["user"];

    if (isset($_POST["submit"])) {
        $role = $_POST["role"];
        $des = $_POST["des"];
        $role_id = $_GET['role-id'];
        
        $inventory_view = isset($_POST["inventory_view"]) ? 1 : 0;
        $inventory_create = isset($_POST["inventory_create"]) ? 1 : 0;
        $inventory_edit = isset($_POST["inventory_edit"]) ? 1 : 0;
        $inventory_all = isset($_POST["inventory_all"]) ? 1 : 0;
        $import_create = isset($_POST["import_create"]) ? 1 : 0;
        $category_view = isset($_POST["category_view"]) ? 1 : 0;
        $category_create = isset($_POST["category_create"]) ? 1 : 0;
        $category_edit = isset($_POST["category_edit"]) ? 1 : 0;
        $category_delete = isset($_POST["category_delete"]) ? 1 : 0;
        $category_all = isset($_POST["category_all"]) ? 1 : 0;
        $brand_view = isset($_POST["brand_view"]) ? 1 : 0;
        $brand_create = isset($_POST["brand_create"]) ? 1 : 0;
        $brand_edit = isset($_POST["brand_edit"]) ? 1 : 0;
        $brand_delete = isset($_POST["brand_delete"]) ? 1 : 0;
        $brand_all = isset($_POST["brand_all"]) ? 1 : 0;
        $location_view = isset($_POST["location_view"]) ? 1 : 0;
        $location_create = isset($_POST["location_create"]) ? 1 : 0;
        $location_edit = isset($_POST["location_edit"]) ? 1 : 0;
        $location_all = isset($_POST["location_all"]) ? 1 : 0;
        $request_view = isset($_POST["request_view"]) ? 1 : 0;
        $request_create = isset($_POST["request_create"]) ? 1 : 0;
        $request_approval = isset($_POST["request_approval"]) ? 1 : 0;
        $request_all = isset($_POST["request_all"]) ? 1 : 0;
        $inventory_return_view = isset($_POST["inventory_return_view"]) ? 1 : 0;
        $inventory_return_edit = isset($_POST["inventory_return_edit"]) ? 1 : 0;
        $inventory_return_all = isset($_POST["inventory_return_all"]) ? 1 : 0;
        $user_view = isset($_POST["user_view"]) ? 1 : 0;
        $user_create = isset($_POST["user_create"]) ? 1 : 0;
        $user_edit = isset($_POST["user_edit"]) ? 1 : 0;
        $user_ban = isset($_POST["user_ban"]) ? 1 : 0;
        $user_all = isset($_POST["user_all"]) ? 1 : 0;
        $group_view = isset($_POST["group_view"]) ? 1 : 0;
        $group_create = isset($_POST["group_create"]) ? 1 : 0;
        $group_edit = isset($_POST["group_edit"]) ? 1 : 0;
        $group_delete = isset($_POST["group_delete"]) ? 1 : 0;
        $group_all = isset($_POST["group_all"]) ? 1 : 0;
        $request_smtp = isset($_POST["request_smtp"]) ? 1 : 0;
        $backup = isset($_POST["backup"]) ? 1 : 0;
        $user = isset($_POST["user"]) ? 1 : 0;
        $section_all = isset($_POST["section_all"]) ? 1 : 0;
        $hr = isset($_POST["hr"]) ? 1 : 0;
        $finance = isset($_POST["finance"]) ? 1 : 0;
        $it_support = isset($_POST["it_support"]) ? 1 : 0;
        $anonymous = isset($_POST["anonymous"]) ? 1 : 0;
        $ticket_view = isset($_POST["ticket_view"]) ? 1 : 0;
        $ticket_create = isset($_POST["ticket_create"]) ? 1 : 0;
        $ticket_edit = isset($_POST["ticket_edit"]) ? 1 : 0;
        $ticket_all = isset($_POST["ticket_all"]) ? 1 : 0;
        $offboarding_view = isset($_POST["offboarding_view"]) ? 1 : 0;
        $offboarding_create = isset($_POST["offboarding_create"]) ? 1 : 0;
        $offboarding_edit = isset($_POST["offboarding_edit"]) ? 1 : 0;
        $offboarding_all = isset($_POST["offboarding_all"]) ? 1 : 0;
        $offboarding_smtp = isset($_POST["offboarding_smtp"]) ? 1 : 0;
        $onboarding_view = isset($_POST["onboarding_view"]) ? 1 : 0;
        $onboarding_create = isset($_POST["onboarding_create"]) ? 1 : 0;
        $onboarding_edit = isset($_POST["onboarding_edit"]) ? 1 : 0;
        $onboarding_all = isset($_POST["onboarding_all"]) ? 1 : 0;



        $sql = "UPDATE `role-list` 
                SET `role` = ?, 
                    `role_description` = ?,  
                    `inventory_view` = ?,
                    `inventory_create` = ?,
                    `inventory_edit` = ?,
                    `inventory_all` = ?,
                    `import_create` = ?,
                    `category_view` = ?,
                    `category_create` = ?,
                    `category_edit` = ?,
                    `category_delete` = ?,
                    `category_all` = ?,
                    `brand_view` = ?,
                    `brand_create` = ?,
                    `brand_edit` = ?,
                    `brand_delete` = ?,
                    `brand_all` = ?,
                    `location_view` = ?,
                    `location_create` = ?,
                    `location_edit` = ?,
                    `location_all` = ?,
                    `request_view` = ?,
                    `request_create` = ?,
                    `request_approval` = ?,
                    `request_all` = ?,
                    `inventory_return_view` = ?,
                    `inventory_return_edit` = ?,
                    `inventory_return_all` = ?,
                    `user_view` = ?,
                    `user_create` = ?,
                    `user_edit` = ?,
                    `user_ban` = ?,
                    `user_all` = ?,
                    `group_view` = ?,
                    `group_create` = ?,
                    `group_edit` = ?,
                    `group_delete` = ?,
                    `group_all` = ?,
                    `request_smtp` = ?,
                    `backup` = ?,
                    `user` = ?,
                    `section_all` = ?,
                    `hr` = ?,
                    `finance` = ?,
                    `it_support` = ?,
                    `anonymous` = ?,
                    `ticket_all` = ?,
                    `offboarding_view` = ?,
                    `offboarding_create` = ?,
                    `offboarding_edit` = ?,
                    `offboarding_all` = ?,
                    `offboarding_smtp` = ?,
                    `onboarding_view` = ?,
                    `onboarding_create` = ?,
                    `onboarding_edit` = ?,
                    `onboarding_all` = ?
                WHERE `role-id` = ?";

        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, "ssiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiis",
                                $role, 
                                $des, 
                                $inventory_view, 
                                $inventory_create, 
                                $inventory_edit, 
                                $inventory_all, 
                                $import_create, 
                                $category_view, 
                                $category_create, 
                                $category_edit, 
                                $category_delete, 
                                $category_all, 
                                $brand_view, 
                                $brand_create, 
                                $brand_edit, 
                                $brand_delete, 
                                $brand_all, 
                                $location_view, 
                                $location_create, 
                                $location_edit, 
                                $location_all, 
                                $request_view, 
                                $request_create, 
                                $request_approval, 
                                $request_all, 
                                $inventory_return_view, 
                                $inventory_return_edit, 
                                $inventory_return_all, 
                                $user_view, 
                                $user_create, 
                                $user_edit, 
                                $user_ban, 
                                $user_all, 
                                $group_view, 
                                $group_create, 
                                $group_edit, 
                                $group_delete, 
                                $group_all, 
                                $request_smtp, 
                                $backup, 
                                $user, 
                                $section_all, 
                                $hr,
                                $finance,
                                $it_support,
                                $anonymous,
                                $ticket_all, 
                                $offboarding_view,
                                $offboarding_create,
                                $offboarding_edit,
                                $offboarding_all,
                                $offboarding_smtp,
                                $onboarding_view,
                                $onboarding_create,
                                $onboarding_edit,
                                $onboarding_all,
                                $role_id);

        if (mysqli_stmt_execute($stmt)) {
            echo "<script>alert('The role $role has been modified.'); window.location='editpermission.php?role-id=$role_id';</script>";
        } else {
            echo "<script>alert('Error: " . mysqli_error($conn) . "'); window.location='grouppermissions.php';</script>";
        }
        mysqli_stmt_close($stmt);
    }
?>
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0">
<meta name="description" content="CLL Warehouse Inventory System">
<meta name="author" content="Powered by CLL Systems Sdn Bhd">
<meta name="robots" content="noindex, nofollow">
<title>CLLXWARE-Edit Group Permission</title>

<link rel='icon' type='image/x-icon' href='https://www.cllsystems.com/wp-content/themes/cllsystems/img/favicon.png'>

<link rel="stylesheet" href="assets/css/bootstrap.min.css">
<link rel="stylesheet" href="assets/css/animate.css">
<link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">
<link rel="stylesheet" href="assets/css/dataTables.bootstrap4.min.css">
<link rel="stylesheet" href="assets/plugins/fontawesome/css/fontawesome.min.css">
<link rel="stylesheet" href="assets/plugins/fontawesome/css/all.min.css">
<link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
<div id="global-loader">
<div class="whirly-loader"> </div>
</div>

<div class="main-wrapper">
<?php 
$role_id = $_SESSION["role_id"];
$sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
$result20 = mysqli_query($conn, $sql20);

if ($result20) {
    $row20 = mysqli_fetch_assoc($result20);
    if ($row20) {
        $edit = $row20['group_edit'];
        $all = $row20['group_all'];
        if($edit != '1' and $all !='1'){
        header("location: ./index.php");
       }
    } else {
        echo "<script>alert('Role data not found')</script>";
    }
} else {
    echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
}
include("header.php");?>
</div>

<div class="page-wrapper">
<div class="content">
<div class="page-header">
<div class="page-title">
<h4>Create Permission</h4>
<h6>Manage Create Permissions</h6>
</div>
</div>
<form action="#" method="post">
<div class="card">
<div class="card-body">
<?php
if(isset($_GET['role-id'])) {   
    $role_id = $_GET['role-id'];
    $sql4 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
    $result4 = mysqli_query($conn, $sql4);
    if ($result4 && mysqli_num_rows($result4) > 0) {
        $row4 = mysqli_fetch_assoc($result4);
echo"
<div class='row'>
<div class='col-lg-3 col-sm-12'>
<div class='form-group'>
<label>Role</label>
<input type='text' name='role' value='{$row4['role']}' required>
</div>
</div>
<div class='col-lg-9 col-sm-12'>
<div class='form-group'>
<label>Role Description</label>
<input type='text' name='des' value='{$row4['role_description']}'>
</div>
</div>
</div>
<div class='row'>
<div class='col-12'>
<div class='productdetails product-respon'>
<ul>

<li>
<h4>Inventory List</h4>
<div class='input-checkset'>
<ul>
<li>
<label class='inputcheck'>Select All
<input type='checkbox' name='inventory_all' value='1' ";?> <?php echo $row4['inventory_all'] == 1 ? 'checked' : ''; ?><?php echo">
<span class='checkmark'></span>
</label>
</li>
<li>
<label class='inputcheck'>View
<input type='checkbox' name='inventory_view' value='1' ";?> <?php echo $row4['inventory_view'] == 1 ? 'checked' : ''; ?><?php echo">
<span class='checkmark'></span>
</label>
</li>
<li>
<label class='inputcheck'>Create
<input type='checkbox' name='inventory_create' value='1' ";?> <?php echo $row4['inventory_create'] == 1 ? 'checked' : ''; ?><?php echo">
<span class='checkmark'></span>
</label>
</li>
<li>
<label class='inputcheck'>Edit
<input type='checkbox' name='inventory_edit' value='1' ";?> <?php echo $row4['inventory_edit'] == 1 ? 'checked' : ''; ?><?php echo">
<span class='checkmark'></span>
</label>
</li>
</ul>
</div>
</li>
<li>
<h4>Import Product</h4>
<div class='input-checkset'>
<ul>
<label class='inputcheck'>Create
<input type='checkbox' name='import_create' value='1' ";?> <?php echo $row4['import_create'] == 1 ? 'checked' : ''; ?><?php echo">
<span class='checkmark'></span>
</label>
</li>
</ul>
</div>
</li>
<li>
<h4>Category List</h4>
<div class='input-checkset'>
<ul>
<li>
<label class='inputcheck'>Select All
<input type='checkbox' name='category_all' value='1'";?> <?php echo $row4['category_all'] == 1 ? 'checked' : ''; ?><?php echo">
<span class='checkmark'></span>
</label>
</li>
<li>
<label class='inputcheck'>View
<input type='checkbox' name='category_view' value='1' ";?> <?php echo $row4['category_view'] == 1 ? 'checked' : ''; ?><?php echo">
<span class='checkmark'></span>
</label>
</li>
<li>
<label class='inputcheck'>Create
<input type='checkbox' name='category_create' value='1' ";?> <?php echo $row4['category_create'] == 1 ? 'checked' : ''; ?><?php echo">
<span class='checkmark'></span>
</label>
</li>
<li>
<label class='inputcheck'>Edit
<input type='checkbox' name='category_edit' value='1' ";?> <?php echo $row4['category_edit'] == 1 ? 'checked' : ''; ?><?php echo">
<span class='checkmark'></span>
</label>
</li>
<li>
<label class='inputcheck'>Hide
<input type='checkbox' name='category_delete' value='1' ";?> <?php echo $row4['category_delete'] == 1 ? 'checked' : ''; ?><?php echo">
<span class='checkmark'></span>
</label>
</li>
</ul>
</div>
</li>
<li>
<h4>Brand List</h4>
<div class='input-checkset'>
<ul>
<li>
<label class='inputcheck'>Select All
<input type='checkbox' name='brand_all' value='1'";?> <?php echo $row4['brand_all'] == 1 ? 'checked' : ''; ?><?php echo">
<span class='checkmark'></span>
</label>
</li>
<li>
<label class='inputcheck'>View
<input type='checkbox' name='brand_view' value='1'";?> <?php echo $row4['brand_view'] == 1 ? 'checked' : ''; ?><?php echo">
<span class='checkmark'></span>
</label>
</li>
<li>
<label class='inputcheck'>Create
<input type='checkbox' name='brand_create' value='1'";?> <?php echo $row4['brand_create'] == 1 ? 'checked' : ''; ?><?php echo">
<span class='checkmark'></span>
</label>
</li>
<li>
<label class='inputcheck'>Edit
<input type='checkbox' name='brand_edit' value='1'";?> <?php echo $row4['brand_edit'] == 1 ? 'checked' : ''; ?><?php echo">
<span class='checkmark'></span>
</label>
</li>
<li>
<label class='inputcheck'>Hide
<input type='checkbox' name='brand_delete' value='1'";?> <?php echo $row4['brand_delete'] == 1 ? 'checked' : ''; ?><?php echo">
<span class='checkmark'></span>
</label>
</li>
</ul>
</div>
</li>
<li>
<h4>Location List</h4>
<div class='input-checkset'>
<ul>
<li>
<label class='inputcheck'>Select All
<input type='checkbox' name='location_all' value='1'";?> <?php echo $row4['location_all'] == 1 ? 'checked' : ''; ?><?php echo">
<span class='checkmark'></span>
</label>
</li>
<li>
<label class='inputcheck'>View
<input type='checkbox' name='location_view' value='1'";?> <?php echo $row4['location_view'] == 1 ? 'checked' : ''; ?><?php echo">
<span class='checkmark'></span>
</label>
</li>
<li>
<label class='inputcheck'>Create
<input type='checkbox' name='location_create' value='1'";?> <?php echo $row4['location_create'] == 1 ? 'checked' : ''; ?><?php echo">
<span class='checkmark'></span>
</label>
</li>
<li>
<label class='inputcheck'>Edit
<input type='checkbox' name='location_edit' value='1'";?> <?php echo $row4['location_edit'] == 1 ? 'checked' : ''; ?><?php echo">
<span class='checkmark'></span>
</label>
</li>
</ul>
</div>
</li>
<li>
<h4>Request List</h4>
<div class='input-checkset'>
<ul>
<li>
<label class='inputcheck'>Select All
<input type='checkbox' name='request_all' value='1'";?> <?php echo $row4['request_all'] == 1 ? 'checked' : ''; ?><?php echo">
<span class='checkmark'></span>
</label>
</li>
<li>
<label class='inputcheck'>View
<input type='checkbox' name='request_view' value='1'";?> <?php echo $row4['request_view'] == 1 ? 'checked' : ''; ?><?php echo">
<span class='checkmark'></span>
 </label>
</li>
<li>
<label class='inputcheck'>Create
<input type='checkbox' name='request_create' value='1' ";?> <?php echo $row4['request_create'] == 1 ? 'checked' : ''; ?><?php echo">
<span class='checkmark'></span>
</label>
</li>
<li>
<label class='inputcheck'>Approval
<input type='checkbox' name='request_approval' value='1'";?> <?php echo $row4['request_approval'] == 1 ? 'checked' : ''; ?><?php echo">
<span class='checkmark'></span>
</label>
</li>
<li>
<label class='inputcheck'>Send Mail Notification
<input type='checkbox' name='request_smtp' value='1'";?> <?php echo $row4['request_smtp'] == 1 ? 'checked' : ''; ?><?php echo">
<span class='checkmark'></span>
</label>
</li>

</ul>
</div>
</li>
<li>
<h4>Inventory Return List</h4>
<div class='input-checkset'>
<ul>
<li>
<label class='inputcheck'>Select All
<input type='checkbox' name='inventory_return_all' value='1'";?> <?php echo $row4['inventory_return_all'] == 1 ? 'checked' : ''; ?><?php echo">
<span class='checkmark'></span>
</label>
</li>
<li>
<label class='inputcheck'>View
<input type='checkbox' name='inventory_return_view' value='1'";?> <?php echo $row4['inventory_return_view'] == 1 ? 'checked' : ''; ?><?php echo">
<span class='checkmark'></span>
</label>
</li>
<li>
<label class='inputcheck'>Edit
<input type='checkbox' name='inventory_return_edit' value='1'";?> <?php echo $row4['inventory_return_edit'] == 1 ? 'checked' : ''; ?><?php echo">
<span class='checkmark'></span>
</label>
</li>
</ul>
</div>
</li>
<li>
<h4>User List</h4>
<div class='input-checkset'>
<ul>
<li>
<label class='inputcheck'>Select All
<input type='checkbox' name='user_all' value='1'";?> <?php echo $row4['user_all'] == 1 ? 'checked' : ''; ?><?php echo">
<span class='checkmark'></span>
</label>
</li>
<li>
<label class='inputcheck'>View
<input type='checkbox' name='user_view' value='1'";?> <?php echo $row4['user_view'] == 1 ? 'checked' : ''; ?><?php echo">
<span class='checkmark'></span>
</label>
</li>
<li>
<label class='inputcheck'>Create
<input type='checkbox' name='user_create' value='1'";?> <?php echo $row4['user_create'] == 1 ? 'checked' : ''; ?><?php echo">
<span class='checkmark'></span>
</label>
</li>
<li>
<label class='inputcheck'>Edit
<input type='checkbox' name='user_edit' value='1'";?> <?php echo $row4['user_edit'] == 1 ? 'checked' : ''; ?><?php echo">
<span class='checkmark'></span>
</label>
</li>
<li>
<label class='inputcheck'>Ban
<input type='checkbox' name='user_ban' value='1'";?> <?php echo $row4['user_ban'] == 1 ? 'checked' : ''; ?><?php echo">
<span class='checkmark'></span>
</label>
</li>
</ul>
</div>
</li>
<li>
<h4>Group Permission</h4>
<div class='input-checkset'>
<ul>
<li>
<label class='inputcheck'>Select All
<input type='checkbox' name='group_all' value='1'";?> <?php echo $row4['group_all'] == 1 ? 'checked' : ''; ?><?php echo">
<span class='checkmark'></span>
</label>
</li>
<li>
<label class='inputcheck'>View
<input type='checkbox' name='group_view' value='1'";?> <?php echo $row4['group_view'] == 1 ? 'checked' : ''; ?><?php echo">
<span class='checkmark'></span>
</label>
</li>
<li>
<label class='inputcheck'>Create
<input type='checkbox' name='group_create' value='1'";?> <?php echo $row4['group_create'] == 1 ? 'checked' : ''; ?><?php echo">
<span class='checkmark'></span>
</label>
</li>
<li>
<label class='inputcheck'>Edit
<input type='checkbox' name='group_edit' value='1'";?> <?php echo $row4['group_edit'] == 1 ? 'checked' : ''; ?><?php echo">
<span class='checkmark'></span>
</label>
</li>
<li>
<label class='inputcheck'>Hide
<input type='checkbox' name='group_delete' value='1'";?> <?php echo $row4['group_delete'] == 1 ? 'checked' : ''; ?><?php echo">
<span class='checkmark'></span>
</label>
</li>
</ul>
</div>
</li>
<li>
<h4>Daily Backup</h4>
<div class='input-checkset'>
<ul>
<li>
<label class='inputcheck'>Receive Backup
<input type='checkbox' name='backup' value='1'";?> <?php echo $row4['backup'] == 1 ? 'checked' : ''; ?><?php echo">
<span class='checkmark'></span>
</label>
</li>
</ul>
</div>
</li>
<li>
<h4>Ticketing List</h4>
<div class='input-checkset'>
<ul>
<li>
<label class='inputcheck'>Select All
<input type='checkbox' name='section_all' value='1'";?> <?php echo $row4['section_all'] == 1 ? 'checked' : ''; ?><?php echo">
<span class='checkmark'></span>
</label>
</li>
<li>
<label class='inputcheck'>User
<input type='checkbox' name='user' value='1'";?> <?php echo $row4['user'] == 1 ? 'checked' : ''; ?><?php echo">
<span class='checkmark'></span>
</label>
</li>
<li>
<label class='inputcheck'>HR
<input type='checkbox' name='hr' value='1'";?> <?php echo $row4['hr'] == 1 ? 'checked' : ''; ?><?php echo">
<span class='checkmark'></span>
</label>
</li>
<li>
<label class='inputcheck'>Finance
<input type='checkbox' name='finance' value='1'";?> <?php echo $row4['finance'] == 1 ? 'checked' : ''; ?><?php echo">
<span class='checkmark'></span>
</label>
</li>
<li>
<label class='inputcheck'>IT Support
<input type='checkbox' name='it_support' value='1'";?> <?php echo $row4['it_support'] == 1 ? 'checked' : ''; ?><?php echo">
<span class='checkmark'></span>
</label>
</li>
<li>
<label class='inputcheck'>Anonymous
<input type='checkbox' name='anonymous' value='1'";?> <?php echo $row4['anonymous'] == 1 ? 'checked' : ''; ?><?php echo">
<span class='checkmark'></span>
</label>
</li>

</ul>
</div>
</li>
<li>
<h4>Ticket Form List</h4>
<div class='input-checkset'>
<ul>
<li>
<label class='inputcheck'>Select All
<input type='checkbox' name='ticket_all' value='1'";?> <?php echo $row4['ticket_all'] == 1 ? 'checked' : ''; ?><?php echo">
<span class='checkmark'></span>
</label>
</li>
</ul>
</div>
</li>

<li>
<h4>Off Boarding List</h4>
<div class='input-checkset'>
<ul>
<li>
<label class='inputcheck'>Select All
<input type='checkbox' name='offboarding_all' value='1'";?> <?php echo $row4['offboarding_all'] == 1 ? 'checked' : ''; ?><?php echo">
<span class='checkmark'></span>
</label>
</li>
<li>
<label class='inputcheck'>View
<input type='checkbox' name='offboarding_view' value='1'";?> <?php echo $row4['offboarding_view'] == 1 ? 'checked' : ''; ?><?php echo">
<span class='checkmark'></span>
</label>
</li>
<li>
<label class='inputcheck'>Create
<input type='checkbox' name='offboarding_create' value='1'";?> <?php echo $row4['offboarding_create'] == 1 ? 'checked' : ''; ?><?php echo">
<span class='checkmark'></span>
</label>
</li>
<li>
<label class='inputcheck'>Edit
<input type='checkbox' name='offboarding_edit' value='1'";?> <?php echo $row4['offboarding_edit'] == 1 ? 'checked' : ''; ?><?php echo">
<span class='checkmark'></span>
</label>
</li>
<li>
<label class='inputcheck'>Send Mail Notification
<input type='checkbox' name='offboarding_smtp' value='1'";?> <?php echo $row4['offboarding_smtp'] == 1 ? 'checked' : ''; ?><?php echo">
<span class='checkmark'></span>
</label>
</li>
</ul>
</div>
</li>

<li>
<h4>On Boarding List</h4>
<div class='input-checkset'>
<ul>
<li>
<label class='inputcheck'>Select All
<input type='checkbox' name='onboarding_all' value='1'";?> <?php echo $row4['onboarding_all'] == 1 ? 'checked' : ''; ?><?php echo">
<span class='checkmark'></span>
</label>
</li>
<li>
<label class='inputcheck'>View
<input type='checkbox' name='onboarding_view' value='1'";?> <?php echo $row4['onboarding_view'] == 1 ? 'checked' : ''; ?><?php echo">
<span class='checkmark'></span>
</label>
</li>
<li>
<label class='inputcheck'>Create
<input type='checkbox' name='onboarding_create' value='1'";?> <?php echo $row4['onboarding_create'] == 1 ? 'checked' : ''; ?><?php echo">
<span class='checkmark'></span>
</label>
</li>
<li>
<label class='inputcheck'>Edit
<input type='checkbox' name='onboarding_edit' value='1'";?> <?php echo $row4['onboarding_edit'] == 1 ? 'checked' : ''; ?><?php echo">
<span class='checkmark'></span>
</label>
</li>

</ul>
</div>
</li>
</ul>

</div>
</div>
</div>";
} else {
    echo "<p>No role found with the provided ID.</p>";
  }

} else {
    echo "<p>Role ID is not provided in the URL.</p>";
    }
?>
</div>
</div>
<div class="col-lg-12">
<input type='submit'  class='btn btn-submit me-2' name='submit' value='Submit'>
<a href="grouppermissions.php" class="btn btn-cancel">Cancel</a>
</div>
</form>
</div>
</div>
</div>

<script src="assets/js/jquery-3.6.0.min.js"></script>
<script src="assets/js/feather.min.js"></script>
<script src="assets/js/jquery.slimscroll.min.js"></script>
<script src="assets/js/jquery.dataTables.min.js"></script>
<script src="assets/js/dataTables.bootstrap4.min.js"></script>
<script src="assets/js/bootstrap.bundle.min.js"></script>
<script src="assets/plugins/select2/js/select2.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalert2.all.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalerts.min.js"></script>
<script src="assets/js/script.js"></script>
</body>
</html>
<?php
}else{
    header("location: ./signin.php");
}
?>
