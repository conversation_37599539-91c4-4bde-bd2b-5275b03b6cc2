<?php
require("database.php");

// Check if user is logged in
if(isset($_SESSION["user"])){
    $email=$_SESSION["user"];
    
    if (isset($_POST["submit"])) {
        $name = $_POST["name"];
        $designation = $_POST["designation"];
        $department = $_POST['department'];
        $des = $_POST['des'];
        $acc_id = $_GET['acc_id'];
        $role = $_POST['role']; 
        
        $sql = "UPDATE `user-list` 
                SET `name` = ?, 
                    `designation` = ?,
                    `department` = ?,
                    `description` = ?
                WHERE `acc_id` = ?";
        
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, "sssss", $name, $designation, $department, $des, $acc_id);
        
        if (mysqli_stmt_execute($stmt)) {
            $update_role_sql = "UPDATE `useracc` SET `role_id` = ? WHERE `acc_id` = ?";
            $stmt_role = mysqli_prepare($conn, $update_role_sql);
            mysqli_stmt_bind_param($stmt_role, "ss", $role, $acc_id);
            
            if (mysqli_stmt_execute($stmt_role)) {
                echo "<script>alert('The user $name has been modified.'); window.location='userlist.php';</script>";
            } else {
                echo "<script>alert('Error updating role: " . mysqli_error($conn) . "'); window.location='userlist.php';</script>";
            }
            
            mysqli_stmt_close($stmt_role);
        } else {
            echo "<script>alert('Error updating user information: " . mysqli_error($conn) . "'); window.location='userlist.php';</script>";
        }
        
        // Close statement
        mysqli_stmt_close($stmt);
    }

?>

<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0">
<meta name="description" content="CLL Warehouse Inventory System">
<meta name="author" content="Powered by CLL Systems Sdn Bhd">
<meta name="robots" content="noindex, nofollow">
<title>CLLXWARE-Edit User</title>

<link rel='icon' type='image/x-icon' href='https://www.cllsystems.com/wp-content/themes/cllsystems/img/favicon.png'>

<link rel="stylesheet" href="assets/css/bootstrap.min.css">

<link rel="stylesheet" href="assets/css/animate.css">

<link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">

<link rel="stylesheet" href="assets/css/dataTables.bootstrap4.min.css">

<link rel="stylesheet" href="assets/plugins/fontawesome/css/fontawesome.min.css">
<link rel="stylesheet" href="assets/plugins/fontawesome/css/all.min.css">

<link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
<div id="global-loader">
<div class="whirly-loader"> </div>
</div>

<div class="main-wrapper">

<?php 
$role_id = $_SESSION["role_id"];
$sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
$result20 = mysqli_query($conn, $sql20);

if ($result20) {
    $row20 = mysqli_fetch_assoc($result20);
    if ($row20) {
        $edit = $row20['user_edit'];
        $all = $row20['user_all'];
        if($edit != '1' and $all !='1'){
        header("location: ./index.php");
       }
    } else {
        echo "<script>alert('Role data not found')</script>";
    }
} else {
    echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
}

include("header.php");?>
</div>

<div class="page-wrapper">
<div class="content">
<div class="page-header">
<div class="page-title">
<h4>User Management</h4>
<h6>Edit User Profile</h6>
</div>
</div>
<form action="#" method="post">
<div class="card">
<div class="card-body">
<div class="row">
<?php
if(isset($_GET['acc_id'])) {
    $acc_id = $_GET['acc_id'];
    $sql4 = "SELECT * FROM `user-list` WHERE `acc_id` = '$acc_id'";
    $result4 = mysqli_query($conn, $sql4);
    if ($result4 && mysqli_num_rows($result4) > 0) {
    $row4 = mysqli_fetch_assoc($result4);

    $sql5 = "SELECT * FROM `useracc` WHERE `acc_id` = '$acc_id'";
    $result5 = mysqli_query($conn, $sql5);
    $row5 = mysqli_fetch_assoc($result5);

    $role_id = $row5['role_id'];

  
    $sql6 = "SELECT * FROM `role-list`";
    $result6 = mysqli_query($conn, $sql6);

echo"
<div class='col-lg-3 col-sm-6 col-12'>
<div class='form-group'>
<label>User Full Name</label>
<input type='text' value='{$row4['name']}' name='name' required>
</div>
</div>
<div class='col-lg-3 col-sm-6 col-12'>
<div class='form-group'>
<label>Email</label>
<input type='text' value='{$row4['email']}' name='email' readonly>
</div>
</div>
<div class='col-lg-3 col-sm-6 col-12'>
<div class='form-group'>
<label>Designation</label>
<input type='text' value='{$row4['designation']}' name='designation' required>
</div>
</div>
<div class='col-lg-3 col-sm-6 col-12'>
<div class='form-group'>
<label>Department</label>
<select class='select' name='department' required>
<option value='' disabled selected>Choose Department</option>
<option value='Admin Department' "; if($row4['department'] == "Admin Department") echo "selected"; echo">Admin Department</option>
<option value='Finance Department' "; if($row4['department'] == "Finance Department") echo "selected"; echo">Finance Department</option>
<option value='Sales & Marketing' "; if($row4['department'] == "Sales & Marketing") echo "selected"; echo">Sales & Marketing</option>
<option value='Technical Department' "; if($row4['department'] == "Technical Department") echo "selected"; echo">Technical Department</option>
<option value='Software Team' "; if($row4['department'] == "Software Team") echo "selected"; echo">Software Team</option>
<option value='Network & Security Department' "; if($row4['department'] == "Network & Security Department") echo "selected"; echo">Network & Security Department</option>
</select>
</div>
</div>
<div class='col-lg-3 col-sm-6 col-12'>
<div class='form-group'>
<label>Role</label>
<select class='select' name='role' required>
<option value='' disabled selected>Choose User Role</option>";?>
<?php
while ($row6 = mysqli_fetch_assoc($result6)) {
    // Check if $row6['hide'] is not equal to 1
    if ($row6['hide'] != 1) {
        echo "<option ";
        if ($role_id == $row6['role-id']) {
            echo "selected ";
        }
        echo "value='" . $row6['role-id'] . "'>" . $row6['role'] . "</option>";
    }
}
?>


<?php
echo"
</select>
</div>
</div>
<div class='col-lg-12'>
<div class='form-group'>
<label>Description</label>
<textarea class='form-control' name='des'>{$row4['description']}</textarea>
</div>
</div>

<div class='col-lg-12'>
<input type='submit'  class='btn btn-submit me-2' name='submit' value='Submit'>
<a href='userlist.php' class='btn btn-cancel'>Cancel</a>
</div>";

} else {
    // Handle case where no inventory item with the provided inventory-id is found
    echo "<p>No user found with the provided ID.</p>";
  }

} else {
    // Handle case where inventory-id is not provided in the URL
    echo "<p>User ID is not provided in the URL.</p>";
    }
?>

</div>
</div>
</div>
</form>
</div>
</div>
</div>


<script src="assets/js/jquery-3.6.0.min.js"></script>

<script src="assets/js/feather.min.js"></script>

<script src="assets/js/jquery.slimscroll.min.js"></script>

<script src="assets/js/jquery.dataTables.min.js"></script>
<script src="assets/js/dataTables.bootstrap4.min.js"></script>

<script src="assets/js/bootstrap.bundle.min.js"></script>

<script src="assets/plugins/select2/js/select2.min.js"></script>

<script src="assets/plugins/sweetalert/sweetalert2.all.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalerts.min.js"></script>

<script src="assets/js/script.js"></script>
</body>
</html>
<?php
}else{
    header("location: ./signin.php");
}
?>