<?php
require('database.php');

// Check if user is logged in and has admin permissions
if (!isset($_SESSION['user'])) {
    header("location: ./signin.php");
    exit;
}

// Check admin permissions
$role_id = $_SESSION["role_id"];
$sql_admin = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
$result_admin = mysqli_query($conn, $sql_admin);
$row_admin = mysqli_fetch_assoc($result_admin);

if (!$row_admin || $row_admin['onboarding_all'] != '1') {
    echo "<script>alert('Access denied. Admin permissions required.'); window.location='dashboard.php';</script>";
    exit;
}

// Create email configuration table if it doesn't exist
$create_table_sql = "CREATE TABLE IF NOT EXISTS `email_config_list` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `config_name` varchar(100) NOT NULL,
    `email_addresses` TEXT NOT NULL,
    `description` TEXT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `config_name` (`config_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

mysqli_query($conn, $create_table_sql);

// Handle form submissions
if ($_POST) {
    $success_message = "";
    
    // Add new email configuration
    if (isset($_POST['add_config'])) {
        $config_name = $_POST['config_name'] ?? '';
        $email_addresses = $_POST['email_addresses'] ?? '';
        $description = $_POST['description'] ?? '';
        
        if (!empty($config_name) && !empty($email_addresses)) {
            $stmt = mysqli_prepare($conn, "INSERT INTO `email_config_list` (`config_name`, `email_addresses`, `description`) VALUES (?, ?, ?)");
            mysqli_stmt_bind_param($stmt, 'sss', $config_name, $email_addresses, $description);
            
            if (mysqli_stmt_execute($stmt)) {
                $success_message = "Email configuration added successfully!";
            } else {
                $error_message = "Error: Configuration name already exists or database error.";
            }
            mysqli_stmt_close($stmt);
        } else {
            $error_message = "Please fill in all required fields.";
        }
    }
    
    // Update email configuration
    if (isset($_POST['update_config'])) {
        $config_id = $_POST['config_id'] ?? '';
        $config_name = $_POST['config_name'] ?? '';
        $email_addresses = $_POST['email_addresses'] ?? '';
        $description = $_POST['description'] ?? '';
        
        if (!empty($config_id) && !empty($config_name) && !empty($email_addresses)) {
            $stmt = mysqli_prepare($conn, "UPDATE `email_config_list` SET `config_name` = ?, `email_addresses` = ?, `description` = ? WHERE `id` = ?");
            mysqli_stmt_bind_param($stmt, 'sssi', $config_name, $email_addresses, $description, $config_id);
            
            if (mysqli_stmt_execute($stmt)) {
                $success_message = "Email configuration updated successfully!";
            } else {
                $error_message = "Error updating configuration.";
            }
            mysqli_stmt_close($stmt);
        }
    }
    
    // Delete email configuration
    if (isset($_POST['delete_config'])) {
        $config_id = $_POST['config_id'] ?? '';
        
        if (!empty($config_id)) {
            $stmt = mysqli_prepare($conn, "DELETE FROM `email_config_list` WHERE `id` = ?");
            mysqli_stmt_bind_param($stmt, 'i', $config_id);
            
            if (mysqli_stmt_execute($stmt)) {
                $success_message = "Email configuration deleted successfully!";
            } else {
                $error_message = "Error deleting configuration.";
            }
            mysqli_stmt_close($stmt);
        }
    }
}

// Get all email configurations
$email_configs = [];
$result = mysqli_query($conn, "SELECT * FROM `email_config_list` ORDER BY `config_name`");
while ($row = mysqli_fetch_assoc($result)) {
    $email_configs[] = $row;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0">
    <title>Email Configuration Management - CLLXWARE</title>
    <link rel="shortcut icon" type="image/x-icon" href="assets/img/favicon.jpg">
    <link rel="stylesheet" href="assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="assets/css/animate.css">
    <link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">
    <link rel="stylesheet" href="assets/css/dataTables.bootstrap4.min.css">
    <link rel="stylesheet" href="assets/plugins/fontawesome/css/fontawesome.min.css">
    <link rel="stylesheet" href="assets/plugins/fontawesome/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">

    <style>
        .form-group { position: relative; }
        .form-group input,
        .form-group textarea,
        .form-group select { padding-left: 12px; }
        .card { border: 1px solid #f0f0f0; padding: 20px; box-shadow: 0 4px 8px rgba(0,0,0,0.1); margin-bottom: 20px; }
        .section-header { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            color: white; 
            padding: 15px; 
            border-radius: 8px; 
            margin-bottom: 20px;
            text-align: center;
        }
        .config-item {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background: #f8f9fa;
        }
        .config-item h6 {
            color: #495057;
            margin-bottom: 10px;
        }
        .email-list {
            background: white;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #dee2e6;
            font-family: monospace;
            font-size: 14px;
            white-space: pre-line;
        }
        .btn-edit, .btn-delete {
            margin: 2px;
        }
    </style>
</head>

<body>
<div id="global-loader">
    <div class="whirly-loader"> </div>
</div>

<div class="main-wrapper">
    <?php include 'header.php'; ?>

    <div class="page-wrapper">
        <div class="content">
            <div class="page-header">
                <div class="page-title">
                    <h4>Email Configuration Management</h4>
                    <h6>Manage email address groups for onboarding notifications</h6>
                </div>
            </div>

            <?php if (isset($success_message)): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <?php endif; ?>

            <?php if (isset($error_message)): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <?php endif; ?>



            <!-- Existing Email Configurations -->
            <div class="card">
                <div class="card-body">
                    <div class="table-top">
                        <div class="search-set">
                            <div class="search-path">
                                <a class="btn btn-filter" id="filter_search">
                                    <img src="assets/img/icons/filter.svg" alt="img">
                                    <span><img src="assets/img/icons/closes.svg" alt="img"></span>
                                </a>
                            </div>
                            <div class="search-input">
                                <a class="btn btn-searchset"><img src="assets/img/icons/search-white.svg" alt="img"></a>
                            </div>
                        </div>
                        <div class="wordset">
                            <ul>
                                <li>
                                    <a data-bs-toggle="tooltip" data-bs-placement="top" title="Add New Email Configuration" class="btn btn-added" id="addEmailBtn">
                                        <img src="assets/img/icons/plus.svg" alt="img" class="me-1">New Configuration
                                    </a>
                                </li>
                                <li>
                                    <a data-bs-toggle="tooltip" data-bs-placement="top" title="Print"><img src="assets/img/icons/printer.svg" alt="img" id="printButton"></a>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table datanew">
                            <thead>
                                <tr>
                                    <th>No</th>
                                    <th>Configuration Name</th>
                                    <th>Email Addresses</th>
                                    <th>Description</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($email_configs)): ?>
                                <tr>
                                    <td colspan="5" class="text-center">
                                        <div class="alert alert-info mb-0">
                                            <i class="fas fa-info-circle"></i> No email configurations found. Add your first configuration above.
                                        </div>
                                    </td>
                                </tr>
                                <?php else: ?>
                                <?php $counter = 1; foreach ($email_configs as $config): ?>
                                <tr>
                                    <td><?php echo $counter++; ?></td>
                                    <td><strong><?php echo htmlspecialchars($config['config_name']); ?></strong></td>
                                    <td>
                                        <div style="max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" title="<?php echo htmlspecialchars($config['email_addresses']); ?>">
                                            <?php echo htmlspecialchars($config['email_addresses']); ?>
                                        </div>
                                    </td>
                                    <td><?php echo htmlspecialchars($config['description'] ?? '-'); ?></td>
                                    <td>
                                        <a class="me-3 btn-edit" href="#" data-config='<?php echo json_encode($config); ?>'>
                                            <img src="assets/img/icons/edit.svg" alt="img">
                                        </a>
                                        <form method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this configuration?');">
                                            <input type="hidden" name="config_id" value="<?php echo $config['id']; ?>">
                                            <button type="submit" name="delete_config" class="btn p-0 border-0 bg-transparent">
                                                <img src="assets/img/icons/delete.svg" alt="img">
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>

<!-- Add Configuration Modal -->
<div class="modal fade" id="addConfigModal" tabindex="-1" aria-labelledby="addConfigModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addConfigModalLabel"><i class="fas fa-plus-circle me-2"></i>Create Email Configuration</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <div class="form-group">
                        <label>Configuration Name *</label>
                        <input type="text" class="form-control" name="config_name" id="add_config_name" placeholder="e.g., HR Team, IT Support, Management" required>
                        <small class="form-text text-muted">Unique name for this email group</small>
                    </div>

                    <div class="form-group">
                        <label>Email Addresses *</label>
                        <textarea class="form-control" name="email_addresses" id="add_email_addresses" rows="4" placeholder="Enter email addresses, one per line" required></textarea>
                        <small class="form-text text-muted">One email per line</small>
                    </div>

                    <div class="form-group">
                        <label>Description</label>
                        <textarea class="form-control" name="description" id="add_description" rows="3" placeholder="Optional description for this email group"></textarea>
                        <small class="form-text text-muted">Optional description</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="add_config" class="btn btn-submit">
                        <i class="fas fa-save"></i> Create Configuration
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Configuration Modal -->
<div class="modal fade" id="editConfigModal" tabindex="-1" aria-labelledby="editConfigModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editConfigModalLabel">Edit Email Configuration</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="config_id" id="edit_config_id">

                    <div class="form-group">
                        <label>Configuration Name *</label>
                        <input type="text" class="form-control" name="config_name" id="edit_config_name" required>
                    </div>

                    <div class="form-group">
                        <label>Email Addresses *</label>
                        <textarea class="form-control" name="email_addresses" id="edit_email_addresses" rows="4" required></textarea>
                        <small class="form-text text-muted">One email per line</small>
                    </div>

                    <div class="form-group">
                        <label>Description</label>
                        <textarea class="form-control" name="description" id="edit_description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="update_config" class="btn btn-primary">
                        <i class="fas fa-save"></i> Update Configuration
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Scripts -->
<script src="assets/js/jquery-3.6.0.min.js"></script>
<script src="assets/js/feather.min.js"></script>
<script src="assets/js/jquery.slimscroll.min.js"></script>
<script src="assets/js/jquery.dataTables.min.js"></script>
<script src="assets/js/dataTables.bootstrap4.min.js"></script>
<script src="assets/js/bootstrap.bundle.min.js"></script>
<script src="assets/plugins/select2/js/select2.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalert2.all.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalerts.min.js"></script>

<script>
    // Handle add email button click
    document.getElementById('addEmailBtn').addEventListener('click', function() {
        // Clear the form fields
        document.getElementById('add_config_name').value = '';
        document.getElementById('add_email_addresses').value = '';
        document.getElementById('add_description').value = '';

        const modal = new bootstrap.Modal(document.getElementById('addConfigModal'));
        modal.show();
    });

    // Handle edit button clicks
    document.querySelectorAll('.btn-edit').forEach(button => {
        button.addEventListener('click', function() {
            const config = JSON.parse(this.getAttribute('data-config'));

            document.getElementById('edit_config_id').value = config.id;
            document.getElementById('edit_config_name').value = config.config_name;
            document.getElementById('edit_email_addresses').value = config.email_addresses;
            document.getElementById('edit_description').value = config.description || '';

            const modal = new bootstrap.Modal(document.getElementById('editConfigModal'));
            modal.show();
        });
    });
</script>

<script src="assets/js/script.js"></script>

</body>
</html>
