<?php
/**
 * Email Scheduler - Daily scheduling script
 * This script runs continuously and checks daily at configured times
 * Run this script: php email_scheduler.php
 * Keep it running in the background
 *
 * Features:
 * - Runs daily at exact configured schedule times
 * - Precise time matching with 1-minute tolerance
 * - Sends emails once per day at scheduled time
 */

// Set timezone
date_default_timezone_set('Asia/Kuala_Lumpur');

// Prevent script timeout
set_time_limit(0);
ini_set('memory_limit', '256M');

// Create logs directory
if (!file_exists(__DIR__ . '/logs')) {
    mkdir(__DIR__ . '/logs', 0755, true);
}

// Log function
function log_scheduler($message) {
    $timestamp = date('Y-m-d H:i:s');
    $log_entry = "[$timestamp] $message" . PHP_EOL;
    file_put_contents(__DIR__ . '/logs/email_scheduler.log', $log_entry, FILE_APPEND | LOCK_EX);
    echo $log_entry;
}

// Function to check if script should stop
function should_stop() {
    // Check for stop file
    if (file_exists(__DIR__ . '/stop_scheduler.txt')) {
        return true;
    }
    return false;
}

// Function to create PID file
function create_pid_file() {
    $pid = getmypid();
    file_put_contents(__DIR__ . '/scheduler.pid', $pid);
    log_scheduler("Scheduler started with PID: $pid");
}

// Function to remove PID file
function remove_pid_file() {
    if (file_exists(__DIR__ . '/scheduler.pid')) {
        unlink(__DIR__ . '/scheduler.pid');
    }
}

// Determine which schedules should run at the current time (returns array of types)
function get_schedules_to_run($conn) {
    $types = [];

    if (!$conn) {
        log_scheduler("Database connection failed in get_schedules_to_run()");
        return $types;
    }

    $current_time = date('H:i');
    $tolerance_minutes = 1; // 1-minute tolerance window for precise daily scheduling

    // Check onboarding cron configuration
    $onboarding_query = "SELECT cron_time FROM `onboarding_cron_config` WHERE `is_enabled` = 1 LIMIT 1";
    $onboarding_result = mysqli_query($conn, $onboarding_query);

    if ($onboarding_result && mysqli_num_rows($onboarding_result) > 0) {
        $onboarding_config = mysqli_fetch_assoc($onboarding_result);
        $onboarding_time = $onboarding_config['cron_time'] ?? '09:00';

        if (is_time_within_tolerance($current_time, $onboarding_time, $tolerance_minutes, true)) {
            log_scheduler("Current time ($current_time) matches onboarding schedule ($onboarding_time)");
            $types[] = 'onboarding';
        }
    }

    // Check IT support configuration
    $it_query = "SELECT cron_time FROM `onboarding_email_config` WHERE `config_type` = 'it_support' AND `cron_time` IS NOT NULL LIMIT 1";
    $it_result = mysqli_query($conn, $it_query);

    if ($it_result && mysqli_num_rows($it_result) > 0) {
        $it_config = mysqli_fetch_assoc($it_result);
        $it_time = substr($it_config['cron_time'], 0, 5); // Get HH:MM format

        if (is_time_within_tolerance($current_time, $it_time, $tolerance_minutes, true)) {
            log_scheduler("Current time ($current_time) matches IT support schedule ($it_time)");
            $types[] = 'it_support';
        }
    }

    return $types;
}

// Backward-compatible boolean check (unused by main loop after refactor)
function should_run_at_current_time($conn) {
    return !empty(get_schedules_to_run($conn));
}

// Function to check if current time is within tolerance of target time
function is_time_within_tolerance($current_time, $target_time, $tolerance_minutes, $no_early = false) {
    $current_timestamp = strtotime($current_time);
    $target_timestamp = strtotime($target_time);
    $tolerance_seconds = $tolerance_minutes * 60;

    // Do not send early: require current time >= target time when $no_early=true
    if ($no_early && $current_timestamp < $target_timestamp) {
        return false;
    }

    $time_diff = abs($current_timestamp - $target_timestamp);

    return $time_diff <= $tolerance_seconds;
}

// Function to check if scheduler is already running
function is_scheduler_running() {
    if (!file_exists(__DIR__ . '/scheduler.pid')) {
        return false;
    }
    
    $pid = trim(file_get_contents(__DIR__ . '/scheduler.pid'));
    
    // On Windows, check if process exists
    if (PHP_OS_FAMILY === 'Windows') {
        $output = shell_exec("tasklist /FI \"PID eq $pid\" 2>NUL");
        return strpos($output, $pid) !== false;
    } else {
        // On Unix-like systems
        return file_exists("/proc/$pid");
    }
}

// Signal handlers for graceful shutdown
if (function_exists('pcntl_signal')) {
    pcntl_signal(SIGTERM, function() {
        log_scheduler("Received SIGTERM, shutting down gracefully...");
        remove_pid_file();
        exit(0);
    });
    
    pcntl_signal(SIGINT, function() {
        log_scheduler("Received SIGINT, shutting down gracefully...");
        remove_pid_file();
        exit(0);
    });
}

// Check if already running
if (is_scheduler_running()) {
    log_scheduler("Email scheduler is already running. Exiting.");
    exit(1);
}

// Create PID file
create_pid_file();

log_scheduler("Email Scheduler Started - Daily Mode");
log_scheduler("To stop the scheduler, create a file named 'stop_scheduler.txt' in the same directory");

// Establish database connection
if (php_sapi_name() === 'cli') {
    $old_error_reporting = error_reporting(E_ALL & ~E_WARNING);
    ob_start();
    require_once __DIR__ . '/database.php';
    ob_end_clean();
    error_reporting($old_error_reporting);
} else {
    require_once __DIR__ . '/database.php';
}

if (!$conn) {
    log_scheduler("Failed to establish database connection. Exiting.");
    exit(1);
}

try {
    while (true) {
        // Check if we should stop
        if (should_stop()) {
            log_scheduler("Stop signal received. Shutting down...");
            break;
        }

        // Process signals if available
        if (function_exists('pcntl_signal_dispatch')) {
            pcntl_signal_dispatch();
        }

        $current_date = date('Y-m-d');
        $current_time = date('H:i');
        $current_datetime = date('Y-m-d H:i');

        log_scheduler("Checking scheduled times for $current_date at $current_time");

        // Determine which schedules should run now
        $run_types = get_schedules_to_run($conn);
        if (!empty($run_types)) {
            log_scheduler("Running email tasks (" . implode(', ', $run_types) . ") at scheduled time: $current_datetime");

                try {
                    // Pass the types to the runner via a global variable
                    $GLOBALS['RUN_TYPES'] = $run_types;

                    // Include and run the auto email runner
                    ob_start();
                    include __DIR__ . '/auto_email_runner.php';
                    $output = ob_get_clean();

                    if (trim($output)) {
                        log_scheduler("Email Runner Output: " . trim($output));
                    }

                } catch (Exception $e) {
                    log_scheduler("Error running email tasks: " . $e->getMessage());
                } catch (Error $e) {
                    log_scheduler("Fatal error running email tasks: " . $e->getMessage());
                }
        } else {
            log_scheduler("Current time does not match any configured schedule. Waiting...");
        }

        // Sleep for 1 minute before checking again (daily precision)
        sleep(60);
    }
    
} catch (Exception $e) {
    log_scheduler("Scheduler error: " . $e->getMessage());
} finally {
    remove_pid_file();
    log_scheduler("Email Scheduler Stopped");
}

?>
