<?php
// get-product-details.php
header('Content-Type: application/json');

$response = [];

if (isset($_GET['inventory_id']) && !empty($_GET['inventory_id'])) {
    $inventory_id = $_GET['inventory_id'];

    // Connect to the database
    $con = mysqli_connect('localhost', 'root', '', 'cll-inventory');

    if (!$con) {
        $response['error'] = 'Database connection error';
    } else {
        // Prepare the SQL statement
        $sql = "SELECT * FROM `inventory-list` WHERE `inventory-id` = '$inventory_id'";

        // Execute the SQL statement
        $result = mysqli_query($con, $sql);

        if (!$result) {
            $response['error'] = 'Query execution error';
        } else {
            // Check if a product was found
            if (mysqli_num_rows($result) == 1) {
                $product_details = mysqli_fetch_assoc($result);

                // Get brand name
                $brand_id = $product_details['brand-id'];
                $brand_result = mysqli_query($con, "SELECT `brand-name` FROM `brand-list` WHERE `brand-id` = '$brand_id'");
                $brand_row = mysqli_fetch_assoc($brand_result);
                $product_details['brand_name'] = $brand_row['brand-name'];

                // Get category name
                $category_id = $product_details['category-id'];
                $category_result = mysqli_query($con, "SELECT `category-name` FROM `category-list` WHERE `category-id` = '$category_id'");
                $category_row = mysqli_fetch_assoc($category_result);
                $product_details['category_name'] = $category_row['category-name'];

                // Set the response data
                $response['product_details'] = $product_details;
            } else {
                $response['error'] = 'Product not found';
            }
        }

        // Close the database connection
        mysqli_close($con);
    }
} else {
    $response['error'] = 'Invalid request';
}

echo json_encode($response);
?>
