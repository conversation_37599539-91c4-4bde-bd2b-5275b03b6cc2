<?php
require("database.php");

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isset($_SESSION["user"])) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Unauthorized access']);
    exit;
}

// Check if department_id is provided
if (!isset($_POST['department_id'])) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Department ID is required']);
    exit;
}

$department_id = $_POST['department_id'];

// Prepare and execute query to get positions for the selected department
$sql = "SELECT position_id, position_name FROM position_list WHERE department_id = ? ORDER BY position_name";
$stmt = mysqli_prepare($conn, $sql);
mysqli_stmt_bind_param($stmt, "s", $department_id);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

$positions = [];
while ($row = mysqli_fetch_assoc($result)) {
    $positions[] = [
        'position_id' => $row['position_id'],
        'position_name' => $row['position_name']
    ];
}

// Return positions as JSON
header('Content-Type: application/json');
echo json_encode($positions);

mysqli_stmt_close($stmt);
?> 