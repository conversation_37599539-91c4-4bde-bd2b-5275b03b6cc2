<?php
require("database.php");
if(isset($_SESSION["user"])){
    $email=$_SESSION["user"];
?>
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0">
<meta name="description" content="CLL Warehouse Inventory System">
<meta name="author" content="Powered by CLL Systems Sdn Bhd">
<meta name="robots" content="noindex, nofollow">
<title>CLLXWARE-Group Permission</title>

<link rel='icon' type='image/x-icon' href='https://www.cllsystems.com/wp-content/themes/cllsystems/img/favicon.png'>

<link rel="stylesheet" href="assets/css/bootstrap.min.css">

<link rel="stylesheet" href="assets/css/animate.css">

<link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">

<link rel="stylesheet" href="assets/css/dataTables.bootstrap4.min.css">

<link rel="stylesheet" href="assets/plugins/fontawesome/css/fontawesome.min.css">
<link rel="stylesheet" href="assets/plugins/fontawesome/css/all.min.css">

<link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
<div id="global-loader">
<div class="whirly-loader"> </div>
</div>

<div class="main-wrapper">
<?php 
$role_id = $_SESSION["role_id"];
$sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
$result20 = mysqli_query($conn, $sql20);

if ($result20) {
    $row20 = mysqli_fetch_assoc($result20);
    if ($row20) {
       $view = $row20['group_view'];
       $all = $row20['group_all'];
       if($view != '1' and $all !='1'){
        header("location: ./index.php");
       }
    } else {
        echo "<script>alert('Role data not found')</script>";
    }
} else {
    echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
}
include("header.php");?>
</div>

<div class="page-wrapper">
<div class="content">
<div class="page-header">
<div class="page-title">
<h4>Group Permissions</h4>
<h6>Manage Group Permissions</h6>
</div>
<?php
$role_id = $_SESSION["role_id"];
$sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
$result20 = mysqli_query($conn, $sql20);

if ($result20) {
    $row20 = mysqli_fetch_assoc($result20);
    if ($row20) {
       $create = $row20['group_create'];
       $all = $row20['group_all'];
       if($create != '1' and $all !='1'){
       echo"";
       }else{
        echo"
        <div class='page-btn'>
        <a class='btn btn-added' href='createpermission.php'><img src='assets/img/icons/plus.svg' alt='img' class='me-1'>Add Group Permission</a>
        </div>
        ";
       }
    } else {
        echo "<script>alert('Role data not found')</script>";
    }
} else {
    echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
}

?>

</div>

<div class="card">
<div class="card-body">
<div class="table-top">
<div class="search-set">
<div class="search-input">
<a class="btn btn-searchset"><img src="assets/img/icons/search-white.svg" alt="img"></a>
</div>
</div>
</div>
<div class="table-responsive">
<table class="table  datanew">
<thead>
<tr>
<th>
No
</th>
<th>Role</th>
<th>Description</th>
<th>Status</th>
<?php 
            $role_id = $_SESSION["role_id"];
            $sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
            $result20 = mysqli_query($conn, $sql20);
            
            if ($result20) {
                $row20 = mysqli_fetch_assoc($result20);
                if ($row20) {
                    $delete = $row20['group_delete'];
                    $all = $row20['group_all'];
                    if($delete != '1' and $all !='1'){
                    echo"";
                   }else{
                   echo"<th>Hide</th>";
                   }
                } else {
                    echo "<script>alert('Role data not found')</script>";
                }
            } else {
                echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
            }?>
<?php 
            $role_id = $_SESSION["role_id"];
            $sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
            $result20 = mysqli_query($conn, $sql20);
            
            if ($result20) {
                $row20 = mysqli_fetch_assoc($result20);
                if ($row20) {
                    $edit = $row20['group_edit'];
                    $all = $row20['group_all'];
                    if($edit != '1' and $all !='1'){
                    echo"";
                   }else{
                    echo"
                    <th class='text-end'>Action</th>
                    ";
                   }
                } else {
                    echo "<script>alert('Role data not found')</script>";
                }
            } else {
                echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
            }?>
</tr>
</thead>
<tbody>
<?php
$sql = "SELECT * FROM `role-list`";
$result = mysqli_query($conn, $sql);


if ($result && mysqli_num_rows($result) > 0) {
    $counter = 1;
    while ($row = mysqli_fetch_assoc($result)) {
        echo "
<tr>
<td>$counter</td>
<td>" . $row['role'] . "</td>
<td>" . $row['role_description'] . "</td>";
$hide=$row['hide'];
if($hide=='0'){
echo"<td>
<span class='badges bg-lightgreen'>Active</span>
</td>";
}else{
    echo"
    <td>
<span class='badges bg-lightred'>Hide</span>
</td>
    ";
}
?>
 <?php 
            $role_id = $_SESSION["role_id"];
            $sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
            $result20 = mysqli_query($conn, $sql20);
            
            if ($result20) {
                $row20 = mysqli_fetch_assoc($result20);
                if ($row20) {
                    $delete = $row20['group_delete'];
                    $all = $row20['group_all'];
                    if($delete != '1' and $all !='1'){
                    echo"";
                   }else{
                    ?>
                    <td>
                        <div class='status-toggle d-flex justify-content-between align-items-center'>
                            <input type='checkbox' id='role_<?php echo $row['role-id']; ?>' class='check' data-roleid='<?php echo $row['role-id']; ?>' <?php echo ($row['hide'] == 1) ? 'checked' : ''; ?>>
                            <label for='role_<?php echo $row['role-id']; ?>' class='checktoggle'><?php echo ($row['hide'] == 1) ? 'Uncheck' : 'Check'; ?></label>
                        </div>
                    </td>
                    <?php
                   }
                } else {
                    echo "<script>alert('Role data not found')</script>";
                }
            } else {
                echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
            }?>

<?php 
            $role_id = $_SESSION["role_id"];
            $sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
            $result20 = mysqli_query($conn, $sql20);
            
            if ($result20) {
                $row20 = mysqli_fetch_assoc($result20);
                if ($row20) {
                    $edit = $row20['group_edit'];
                    $all = $row20['group_all'];
                    if($edit != '1' and $all !='1'){
                    echo"";
                   }else{
                    echo"
                    <td class='text-end'>
                    <a class='me-3' href='editpermission.php?role-id=" . $row['role-id'] . "'>
                    <img src='assets/img/icons/edit.svg' alt='img'>
                    </a>
                    </td>
                    ";
                   }
                } else {
                    echo "<script>alert('Role data not found')</script>";
                }
            } else {
                echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
            }?>
<?php
echo"
</tr>
";
$counter++;
    }
}
?>
</tbody>
</table>
</div>
</div>
</div>

</div>
</div>
</div>

<script>
    // Add event listener for checkbox change
    document.querySelectorAll('.check').forEach(item => {
        item.addEventListener('change', function() {
            let role_id = this.dataset.roleid; // Get role_id from data attribute
            let hide = this.checked ? 1 : 0; // Determine the hide value based on checkbox state
            updateHide(role_id, hide); // Call the updateHide function
        });
    });

    // Function to update hide status via AJAX
    function updateHide(role_id, hide) {
        // AJAX request
        let xhr = new XMLHttpRequest();
        xhr.open('POST', 'update_hide.php', true);
        xhr.setRequestHeader('Content-type', 'application/x-www-form-urlencoded');
        xhr.onload = function() {
            if (xhr.status === 200) {
                // Success, reload the page
                location.reload();
            } else {
                // Error handling
                console.error(xhr.responseText);
            }
        };
        xhr.send('role_id=' + role_id + '&hide=' + hide); // Send POST data
    }
</script>

<script data-cfasync="false" src="../../cdn-cgi/scripts/5c5dd728/cloudflare-static/email-decode.min.js"></script><script src="assets/js/jquery-3.6.0.min.js"></script>
<script src="assets/js/moment.min.js"></script>
<script src="assets/js/jquery-3.6.0.min.js"></script>

<script src="assets/js/feather.min.js"></script>

<script src="assets/js/jquery.slimscroll.min.js"></script>

<script src="assets/js/jquery.dataTables.min.js"></script>
<script src="assets/js/dataTables.bootstrap4.min.js"></script>

<script src="assets/js/bootstrap.bundle.min.js"></script>

<script src="assets/plugins/select2/js/select2.min.js"></script>

<script src="assets/plugins/sweetalert/sweetalert2.all.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalerts.min.js"></script>

<script src="assets/js/script.js"></script>
</body>
</html>
<?php
}else{
    header("location: ./signin.php");
}
?>