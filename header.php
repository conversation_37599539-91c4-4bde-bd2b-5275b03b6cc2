<div class="header">

<div class="header-left active">
<a href="index.php" class="logo">
<img src="assets/img/logo2.png" alt="">
</a>
<a href="index.php" class="logo-small">
<img src="https://www.cllsystems.com/wp-content/themes/cllsystems/img/favicon.png" alt="">
</a>

</div>

<a id="mobile_btn" class="mobile_btn" href="#sidebar">
<span class="bar-icon">
<span></span>
<span></span>
<span></span>
</span>
</a>
<?php
$sql = "SELECT * FROM `notification` WHERE `read_msg` = '0' ORDER BY `id` DESC LIMIT 5";
$result = mysqli_query($conn, $sql);
echo"
<ul class='nav user-menu'>


<li class='nav-item dropdown'>
<a href='javascript:void(0);' class='dropdown-toggle nav-link' data-bs-toggle='dropdown'>
<img src='assets/img/icons/notification-bing.svg' alt='img'>";
$sql4 = "SELECT COUNT(*) AS total_rows_notification FROM `notification` WHERE `read_msg` ='0'";
$result4 = mysqli_query($conn, $sql4);
$row4 = mysqli_fetch_assoc($result4);
if (isset($row4["total_rows_notification"])) {
    $totalrow=$row4["total_rows_notification"];
    if($totalrow!='0'){
        $role_id = $_SESSION["role_id"];
        $sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
        $result20 = mysqli_query($conn, $sql20);

        if ($result20) {
            $row20 = mysqli_fetch_assoc($result20);
            if ($row20) {
            $approval = $row20['request_approval'];
            $all = $row20['request_all'];
            if($approval == '1' or $all =='1'){
                if($totalrow>'5'){
                    $newtotalrow='5';
                    echo"<span class='badge rounded-pill'>$newtotalrow</span>";
                }else{
                    echo"<span class='badge rounded-pill'>" . $row4['total_rows_notification'] . "</span>";
                }
            }
            } else {
                echo "";
            }
        } else {
            echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
        }

}else{
    echo"";
}
}
echo" 
</a>
<div class='dropdown-menu notifications'>
<div class='topnav-dropdown-header'>
<span class='notification-title'>Notifications</span>";

$role_id = $_SESSION["role_id"];
$sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
$result20 = mysqli_query($conn, $sql20);

if ($result20) {
    $row20 = mysqli_fetch_assoc($result20);
    if ($row20) {
       $approval = $row20['request_approval'];
       $all = $row20['request_all'];
       if($approval == '1' or $all =='1'){
        echo"<a href='javascript:void(0)' class='clear-noti' id='mark-as-read'> Mark as read </a>";
       }
    } else {
        echo "";
    }
} else {
    echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
}

echo"
</div>
<div class='noti-content'>
<ul class='notification-list'>";
if ($result && mysqli_num_rows($result) > 0) {
    $role_id = $_SESSION["role_id"];
    $sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
    $result20 = mysqli_query($conn, $sql20);

    if ($result20) {
        $row20 = mysqli_fetch_assoc($result20);
        if ($row20) {
        $approval = $row20['request_approval'];
        $all = $row20['request_all'];
        if($approval == '1' or $all =='1'){
            while ($row = mysqli_fetch_assoc($result)) {
                $acc_id = $row['acc_id'];
                $sql3 = "SELECT * FROM `user-list` WHERE `acc_id` = '$acc_id'";
                $result3 = mysqli_query($conn, $sql3);
                if ($result3 && mysqli_num_rows($result3) > 0) {
                    $row3 = mysqli_fetch_assoc($result3);
                } else {
                    // Handle error if query fails or returns no rows
                    echo "Error fetching user data: " . mysqli_error($conn);
                    continue; // Skip to next iteration of loop
                }
        echo"
        <li class='notification-message'>
        <a href='request-details.php?request-id=" . $row['request_id'] . "'>
        <div class='media d-flex'>
        <span class='avatar flex-shrink-0'>
        <img alt='' src='assets/img/profiles/profile_pic.png'>
        </span>
        <div class='media-body flex-grow-1'>
        <p class='noti-details'><span class='noti-title'>" . $row3['name'] . "</span> raised a request <span class='noti-title'>Request ID: " . $row['request_id'] . "</span></p>
        <p class='noti-time'><span class='notification-time'>" . $row['timestamp'] . "</span></p>
        </div>
        </div>
        </a>
        </li>";
        }
        }
        } else {
            echo "";
        }
    } else {
        echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
    }
} else {
    echo "<div style='text-align: center;'>No notification found.</div>";
    }
echo"

</ul>";?>
</div>
<div class="topnav-dropdown-footer">
</div>
</div>
</li>

<li class="nav-item dropdown has-arrow main-drop">
    <a href="javascript:void(0);" class="dropdown-toggle nav-link userset" data-bs-toggle="dropdown">
        <span class="user-img">
            <?php
            $acc_id = $_SESSION["acc_id"];
            $role_id = $_SESSION["role_id"];

            $sql = "SELECT ul.name, ul.profile_image, rl.role
                    FROM `user-list` AS ul
                    JOIN `role-list` AS rl ON ul.acc_id = '$acc_id' AND rl.`role-id` = '$role_id'";

            $result = mysqli_query($conn, $sql);

            if ($result) {
                $row = mysqli_fetch_assoc($result);
                if ($row) {
                    // Set the profile image or default image
                    $profile_image = !empty($row['profile_image']) ? $row['profile_image'] : 'assets/img/profiles/profile_pic.png';
                    echo '<img src="' . $profile_image . '" alt="">';
                } else {
                    echo '<img src="assets/img/profiles/profile_pic.png" alt="">';
                }
            } else {
                echo '<img src="assets/img/profiles/profile_pic.png" alt="">';
            }
            ?>
            <span class="status online"></span>
        </span>
    </a>
    <div class="dropdown-menu menu-drop-user">
        <div class="profilename">
            <div class="profileset">
                <span class="user-img">
                    <img src="<?php echo $profile_image; ?>" alt="">
                    <span class="status online"></span>
                </span>
                <div class="profilesets">
                    <?php
                    if ($row) {
                        echo "
                        <h6>" . $row['name'] . "</h6>
                        <h5>" . $row['role'] . "</h5>";
                    } else {
                        echo "No matching records found.";
                    }
                    ?>
                </div>
            </div>

<hr class="m-0">
<a class="dropdown-item" href="profile.php"> <i class="me-2" data-feather="user"></i> My Profile</a>
<hr class="m-0">
<a class="dropdown-item logout pb-0" href="logout.php"><img src="assets/img/icons/log-out.svg" class="me-2" alt="img">Logout</a>
</div>
</div>
</li>
</ul>


<div class="dropdown mobile-user-menu">
<a href="javascript:void(0);" class="nav-link dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false"><i class="fa fa-ellipsis-v"></i></a>
<div class="dropdown-menu dropdown-menu-right">
<a class="dropdown-item" href="profile.php">My Profile</a>
<a class="dropdown-item" href="logout.php">Logout</a>
</div>
</div>

</div>


<div class="sidebar" id="sidebar">
<div class="sidebar-inner slimscroll">
<div id="sidebar-menu" class="sidebar-menu">
<ul>
<li class="active">
<a href="index.php"><img src="assets/img/icons/dashboard.svg" alt="img"><span> Dashboard</span> </a>
</li>

<?php
        $role_id = $_SESSION["role_id"];
        $sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
        $result20 = mysqli_query($conn, $sql20);

        if ($result20) {
            $row20 = mysqli_fetch_assoc($result20);
            if ($row20) {
                $inventory_view = $row20['inventory_view'];
                $inventory_create = $row20['inventory_create'];
                $inventory_all = $row20['inventory_all'];
                $import_create = $row20['import_create'];
                $category_view = $row20['category_view'];
                $category_create = $row20['category_create'];
                $category_all = $row20['category_all'];
                $brand_view = $row20['brand_view'];
                $brand_create = $row20['brand_create'];
                $brand_all= $row20['brand_all'];
                $location_view = $row20['location_view'];
                $location_create = $row20['location_create'];
                $location_all = $row20['location_all'];
                if($inventory_view == '1' or $inventory_create =='1' or $inventory_all == '1' or $import_create =='1' or $category_view =='1' or $category_create == '1' or $category_all =='1' or $brand_view =='1' or $brand_create == '1' or $brand_all =='1' or $location_view =='1' or $location_create == '1' or $location_all =='1'){
            ?>
                <li class="submenu">
                <a href="javascript:void(0);"><img src="assets/img/icons/product.svg" alt="img"><span> Inventory</span> <span class="menu-arrow"></span></a>
                <ul>
                <?php
                $role_id = $_SESSION["role_id"];
                $sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
                $result20 = mysqli_query($conn, $sql20);

                if ($result20) {
                    $row20 = mysqli_fetch_assoc($result20);
                    if ($row20) {
                        $view = $row20['inventory_view'];
                        $all = $row20['inventory_all'];
                        if($view != '1' and $all !='1'){
                    echo"";
                    }else{
                        echo"<li><a href='inventorylist.php'>Inventory List</a></li>";
                    }
                    } else {
                        echo "<script>alert('Role data not found')</script>";
                    }
                } else {
                    echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
                }
                ?>
                <?php
                $role_id = $_SESSION["role_id"];
                $sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
                $result20 = mysqli_query($conn, $sql20);

                if ($result20) {
                    $row20 = mysqli_fetch_assoc($result20);
                    if ($row20) {
                        $create = $row20['inventory_create'];
                    $all = $row20['inventory_all'];
                    if($create != '1' and $all !='1'){
                    echo"";
                    }else{
                        echo"<li><a href='addinventory.php'>Add Inventory</a></li>";
                    }
                    } else {
                        echo "<script>alert('Role data not found')</script>";
                    }
                } else {
                    echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
                }
                ?>
                <?php
                $role_id = $_SESSION["role_id"];
                $sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
                $result20 = mysqli_query($conn, $sql20);

                if ($result20) {
                    $row20 = mysqli_fetch_assoc($result20);
                    if ($row20) {
                    $import = $row20['import_create'];
                    if($import != '1'){
                    echo"";
                    }else{
                        echo"<li><a href='importproduct.php'>Import Products</a></li>";
                    }
                    } else {
                        echo "<script>alert('Role data not found')</script>";
                    }
                } else {
                    echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
                }
                ?>
                <?php
                $role_id = $_SESSION["role_id"];
                $sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
                $result20 = mysqli_query($conn, $sql20);

                if ($result20) {
                    $row20 = mysqli_fetch_assoc($result20);
                    if ($row20) {
                        $view = $row20['category_view'];
                    $all = $row20['category_all'];
                        if($view != '1' and $all !='1'){
                    echo"";
                    }else{
                        echo"<li><a href='categorylist.php'>Category List</a></li>";
                    }
                    } else {
                        echo "<script>alert('Role data not found')</script>";
                    }
                } else {
                    echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
                }
                ?>
                <?php
                $role_id = $_SESSION["role_id"];
                $sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
                $result20 = mysqli_query($conn, $sql20);

                if ($result20) {
                    $row20 = mysqli_fetch_assoc($result20);
                    if ($row20) {
                        $create = $row20['category_create'];
                        $all = $row20['category_all'];
                        if($create != '1' and $all !='1'){
                    echo"";
                    }else{
                        echo"<li><a href='addcategory.php'>Add Category</a></li>";
                    }
                    } else {
                        echo "<script>alert('Role data not found')</script>";
                    }
                } else {
                    echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
                }
                ?>
                <?php
                $role_id = $_SESSION["role_id"];
                $sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
                $result20 = mysqli_query($conn, $sql20);

                if ($result20) {
                    $row20 = mysqli_fetch_assoc($result20);
                    if ($row20) {
                        $view = $row20['brand_view'];
                    $all = $row20['brand_all'];
                        if($view != '1' and $all !='1'){
                    echo"";
                    }else{
                        echo"<li><a href='brandlist.php'>Brand List</a></li>";
                    }
                    } else {
                        echo "<script>alert('Role data not found')</script>";
                    }
                } else {
                    echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
                }
                ?>
                <?php
                $role_id = $_SESSION["role_id"];
                $sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
                $result20 = mysqli_query($conn, $sql20);

                if ($result20) {
                    $row20 = mysqli_fetch_assoc($result20);
                    if ($row20) {
                        $create = $row20['brand_create'];
                        $all = $row20['brand_all'];
                        if($create != '1' and $all !='1'){
                    echo"";
                    }else{
                        echo"<li><a href='addbrand.php'>Add Brand</a></li>";
                    }
                    } else {
                        echo "<script>alert('Role data not found')</script>";
                    }
                } else {
                    echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
                }
                ?>
                <?php
                $role_id = $_SESSION["role_id"];
                $sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
                $result20 = mysqli_query($conn, $sql20);

                if ($result20) {
                    $row20 = mysqli_fetch_assoc($result20);
                    if ($row20) {
                        $view = $row20['location_view'];
                    $all = $row20['location_all'];
                        if($view != '1' and $all !='1'){
                    echo"";
                    }else{
                        echo"<li><a href='location_list.php'>Location List</a></li>";
                    }
                    } else {
                        echo "<script>alert('Role data not found')</script>";
                    }
                } else {
                    echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
                }
                ?>
                <?php
                $role_id = $_SESSION["role_id"];
                $sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
                $result20 = mysqli_query($conn, $sql20);

                if ($result20) {
                    $row20 = mysqli_fetch_assoc($result20);
                    if ($row20) {
                        $create = $row20['location_create'];
                        $all = $row20['location_all'];
                        if($create != '1' and $all !='1'){
                    echo"";
                    }else{
                        echo"<li><a href='addlocation.php'>Add Location</a></li>";
                    }
                    } else {
                        echo "<script>alert('Role data not found')</script>";
                    }
                } else {
                    echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
                }
                ?>
                </ul>
                </li>
            <?php
            }else{
                echo"";
            }
            } else {
                echo "<script>alert('Role data not found')</script>";
            }
        } else {
            echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
        }
        ?>
        
<?php
$role_id = $_SESSION["role_id"];
$sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
$result20 = mysqli_query($conn, $sql20);

if ($result20) {
    $row20 = mysqli_fetch_assoc($result20);
    if ($row20) {
       $request_view = $row20['request_view'];
       $request_all = $row20['request_all'];
       $inventory_return_view = $row20['inventory_return_view'];
       $inventory_return_all = $row20['inventory_return_all'];
        if($request_view == '1' or $request_all =='1' or $inventory_return_view =='1' or $inventory_return_all =='1'){
      ?>
        <li class="submenu">
        <a href="javascript:void(0);"><img src="assets/img/icons/purchase1.svg" alt="img"><span>Request</span> <span class="menu-arrow"></span></a>
        <ul>
        <?php
        $role_id = $_SESSION["role_id"];
        $sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
        $result20 = mysqli_query($conn, $sql20);

        if ($result20) {
            $row20 = mysqli_fetch_assoc($result20);
            if ($row20) {
                $view = $row20['request_view'];
            $all = $row20['request_all'];
                if($view != '1' and $all !='1'){
            echo"";
            }else{
                echo"<li><a href='requestlist.php'>Request List</a></li>";
            }
            } else {
                echo "<script>alert('Role data not found')</script>";
            }
        } else {
            echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
        }
        ?>
        <?php
        $role_id = $_SESSION["role_id"];
        $sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
        $result20 = mysqli_query($conn, $sql20);

        if ($result20) {
            $row20 = mysqli_fetch_assoc($result20);
            if ($row20) {
                $create = $row20['inventory_return_view'];
                $all = $row20['inventory_return_all'];
                if($create != '1' and $all !='1'){
            echo"";
            }else{
                echo"<li><a href='inventoryreturnlists.php'>Inventory Return List</a></li>";
            }
            } else {
                echo "<script>alert('Role data not found')</script>";
            }
        } else {
            echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
        }
        ?>
        </ul>
        </li>
      <?php
       }else{
        echo"";
       }
    } else {
        echo "<script>alert('Role data not found')</script>";
    }
} else {
    echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
}
?>


<?php
$role_id = $_SESSION["role_id"];
$sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
$result20 = mysqli_query($conn, $sql20);

if ($result20) {
    $row20 = mysqli_fetch_assoc($result20);
    if ($row20) {
        $onboarding_view = $row20['onboarding_view'];
        $onboarding_all = $row20['onboarding_all'];
        $onboarding_create = $row20['onboarding_create'];
         if($onboarding_view == '1' or $onboarding_all =='1' or $onboarding_create =='1' ){      ?>
        <li class="submenu">
        <a href="javascript:void(0);"><img src="assets/img/icons/users1.svg" alt="img"><span>On-Boarding</span> <span class="menu-arrow"></span></a>
        <ul>
        <?php
        $role_id = $_SESSION["role_id"];
        $sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
        $result20 = mysqli_query($conn, $sql20);

        if ($result20) {
            $row20 = mysqli_fetch_assoc($result20);
            if ($row20) {
                $view = $row20['onboarding_view'];
                $all = $row20['onboarding_all'];
                if($view != '1' and $all !='1'){
            echo"";
            }else{
                echo"<li><a href='onboarding_list.php'>On-Boarding List</a></li>";
            }
            } else {
                echo "<script>alert('Role data not found')</script>";
            }
        } else {
            echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
        }
        ?>

        <?php
        $role_id = $_SESSION["role_id"];
        $sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
        $result20 = mysqli_query($conn, $sql20);

        if ($result20) {
            $row20 = mysqli_fetch_assoc($result20);
            if ($row20) {
                $create = $row20['onboarding_create'];
                $all = $row20['onboarding_all'];
                if($create != '1' and $all !='1'){
            echo"";
            }else{
                echo"<li><a href='add_onboarding.php'>Add On-Boarding</a></li>";
            }
            } else {
                echo "<script>alert('Role data not found')</script>";
            }
        } else {
            echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
        }

        // Add Email Format option for admin roles
        $role_id = $_SESSION["role_id"];
        $sql_admin = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
        $result_admin = mysqli_query($conn, $sql_admin);

        if ($result_admin) {
            $row_admin = mysqli_fetch_assoc($result_admin);
            if ($row_admin) {
                $all = $row_admin['onboarding_all'];
                if($all == '1'){  
                    echo"<li><a href='onboarding_email_config.php'>Email Format</a></li>";
                    echo"<li><a href='onboarding_cron_config.php'>Cron Job Setup</a></li>";
                    echo"<li><a href='email_config_management.php'>Email Configuration</a></li>";
                }
            }
        }
        ?>

        </ul>
        </li>
      <?php
       }else{
        echo"";
       }
    } else {
        echo "<script>alert('Role data not found')</script>";
    }
} else {
    echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
}
?>

<?php
$role_id = $_SESSION["role_id"];
$sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
$result20 = mysqli_query($conn, $sql20);

if ($result20) {
    $row20 = mysqli_fetch_assoc($result20);
    if ($row20) {
        $offboarding_view = $row20['offboarding_view'];
        $offboarding_all = $row20['offboarding_all'];
        $offboarding_create = $row20['offboarding_create'];
         if($offboarding_view == '1' or $offboarding_all =='1' or $offboarding_create =='1' ){      ?>
        <li class="submenu">
        <a href="javascript:void(0);"><img src="assets/img/icons/offboarding.png" alt="img"><span>Off-Boarding</span> <span class="menu-arrow"></span></a>
        <ul>
        <?php
        $role_id = $_SESSION["role_id"];
        $sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
        $result20 = mysqli_query($conn, $sql20);

        if ($result20) {
            $row20 = mysqli_fetch_assoc($result20);
            if ($row20) {
                $view = $row20['offboarding_view'];
                $all = $row20['offboarding_all'];
                if($view != '1' and $all !='1'){
            echo"";
            }else{
                echo"<li><a href='offboarding_list.php'>Off-Boarding List</a></li>";
            }
            } else {
                echo "<script>alert('Role data not found')</script>";
            }
        } else {
            echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
        }
        ?>

        <?php
        $role_id = $_SESSION["role_id"];
        $sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
        $result20 = mysqli_query($conn, $sql20);

        if ($result20) {
            $row20 = mysqli_fetch_assoc($result20);
            if ($row20) {
                $all = $row20['offboarding_all'];
                if($all !='1'){
            echo"";
            }else{
                echo"<li><a href='position_list.php'>Position List</a></li>";
            }
            } else {
                echo "<script>alert('Role data not found')</script>";
            }
        } else {
            echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
        }
        ?>

    <?php
        $role_id = $_SESSION["role_id"];
        $sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
        $result20 = mysqli_query($conn, $sql20);

        if ($result20) {
            $row20 = mysqli_fetch_assoc($result20);
            if ($row20) {
                $all = $row20['offboarding_all'];
                if($all !='1'){
            echo"";
            }else{
                echo"<li><a href='department_list.php'>Department List</a></li>";
            }
            } else {
                echo "<script>alert('Role data not found')</script>";
            }
        } else {
            echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
        }
        ?>

<?php
        $role_id = $_SESSION["role_id"];
        $sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
        $result20 = mysqli_query($conn, $sql20);

        if ($result20) {
            $row20 = mysqli_fetch_assoc($result20);
            if ($row20) {
                $all = $row20['offboarding_all'];
                if($all !='1'){
            echo"";
            }else{
                echo"<li><a href='checklist_list.php'>Checklist List</a></li>";
            }
            } else {
                echo "<script>alert('Role data not found')</script>";
            }
        } else {
            echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
        }
        ?>

        <?php
        $role_id = $_SESSION["role_id"];
        $sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
        $result20 = mysqli_query($conn, $sql20);

        if ($result20) {
            $row20 = mysqli_fetch_assoc($result20);
            if ($row20) {
                $all = $row20['offboarding_all'];
                if($all !='1'){
            echo"";
            }else{
                echo"<li><a href='manager_list.php'>Manager List</a></li>";
            }
            } else {
                echo "<script>alert('Role data not found')</script>";
            }
        } else {
            echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
        }
        ?>
         <?php
        $role_id = $_SESSION["role_id"];
        $sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
        $result20 = mysqli_query($conn, $sql20);

        if ($result20) {
            $row20 = mysqli_fetch_assoc($result20);
            if ($row20) {
                $all = $row20['offboarding_all'];
                if($all !='1'){
            echo"";
            }else{
                echo"<li><a href='person_in_charge_list.php'>Person In Charge List</a></li>";
            }
            } else {
                echo "<script>alert('Role data not found')</script>";
            }
        } else {
            echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
        }
        ?>
        </ul>
        </li>
      <?php
       }else{
        echo"";
       }
    } else {
        echo "<script>alert('Role data not found')</script>";
    }
} else {
    echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
}
?>

<?php
$role_id = $_SESSION["role_id"];
$sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
$result20 = mysqli_query($conn, $sql20);

if ($result20) {
    $row20 = mysqli_fetch_assoc($result20);
    if ($row20) {
        $user_view = $row20['user_view'];
        $user_create = $row20['user_create'];
        $user_all = $row20['user_all'];
        if($user_view == '1' or $user_create  =='1' or $user_all  =='1'){
        ?>
       <li class="submenu">
        <a href="javascript:void(0);"><img src="assets/img/icons/users1.svg" alt="img"><span> People</span> <span class="menu-arrow"></span></a>
        <ul>
        <?php
        $role_id = $_SESSION["role_id"];
        $sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
        $result20 = mysqli_query($conn, $sql20);

        if ($result20) {
            $row20 = mysqli_fetch_assoc($result20);
            if ($row20) {
                $create = $row20['user_view'];
                $all = $row20['user_all'];
                if($create != '1' and $all !='1'){
            echo"";
            }else{
                echo"<li><a href='userlist.php'>User List</a></li>";
            }
            } else {
                echo "<script>alert('Role data not found')</script>";
            }
        } else {
            echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
        }
        ?>
        <?php
        $role_id = $_SESSION["role_id"];
        $sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
        $result20 = mysqli_query($conn, $sql20);

        if ($result20) {
            $row20 = mysqli_fetch_assoc($result20);
            if ($row20) {
                $create = $row20['user_create'];
                $all = $row20['user_all'];
                if($create != '1' and $all !='1'){
            echo"";
            }else{
                echo"<li><a href='adduser.php'>Add User </a></li>";
            }
            } else {
                echo "<script>alert('Role data not found')</script>";
            }
        } else {
            echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
        }
        ?>
        </ul>
        </li>
       <?php
       }else{
        echo"";
       }
    } else {
        echo "<script>alert('Role data not found')</script>";
    }
} else {
    echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
}
?>


<?php
$role_id = $_SESSION["role_id"];
$sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
$result20 = mysqli_query($conn, $sql20);

if ($result20) {
    $row20 = mysqli_fetch_assoc($result20);
    if ($row20) {
        $user = $row20['user'];
        $hr = $row20['hr'];
        $finance = $row20['finance'];
        $it_support= $row20['it_support'];
        $anonymous = $row20['anonymous'];
        $section_all = $row20['section_all'];
        $ticket_all = $row20['ticket_all'];
        
        if ($user == '1' || $hr == '1' || $finance == '1' || $it_support == '1' || $anonymous == '1' || $section_all == '1' || $ticket_all == '1') {
            ?>
            <li class="submenu">
                <a href="javascript:void(0);"><img src="assets/img/form.png" alt="img"><span>Report An Issue</span> <span class="menu-arrow"></span></a>
                <ul>
                <?php
                $sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
                $result20 = mysqli_query($conn, $sql20);

                if ($result20) {
                    $row20 = mysqli_fetch_assoc($result20);
                    if ($row20) {
                        $view = $row20['user'] || $row20['hr'] || $row20['finance'] || $row20['it_support'] || $row20['anonymous'] || $row20['section_all'];
                        if ($view) {
                            echo "<li><a href='section_list_user.php'>My Requests</a></li>";
                        }
                    } else {
                        echo "<script>alert('Role data not found')</script>";
                    }
                } else {
                    echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
                }



                $sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
                $result20 = mysqli_query($conn, $sql20);

                if ($result20) {
                    $row20 = mysqli_fetch_assoc($result20);
                    if ($row20) {
                        $view = $row20['hr'] || $row20['finance'] || $row20['it_support'] || $row20['anonymous'] || $row20['section_all'];
                        if ($view) {
                            echo "<li><a href='section_list_admin.php'>Requests List</a></li>";
                        }
                    } else {
                        echo "<script>alert('Role data not found')</script>";
                    }
                } else {
                    echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
                }




                $sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
                $result20 = mysqli_query($conn, $sql20);

                if ($result20) {
                    $row20 = mysqli_fetch_assoc($result20);
                    if ($row20) {
                        $create = $row20['user'] || $row20['hr'] || $row20['finance'] || $row20['it_support'] || $row20['anonymous'] || $row20['section_all'];
                        if ($create) {
                            echo "<li><a href='add_section.php'>Submit Request</a></li>";
                        }
                    } else {
                        echo "<script>alert('Role data not found')</script>";
                    }
                } else {
                    echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
                }

                $sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
                $result20 = mysqli_query($conn, $sql20);

                if ($result20) {
                    $row20 = mysqli_fetch_assoc($result20);
                    if ($row20) {
                        $view = $row20['hr'] || $row20['section_all'];
                        if ($view) {
                            echo "<li><a href='hrinq_list.php'> HR - Inquiry Category</a></li>";
                        }
                    } else {
                        echo "<script>alert('Role data not found')</script>";
                    }
                } else {
                    echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
                }

                $sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
                $result20 = mysqli_query($conn, $sql20);

                if ($result20) {
                    $row20 = mysqli_fetch_assoc($result20);
                    if ($row20) {
                        $view = $row20['hr'] || $row20['section_all'];
                        if ($view) {
                            echo "<li><a href='office_list.php'>Office List</a></li>";
                        }
                    } else {
                        echo "<script>alert('Role data not found')</script>";
                    }
                } else {
                    echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
                }

                $sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
                $result20 = mysqli_query($conn, $sql20);

                if ($result20) {
                    $row20 = mysqli_fetch_assoc($result20);
                    if ($row20) {
                        $view = $row20['hr'] || $row20['section_all'];
                        if ($view) {
                            echo "<li><a href='office_area_list.php'>Office Area List</a></li>";
                        }
                    } else {
                        echo "<script>alert('Role data not found')</script>";
                    }
                } else {
                    echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
                }

                $sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
                $result20 = mysqli_query($conn, $sql20);

                if ($result20) {
                    $row20 = mysqli_fetch_assoc($result20);
                    if ($row20) {
                        $view = $row20['hr'] || $row20['section_all'];
                        if ($view) {
                            echo "<li><a href='office_category_list.php'>Office Category List</a></li>";
                        }
                    } else {
                        echo "<script>alert('Role data not found')</script>";
                    }
                } else {
                    echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
                }

                $sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
                $result20 = mysqli_query($conn, $sql20);

                if ($result20) {
                    $row20 = mysqli_fetch_assoc($result20);
                    if ($row20) {
                        $view = $row20['hr'] || $row20['section_all'];
                        if ($view) {
                            echo "<li><a href='fault_category_list.php'>Fault Category List</a></li>";
                        }
                    } else {
                        echo "<script>alert('Role data not found')</script>";
                    }
                } else {
                    echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
                }

                $sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
                $result20 = mysqli_query($conn, $sql20);

                if ($result20) {
                    $row20 = mysqli_fetch_assoc($result20);
                    if ($row20) {
                        $view = $row20['finance'] || $row20['section_all'];
                        if ($view) {
                            echo "<li><a href='financeinq_list.php'>Finance - Inquiry Category</a></li>";
                        }
                    } else {
                        echo "<script>alert('Role data not found')</script>";
                    }
                } else {
                    echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
                }

                $sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
                $result20 = mysqli_query($conn, $sql20);

                if ($result20) {
                    $row20 = mysqli_fetch_assoc($result20);
                    if ($row20) {
                        $view = $row20['it_support'] || $row20['section_all'];
                        if ($view) {
                            echo "<li><a href='itsupportinq_list.php'> IT Support - Inquiry Category</a></li>";
                        }
                    } else {
                        echo "<script>alert('Role data not found')</script>";
                    }
                } else {
                    echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
                }

                $sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
                $result20 = mysqli_query($conn, $sql20);

                if ($result20) {
                    $row20 = mysqli_fetch_assoc($result20);
                    if ($row20) {
                        $view = $row20['it_support'] || $row20['section_all'];
                        if ($view) {
                            echo "<li><a href='itsupport_category_list.php'> IT Support - Brand List</a></li>";
                        }
                    } else {
                        echo "<script>alert('Role data not found')</script>";
                    }
                } else {
                    echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
                }
                ?>


                </ul>
            </li>
            <?php
        }
    } else {
        echo "<script>alert('Role data not found')</script>";
    }
} else {
    echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
}
?>


<?php
$role_id = $_SESSION["role_id"];
$sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
$result20 = mysqli_query($conn, $sql20);

if ($result20) {
    $row20 = mysqli_fetch_assoc($result20);
    if ($row20) {
        $view = $row20['group_view'];
       $all = $row20['group_all'];
        if($view != '1' and $all !='1'){
       echo"";
       }else{
        echo"
        <li class='submenu'>
    <a href='javascript:void(0);'><img src='assets/img/icons/settings.svg' alt='img'><span> Settings</span> <span class='menu-arrow'></span></a>
    <ul>
    <li><a href='grouppermissions.php'>Group Permissions</a></li>
    <li><a href='update.php'>Update patches</a></li>
    </ul>
    </li>
    
        ";
       }
    } else {
        echo "<script>alert('Role data not found')</script>";
    }
} else {
    echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
}
?>

</ul>
</div>
</div>
</div>


<script>
document.getElementById('mark-as-read').addEventListener('click', function() {
    // Make an AJAX request
    var xhr = new XMLHttpRequest();
    xhr.open('POST', 'update_notification.php', true);
    xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
    xhr.onload = function() {
        if (xhr.status === 200) {
            // Request was successful
            alert('All messages marked as read');
            window.location.reload(); // Refresh the page
        } else {
            // Request failed
            alert('Error: ' + xhr.responseText);
        }
    };
    xhr.send('mark-as-read=true'); // Send data indicating the link was clicked
});
</script>
