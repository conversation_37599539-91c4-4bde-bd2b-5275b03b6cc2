<?php
require("database.php");
if(isset($_SESSION["user"])){
    $email=$_SESSION["user"];
?>
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0">
<meta name="description" content="CLL Warehouse Inventory System">
<meta name="author" content="Powered by CLL Systems Sdn Bhd">
<meta name="robots" content="noindex, nofollow">
<title>CLLXWARE-Import Product</title>

<link rel='icon' type='image/x-icon' href='https://www.cllsystems.com/wp-content/themes/cllsystems/img/favicon.png'>

<link rel="stylesheet" href="assets/css/bootstrap.min.css">

<link rel="stylesheet" href="assets/css/animate.css">

<link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">

<link rel="stylesheet" href="assets/css/dataTables.bootstrap4.min.css">

<link rel="stylesheet" href="assets/plugins/fontawesome/css/fontawesome.min.css">
<link rel="stylesheet" href="assets/plugins/fontawesome/css/all.min.css">

<link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
<div id="global-loader">
<div class="whirly-loader"> </div>
</div>

<div class="main-wrapper">

<?php 
$role_id = $_SESSION["role_id"];
$sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
$result20 = mysqli_query($conn, $sql20);

if ($result20) {
    $row20 = mysqli_fetch_assoc($result20);
    if ($row20) {
       $import = $row20['import_create'];
       if($import != '1'){
        header("location: ./index.php");
       }
    } else {
        echo "<script>alert('Role data not found')</script>";
    }
} else {
    echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
}
include("header.php");?>


</div>




<div class="page-wrapper">
<div class="content">
<div class="page-header">
<div class="page-title">
<h4>Import Products</h4>
<h6>Bulk upload your products</h6>
</div>
</div>
<form action='upload.php' method='post' enctype='multipart/form-data'>
<div class="card">
<div class="card-body">
<div class="requiredfield">
<h4>Field must be in csv format</h4>
</div>
<div class="row">
<div class="col-lg-3 col-sm-6 col-12">
<div class="form-group">
<a class="btn btn-submit w-100" href='Inventory_data.csv'>Download Sample File</a>
</div>
</div>

<div class="col-lg-12">
<div class="form-group">
<label> Upload CSV File</label>
<div class="image-upload">
<input type="file" id='csvFile' name='csvFile' accept='.csv' required>
<div class="image-uploads">
<img src="assets/img/icons/upload.svg" alt="img">
<h4>Drag and drop a file to upload</h4>
</div>
</div>
</div>
</div>
<div class="col-lg-6 col-sm-12">
<div class="productdetails productdetailnew">
<ul class="product-bar">
<li>
<h4>Product Model</h4>
<h6 class="manitorygreen">This Field is required</h6>
</li>
<li>
<h4>Brand</h4>
<h6 class="manitorygreen">This Field is required</h6>
</li>
<li>
<h4>Category</h4>
<h6 class="manitorygreen">This Field is required</h6>
</li>
<li>
<h4>Location</h4>
<h6 class="manitorygreen">This Field is required</h6>
</li>
<li>
<h4>Product Part Number</h4>
<h6 class="manitorygreen">This Field is required</h6>
</li>
<li>
<h4>Product Serial Number</h4>
<h6 class="manitorygreen">This Field is required</h6>
</li>
<li>
<h4>Quantity</h4>
<h6 class="manitorygreen">This Field is required</h6>
</li>
</ul>
</div>
</div>
<div class="col-lg-6 col-sm-12">
<div class="productdetails productdetailnew">
<ul class="product-bar">
<li>
<h4>Ownership</h4>
<h6 class="manitorygreen">This Field is required</h6>
</li>
<li>
<h4>Description</h4>
<h6 class="manitoryblue">Field optional</h6>
</li>
<li>
<h4>Status</h4>
<h6 class="manitorygreen">This Field is required</h6>
</li>
<li>
<h4>Remark</h4>
<h6 class="manitoryblue">Field optional</h6>
</li>
</ul>
</div>
</div>
<div class="col-lg-12">
<div class="form-group mb-0">
<button type='submit' class='btn btn-submit me-2'>Upload</button>
<a href="javascript:void(0);" class="btn btn-cancel">Cancel</a>
</div>
</div>

</div>
</div>
</div>
</form>
</div>
</div>
</div>


<script src="assets/js/jquery-3.6.0.min.js"></script>

<script src="assets/js/feather.min.js"></script>

<script src="assets/js/jquery.slimscroll.min.js"></script>

<script src="assets/js/jquery.dataTables.min.js"></script>
<script src="assets/js/dataTables.bootstrap4.min.js"></script>

<script src="assets/js/bootstrap.bundle.min.js"></script>

<script src="assets/plugins/select2/js/select2.min.js"></script>

<script src="assets/plugins/sweetalert/sweetalert2.all.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalerts.min.js"></script>

<script src="assets/js/script.js"></script>
</body>
</html>
<?php
}else{
    header("location: ./signin.php");
}
?>