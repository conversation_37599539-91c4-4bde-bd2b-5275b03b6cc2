<?php
require("database.php");
if(isset($_SESSION["user"])){
    $email=$_SESSION["user"];
?>
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0">
<meta name="description" content="CLL Warehouse Inventory System">
<meta name="author" content="Powered by CLL Systems Sdn Bhd">
<meta name="robots" content="noindex, nofollow">
<title>CLLXWARE</title>

<link rel='icon' type='image/x-icon' href='https://www.cllsystems.com/wp-content/themes/cllsystems/img/favicon.png'>

<link rel="stylesheet" href="assets/css/bootstrap.min.css">

<link rel="stylesheet" href="assets/css/animate.css">

<link rel="stylesheet" href="assets/css/dataTables.bootstrap4.min.css">

<link rel="stylesheet" href="assets/plugins/fontawesome/css/fontawesome.min.css">
<link rel="stylesheet" href="assets/plugins/fontawesome/css/all.min.css">

<link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
<div id="global-loader">
<div class="whirly-loader"> </div>
</div>

<div class="main-wrapper">

<?php include("header.php");?>
</div>

<div class="page-wrapper">
<div class="content">
<div class="row">
<?php
$sql = "SELECT COUNT(*) AS total_rows FROM `inventory-list`";
$result = mysqli_query($conn, $sql);
$row = mysqli_fetch_assoc($result);
echo"
<div class='col-lg-3 col-sm-6 col-12 d-flex'>
<div class='dash-count'>
<div class='dash-counts'>
<h4>" . (isset($row["total_rows"]) ? $row["total_rows"] : '') . "</h4>
<h5>Total Assets Inventory</h5>
</div>
<div class='dash-imgs'>
<i data-feather='box'></i>
</div>
</div>
</div>
";
?>
<?php
$sql5 = "SELECT COUNT(*) AS total_pending 
FROM (
    SELECT DISTINCT `request-id` 
    FROM `request-list` 
    WHERE `inventory_status` != 3 
    AND `approval_status` IN (0, 1)
) AS unique_pending_requests;
";
$result5 = mysqli_query($conn, $sql5);
$row5 = mysqli_fetch_assoc($result5);
echo"
<div class='col-lg-3 col-sm-6 col-12 d-flex'>
<div class='dash-count das1'>
<div class='dash-counts'>
<h4>" . (isset($row5["total_pending"]) ? $row5["total_pending"] : '') . "</h4>
<h5>Total Pending Request</h5>
</div>
<div class='dash-imgs'>
<i data-feather='bell'></i>
</div>
</div>
</div>";
?>
<?php
$sql3 = "SELECT COUNT(*) AS total_rows_user FROM `user-list`";
$result3 = mysqli_query($conn, $sql3);
$row3 = mysqli_fetch_assoc($result3);
echo"
<div class='col-lg-3 col-sm-6 col-12 d-flex'>
<div class='dash-count das2'>
<div class='dash-counts'>
<h4>" . (isset($row3["total_rows_user"]) ? $row3["total_rows_user"] : '') . "</h4>
<h5>Total Active Users</h5>
</div>
<div class='dash-imgs'>
<i data-feather='users'></i>
</div>
</div>
</div>";
?>
<?php
$sql4 = "SELECT COUNT(*) AS total_rows_user 
FROM (
    SELECT DISTINCT `request-id` 
    FROM `request-list` 
    WHERE `inventory_status` = 3 or `approval_status` = 2
) AS unique_request_ids;
";
$result4 = mysqli_query($conn, $sql4);
$row4 = mysqli_fetch_assoc($result4);
echo"
<div class='col-lg-3 col-sm-6 col-12 d-flex'>
<div class='dash-count das3'>
<div class='dash-counts'>
<h4>" . (isset($row4["total_rows_user"]) ? $row4["total_rows_user"] : '') . "</h4>
<h5>Total Completed Request</h5>
</div>
<div class='dash-imgs'>
<i data-feather='check-circle'></i>
</div>
</div>
</div>
</div>";?>

<div class="card mb-0">
<div class="card-body">
<h4 class="card-title">Latest Added Assets</h4>
<div class="table-responsive dataview">
<table class="table datatable ">
<thead>
<tr>
<th>No</th>
<th>Brand</th>
<th>Product Model</th>
<th>Product Part Number</th>
<th>Product Serial Number</th>
<th>Quantity</th>
<th>Ownership</th>
<th>Location</th>
</tr>
</thead>
<tbody>
<?php
$sql = "SELECT * FROM `inventory-list` ORDER BY `id` DESC LIMIT 10";
$result = mysqli_query($conn, $sql);


if ($result && mysqli_num_rows($result) > 0) {
    $counter = 1;

    while ($row = mysqli_fetch_assoc($result)) {
        // Sanitize data to prevent HTML issues
        $product_name = htmlspecialchars($row['product-name'] ?? '', ENT_QUOTES, 'UTF-8');
        $part_number = htmlspecialchars($row['part-number'] ?? '', ENT_QUOTES, 'UTF-8');
        $serial_number = htmlspecialchars($row['serial-number'] ?? '', ENT_QUOTES, 'UTF-8');
        $quantity = htmlspecialchars($row['quantity'] ?? '0', ENT_QUOTES, 'UTF-8');
        $ownership = htmlspecialchars($row['ownership'] ?? '', ENT_QUOTES, 'UTF-8');
        $location_id = htmlspecialchars($row['location-id'] ?? '', ENT_QUOTES, 'UTF-8');
        $inventory_id = htmlspecialchars($row['inventory-id'] ?? '', ENT_QUOTES, 'UTF-8');

        $brand_id = $row['brand-id'];
        $sql2 = "SELECT `brand-name` FROM `brand-list` WHERE `brand-id` = '" . mysqli_real_escape_string($conn, $brand_id) . "'";
        $result2 = mysqli_query($conn, $sql2);

        if ($result2 && mysqli_num_rows($result2) > 0) {
            $row2 = mysqli_fetch_assoc($result2);
            $brand_name = htmlspecialchars($row2['brand-name'] ?? '', ENT_QUOTES, 'UTF-8');
        } else {
            $brand_name = "Unknown Brand";
        }

        echo "<tr>";
        echo "<td>" . $counter . "</td>";
        echo "<td>" . $brand_name . "</td>";
        echo "<td class='productimgname'>";
        echo "<a href='inventory-details.php?inventory-id=" . $inventory_id . "'>" . $product_name . "</a>";
        echo "</td>";
        echo "<td>" . $part_number . "</td>";
        echo "<td>" . $serial_number . "</td>";
        echo "<td>" . $quantity . "</td>";
        echo "<td>" . $ownership . "</td>";
        echo "<td>Room " . $location_id . "</td>";
        echo "</tr>";

        $counter++;
    }
} else {
    // Add a fallback row if no data is found
    echo "<tr><td colspan='8' class='text-center'>No data available</td></tr>";
}?>
</tbody>
</table>
</div>
</div>
</div>
</div>
</div>
</div>


<script src="assets/js/jquery-3.6.0.min.js"></script>

<script src="assets/js/feather.min.js"></script>

<script src="assets/js/jquery.slimscroll.min.js"></script>

<script src="assets/js/jquery.dataTables.min.js"></script>
<script src="assets/js/dataTables.bootstrap4.min.js"></script>

<script src="assets/js/bootstrap.bundle.min.js"></script>

<script src="assets/plugins/apexchart/apexcharts.min.js"></script>
<script src="assets/plugins/apexchart/chart-data.js"></script>

<script src="assets/js/script.js"></script>
</body>
</html>
<?php
}else{
    header("location: ./signin.php");
}
?>