<?php
require("database.php");
if(isset($_SESSION["user"])){
    $email=$_SESSION["user"];
?>
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0">
<meta name="description" content="CLL Warehouse Inventory System">
<meta name="author" content="Powered by CLL Systems Sdn Bhd">
<meta name="robots" content="noindex, nofollow">
<title>CLLXWARE-Inventory Details</title>

<link rel='icon' type='image/x-icon' href='https://www.cllsystems.com/wp-content/themes/cllsystems/img/favicon.png'>

<link rel="stylesheet" href="assets/css/bootstrap.min.css">

<link rel="stylesheet" href="assets/css/animate.css">

<link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">

<link rel="stylesheet" href="assets/plugins/owlcarousel/owl.carousel.min.css">

<link rel="stylesheet" href="assets/css/dataTables.bootstrap4.min.css">

<link rel="stylesheet" href="assets/plugins/fontawesome/css/fontawesome.min.css">
<link rel="stylesheet" href="assets/plugins/fontawesome/css/all.min.css">

<link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
<div id="global-loader">
<div class="whirly-loader"> </div>
</div>

<div class="main-wrapper">

<?php include("header.php");?>
</div>

<div class="page-wrapper">
<div class="content">
<div class="page-header">
<div class="page-title">
<h4>Inventory Details</h4>
<h6>Full details of a inventory</h6>
</div>
</div>

<div class="row">

<?php
if(isset($_GET['inventory-id'])) {
    $inventory_id = $_GET['inventory-id'];
    $sql = "SELECT * FROM `inventory-list` WHERE `inventory-id` = '$inventory_id'";
    $result = mysqli_query($conn, $sql);

    if ($result && mysqli_num_rows($result) > 0) {
        $row = mysqli_fetch_assoc($result);

        // Check if the necessary array keys exist before accessing them
        $brand_name = "Unknown Brand"; 
        if (isset($row['brand-id'])) {
            $brand_id = $row['brand-id'];
            $sql2 = "SELECT `brand-name` FROM `brand-list` WHERE `brand-id` = '$brand_id'";
            $result2 = mysqli_query($conn, $sql2);
            if ($result2 && mysqli_num_rows($result2) > 0) {
                $row2 = mysqli_fetch_assoc($result2);
                $brand_name = $row2['brand-name'];
            }
        }

        $category_name = "Unknown Category";
        if (isset($row['category-id'])) {
            $category_id = $row['category-id'];
            $sql3 = "SELECT `category-name` FROM `category-list` WHERE `category-id` = '$category_id'";
            $result3 = mysqli_query($conn, $sql3);
            if ($result3 && mysqli_num_rows($result3) > 0) {
                $row3 = mysqli_fetch_assoc($result3);
                $category_name = $row3['category-name'];
            }
        }
        $qty=$row['quantity'];
        $reserve_qty=$row['reserve_qty'];
        $totalqty=$qty+$reserve_qty;
        // Display inventory details
        echo "
        <div class='card'>
            <div class='card-body'>
                <div class='productdetails'>
                    <ul class='product-bar'>
                        <li>
                            <h4>Product Model</h4>
                            <h6>{$row['product-name']}</h6>
                        </li>
                        <li>
                            <h4>Brand</h4>
                            <h6>$brand_name</h6>
                        </li>
                        <li>
                            <h4>Category</h4>
                            <h6>$category_name</h6>
                        </li>
                        <li>
                            <h4>Product Part Number</h4>
                            <h6>{$row['part-number']}</h6>
                        </li>
                        <li>
                            <h4>Product Serial Number</h4>
                            <h6>{$row['serial-number']}</h6>
                        </li>
                        <li>
                            <h4>Quantity</h4>
                            <h6>$totalqty (Reserved:$reserve_qty)</h6>
                        </li>
                        <li>
                            <h4>Ownership</h4>
                            <h6>{$row['ownership']}</h6>
                        </li>
                        <li>
                            <h4>Location</h4>
                            <h6>ROOM {$row['location-id']}</h6>
                        </li>
                        <li>
                            <h4>Created By</h4>
                            <h6>{$row['created']}</h6>
                        </li>
                        <li>
                            <h4>Status</h4>";
                            if($qty != '0') {
                                echo "<h6>Available</h6>";
                            } else {
                                echo "<h6>Taken</h6>";
                            }
                            echo "
                        </li>
                        <li>
                            <h4>Remark</h4>
                            <h6>{$row['remark']}</h6>
                        </li>
                        <li>
                            <h4>Description</h4>
                            <h6>{$row['des']}</h6>
                        </li>
                    </ul>
                </div>
            </div>
        </div>";
    } else {
        // Handle case where no inventory item with the provided inventory-id is found
        echo "<p>No inventory item found with the provided ID.</p>";
    }
} else {
    // Handle case where inventory-id is not provided in the URL
    echo "<p>Inventory ID is not provided in the URL.</p>";
}
?>



</div>
</div>
</div>


<script src="assets/js/jquery-3.6.0.min.js"></script>

<script src="assets/js/feather.min.js"></script>

<script src="assets/js/jquery.slimscroll.min.js"></script>

<script src="assets/js/bootstrap.bundle.min.js"></script>

<script src="assets/plugins/owlcarousel/owl.carousel.min.js"></script>

<script src="assets/plugins/select2/js/select2.min.js"></script>

<script src="assets/js/script.js"></script>
</body>
</html>
<?php
}else{
    header("location: ./signin.php");
}
?>