<?php
require("database.php");
if(isset($_SESSION["user"])){
    $email=$_SESSION["user"];
?>
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0">
<meta name="description" content="CLL Warehouse Inventory System">
<meta name="author" content="Powered by CLL Systems Sdn Bhd">
<meta name="robots" content="noindex, nofollow">
<title>CLLXWARE-Inventory List</title>

<link rel='icon' type='image/x-icon' href='https://www.cllsystems.com/wp-content/themes/cllsystems/img/favicon.png'>

<link rel="stylesheet" href="assets/css/bootstrap.min.css">

<link rel="stylesheet" href="assets/css/animate.css">

<link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">

<link rel="stylesheet" href="assets/css/dataTables.bootstrap4.min.css">

<link rel="stylesheet" href="assets/plugins/fontawesome/css/fontawesome.min.css">
<link rel="stylesheet" href="assets/plugins/fontawesome/css/all.min.css">

<link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
<div id="global-loader">
<div class="whirly-loader"> </div>
</div>

<div class="main-wrapper">

<?php 
$role_id = $_SESSION["role_id"];
$sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
$result20 = mysqli_query($conn, $sql20);

if ($result20) {
    $row20 = mysqli_fetch_assoc($result20);
    if ($row20) {
       $view = $row20['inventory_view'];
       $all = $row20['inventory_all'];
       if($view != '1' and $all !='1'){
        header("location: ./index.php");
       }
    } else {
        echo "<script>alert('Role data not found')</script>";
    }
} else {
    echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
}
include("header.php");?>
</div>

<div class="page-wrapper">
<div class="content">
<div class="page-header">
<div class="page-title">
<h4>Inventory List</h4>
<h6>Manage your inventory</h6>
</div>
<?php
$role_id = $_SESSION["role_id"];
$sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
$result20 = mysqli_query($conn, $sql20);

if ($result20) {
    $row20 = mysqli_fetch_assoc($result20);
    if ($row20) {
       $create = $row20['inventory_create'];
       $all = $row20['inventory_all'];
       if($create != '1' and $all !='1'){
       echo"";
       }else{
        echo"
        <div class='page-btn'>
        <a href='addinventory.php' class='btn btn-added'><img src='assets/img/icons/plus.svg' alt='img' class='me-1'>Add New Inventory</a>
        </div>";
       }
    } else {
        echo "<script>alert('Role data not found')</script>";
    }
} else {
    echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
}


?>
</div>

<div class="card">
<div class="card-body">
<div class="table-top">
<div class="search-set">
<!---<div class="search-path">
 <a class="btn btn-filter" id="filter_search">
<img src="assets/img/icons/filter.svg" alt="img">
<span><img src="assets/img/icons/closes.svg" alt="img"></span>
</a>
</div>--->
<div class="search-input">
<a class="btn btn-searchset"><img src="assets/img/icons/search-white.svg" alt="img"></a>
</div>
</div>
<div class="wordset">
<ul>
<form action='csv_inventory.php' method='post'>
<li>
<a data-bs-toggle="tooltip" data-bs-placement="top" title="CSV" href="#" onclick="submitForm();">
                <img src="assets/img/icons/excel.svg" alt="CSV">
            </a>
</li>
</form>
<li>
<a data-bs-toggle="tooltip" data-bs-placement="top" title="Print"><img src="assets/img/icons/printer.svg" alt="img" id="printButton"></a>
</li>
</ul>
</div>
</div>

<!--- <div class="card mb-0" id="filter_inputs">
<div class="card-body pb-0">
<div class="row">
<div class="col-lg-12 col-sm-12">
<div class="row">
<?php 
//$sql3 = "SELECT DISTINCT `brand-id` FROM `inventory-list`"; // Fetch distinct brand IDs
//$result3 = mysqli_query($conn, $sql3);

//$sql4 = "SELECT DISTINCT `ownership` FROM `inventory-list`"; // Fetch distinct ownership values
//$result4 = mysqli_query($conn, $sql4);

//$sql5 = "SELECT DISTINCT `location-id` FROM `inventory-list`"; // Fetch distinct location IDs
//$result5 = mysqli_query($conn, $sql5);
?>

<div class='col-lg col-sm-6 col-12'>
    <div class='form-group'>
        <select class='select'>
            <option disabled selected>Choose Brand</option>
            <?php
            //$brands = array(); // Array to store unique brand names
           // while ($row3 = mysqli_fetch_assoc($result3)) {
               // $brand_id2 = $row3['brand-id']; 
               // $sql6 = "SELECT `brand-name` FROM `brand-list` WHERE `brand-id` = '$brand_id2'";
               // $result6 = mysqli_query($conn, $sql6);

                //if ($result6 && mysqli_num_rows($result6) > 0) {
                  //  $row6 = mysqli_fetch_assoc($result6);
                    //$brand_name2 = $row6['brand-name'];
                    // Add the brand name to the array if it's not already present
                    //if (!in_array($brand_name2, $brands)) {
                       // $brands[] = $brand_name2;
                       //echo "<option value='$brand_name2'>$brand_name2</option>";
                   //}
               // } else {
                  //  $brand_name2 = "Unknown Brand"; 
                //}
            //}
            ?>
        </select>
    </div>
</div>

<div class='col-lg col-sm-6 col-12'>
    <div class='form-group'>
        <select class='select'>
            <option>Choose Ownership</option>
            <?php
            //$ownerships = array(); // Array to store unique ownership values
            //while ($row4 = mysqli_fetch_assoc($result4)) {
                //$ownership = $row4['ownership'];
                // Add the ownership value to the array if it's not already present
                //if (!in_array($ownership, $ownerships)) {
                    //$ownerships[] = $ownership;
                    //echo "<option value='$ownership'>$ownership</option>";
                //}
           // }
            ?>
        </select>
    </div>
</div>

<div class='col-lg col-sm-6 col-12'>
    <div class='form-group'>
        <select class='select'>
            <option disabled selected>Location</option>
            <?php
            //$locations = array(); // Array to store unique location IDs
           // while ($row5 = mysqli_fetch_assoc($result5)) {
                //$location_id = $row5['location-id'];
                // Add the location ID to the array if it's not already present
                //if (!in_array($location_id, $locations)) {
                  //  $locations[] = $location_id;
                    //echo "<option value='$location_id'>$location_id</option>";
                //}
            //}
            ?>
        </select>
    </div>
</div>

<div class="col-lg-1 col-sm-6 col-12">
<div class="form-group">
<a class="btn btn-filters ms-auto"><img src="assets/img/icons/search-whites.svg" alt="img"></a>
</div>
</div>
</div>
</div>
</div>
</div>
</div>--->

<div class="table-responsive">
<table class="table  datanew">
<thead>
<tr>
<th>No</th>
<th>Brand</th>
<th>Product Model</th>
<th>Product Part Number</th>
<th>Product Serial Number</th>
<th>Quantity</th>
<th>Ownership</th>
<th>Location</th>
<th>Created By</th>
<th>Status</th>
<th>Action</th>
</tr>
</thead>
<tbody>
<?php 
$sql = "SELECT * FROM `inventory-list`";
$result = mysqli_query($conn, $sql);


if ($result && mysqli_num_rows($result) > 0) {
    $counter = 1;

    while ($row = mysqli_fetch_assoc($result)) {
echo"
<tr>
<td>$counter</td>";
$brand_id = $row['brand-id']; 
$sql2 = "SELECT `brand-name` FROM `brand-list` WHERE `brand-id` = '$brand_id'";
$result2 = mysqli_query($conn, $sql2);

if ($result2 && mysqli_num_rows($result2) > 0) {
    $row2 = mysqli_fetch_assoc($result2);
    $brand_name = $row2['brand-name'];
} else {
    $brand_name = "Unknown Brand"; 
}
$qty=$row['quantity'];
$reserve_qty=$row['reserve_qty'];
$totalqty=$qty+$reserve_qty;
echo"
<td>$brand_name</td>
<td>" . $row['product-name'] . "</td>
<td>" . $row['part-number'] . "</td>
<td>" . $row['serial-number'] . "</td>
<td>$totalqty (Reserved:$reserve_qty)</td>
<td>" . $row['ownership'] . "</td>
<td>Room " . $row['location-id'] . "</td>
<td>" . $row['created'] . "</td>
";
$status = $row['status']; 
if($qty!='0')
{
    echo"<td style='color: green; font-weight: bold;'>Available</td>";
}else{
    echo"<td style='color: red; font-weight: bold;'>Taken</td>";
}
echo"

<td>
<a class='me-3' href='inventory-details.php?inventory-id=" . $row['inventory-id'] . "'>
<img src='assets/img/icons/eye.svg' alt='img'>
</a>";
$role_id = $_SESSION["role_id"];
$sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
$result20 = mysqli_query($conn, $sql20);

if ($result20) {
    $row20 = mysqli_fetch_assoc($result20);
    if ($row20) {
       $edit = $row20['inventory_edit'];
       $all = $row20['inventory_all'];
       if($edit != '1' and $all !='1'){
        echo"";
       }else{
        echo"
        <a class='me-3' href='editinventory.php?inventory-id=" . $row['inventory-id'] . "'>
        <img src='assets/img/icons/edit.svg' alt='img'>
        </a>
        ";
       }
    } else {
        echo "<script>alert('Role data not found')</script>";
    }
} else {
    echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
}
//if ($qty == '0' && $reserve_qty == '0') {
    //echo "<a href='javascript:void(0);' onclick='deleteItem(\"" . $row['inventory-id'] . "\");'>
    //<img src='assets/img/icons/delete.svg' alt='Delete'>
    //</a>";
//}
?>
<?php echo"
</td>
</tr>";
$counter++;
}
}
?>
</tbody>
</table>
</div>
</div>
</div>

</div>
</div>
</div>

<script>
    function deleteItem(inventoryId) {
        if (confirm('Are you sure you want to delete this item?')) {
            // Create a new XMLHttpRequest object
            var xhr = new XMLHttpRequest();
            
            // Define the request
            xhr.open('POST', 'delete_item.php', true);
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
            
            // Set up the callback function
            xhr.onload = function () {
                if (xhr.status >= 200 && xhr.status < 300) {
                    // Reload the page or perform any other action if deletion is successful
                    window.location.reload();
                } else {
                    // Handle error
                    console.error('Error:', xhr.responseText);
                    alert('Error deleting item: ' + xhr.responseText);
                }
            };
            
            // Send the request
            xhr.send('inventory_id=' + inventoryId);
        }
    }
</script>
<script>
    function submitForm() {
        // Programmatically submit the form when the link is clicked
        document.querySelector("form").submit();
    }
</script>

<script>
  document.getElementById("printButton").addEventListener("click", function() {
    // Create a temporary copy of the table
    const originalTable = document.querySelector(".datanew");
    const printTable = originalTable.cloneNode(true);

    // Create a temporary printable container
    const printContainer = document.createElement("div");
    printContainer.style.display = "none";
    printContainer.appendChild(printTable);
    document.body.appendChild(printContainer);

    // Set print styles for table and landscape orientation
    printTable.style.width = "100%";
    printTable.style.border = "1px solid black"; // Optional for visual clarity
    const stylesheet = document.createElement("style");
    stylesheet.textContent = "@page { size: landscape; }";
    printContainer.appendChild(stylesheet);

    // Trigger print and clean up
    window.print();
    document.body.removeChild(printContainer);
  });
</script>
<script src="assets/js/jquery-3.6.0.min.js"></script>

<script src="assets/js/feather.min.js"></script>

<script src="assets/js/jquery.slimscroll.min.js"></script>

<script src="assets/js/jquery.dataTables.min.js"></script>
<script src="assets/js/dataTables.bootstrap4.min.js"></script>

<script src="assets/js/bootstrap.bundle.min.js"></script>

<script src="assets/plugins/select2/js/select2.min.js"></script>

<script src="assets/plugins/sweetalert/sweetalert2.all.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalerts.min.js"></script>

<script src="assets/js/script.js"></script>
</body>
</html>
<?php
}else{
    header("location: ./signin.php");
}
?>