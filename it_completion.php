<?php
require('database.php');
require_once 'MAILER/vendor/autoload.php';
use PHPMailer\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\Exception;

// Get parameters from URL
$onboarding_id = $_GET['id'] ?? '';
$it_email = $_GET['email'] ?? '';
$access_token = $_GET['token'] ?? '';

if (!$onboarding_id || !$it_email || !$access_token) {
    echo "<script>alert('Invalid access link.'); window.close();</script>";
    exit;
}

// Verify access token (security measure)
$expected_token = hash('sha256', $onboarding_id . $it_email . 'IT_COMPLETION_2024');
$expected_override_token = hash('sha256', $onboarding_id . 'IT_OVERRIDE_' . time()); // This will be checked against database

// Check if it's a valid token (either original or override)
$valid_token = false;
if ($access_token === $expected_token) {
    $valid_token = true;
} else {
    // Check if it's an override token from database
    $token_check_stmt = mysqli_prepare($conn, "SELECT it_override_token, it_override_email FROM `onboarding_list` WHERE `onboarding_id` = ?");
    mysqli_stmt_bind_param($token_check_stmt, 's', $onboarding_id);
    mysqli_stmt_execute($token_check_stmt);
    $token_result = mysqli_stmt_get_result($token_check_stmt);
    $token_data = mysqli_fetch_assoc($token_result);
    mysqli_stmt_close($token_check_stmt);

    if ($token_data && $token_data['it_override_token'] === $access_token && $token_data['it_override_email'] === $it_email) {
        $valid_token = true;
    }
}

if (!$valid_token) {
    echo "<script>alert('Invalid or expired access token.'); window.close();</script>";
    exit;
}

// Get onboarding record with related names
$stmt = mysqli_prepare($conn, "
    SELECT ol.*,
           dl.department_name,
           ml.manager_name,
           ofl.office_name as office_name_actual
    FROM `onboarding_list` ol
    LEFT JOIN `department_list` dl ON ol.department_id = dl.department_id
    LEFT JOIN `manager_list` ml ON ol.manager_id = ml.manager_id
    LEFT JOIN `office_list` ofl ON ol.office_id = ofl.office_id
    WHERE ol.onboarding_id = ?
");
mysqli_stmt_bind_param($stmt, 's', $onboarding_id);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);
$record = mysqli_fetch_assoc($result);
mysqli_stmt_close($stmt);

if (!$record) {
    echo "<script>alert('Onboarding record not found.'); window.close();</script>";
    exit;
}

// Check if setup is already completed
if (!empty($record['laptop_email_sent'])) {
    $already_completed = true;
    // If already completed, show completion status and block further access
} else {
    $already_completed = false;
}

// Check if this email is authorized (primary or backup)
$is_authorized = ($it_email === $record['it_primary_email'] || $it_email === $record['it_backup_email']);
if (!$is_authorized) {
    echo "<script>alert('You are not authorized to complete this setup.'); window.close();</script>";
    exit;
}

// Handle form submission
if ($_POST && isset($_POST['complete_setup']) && !$already_completed) {
    $completion_remarks = $_POST['completion_remarks'] ?? '';
    $completed_by = $it_email;
    
    $stmt = mysqli_prepare($conn, "UPDATE `onboarding_list` SET `laptop_email_sent` = 1, `completed_by` = ?, `completion_remarks` = ?, `completion_date` = NOW() WHERE `onboarding_id` = ?");
    mysqli_stmt_bind_param($stmt, 'sss', $completed_by, $completion_remarks, $onboarding_id);
    mysqli_stmt_execute($stmt);
    mysqli_stmt_close($stmt);
    
    // Send notification to HR that laptop setup is complete
    send_hr_laptop_completion_notification($onboarding_id, $conn, $completed_by, $completion_remarks);
    
    $completion_message = "Laptop setup marked as complete! HR team has been notified. This link is now expired.";
    $already_completed = true;
    
    // Refresh record with related names
    $stmt = mysqli_prepare($conn, "
        SELECT ol.*,
               dl.department_name,
               ml.manager_name,
               ofl.office_name as office_name_actual
        FROM `onboarding_list` ol
        LEFT JOIN `department_list` dl ON ol.department_id = dl.department_id
        LEFT JOIN `manager_list` ml ON ol.manager_id = ml.manager_id
        LEFT JOIN `office_list` ofl ON ol.office_id = ofl.office_id
        WHERE ol.onboarding_id = ?
    ");
    mysqli_stmt_bind_param($stmt, 's', $onboarding_id);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    $record = mysqli_fetch_assoc($result);
    mysqli_stmt_close($stmt);
}

// Function to send HR notification when laptop setup is complete
function send_hr_laptop_completion_notification($onboarding_id, $conn, $completed_by, $completion_remarks) {
    // Get employee record with related names
    $stmt = mysqli_prepare($conn, "
        SELECT ol.*,
               dl.department_name,
               ml.manager_name,
               ofl.office_name as office_name_actual
        FROM `onboarding_list` ol
        LEFT JOIN `department_list` dl ON ol.department_id = dl.department_id
        LEFT JOIN `manager_list` ml ON ol.manager_id = ml.manager_id
        LEFT JOIN `office_list` ofl ON ol.office_id = ofl.office_id
        WHERE ol.onboarding_id = ?
    ");
    mysqli_stmt_bind_param($stmt, 's', $onboarding_id);
    mysqli_stmt_execute($stmt);
    $res = mysqli_stmt_get_result($stmt);
    $rec = mysqli_fetch_assoc($res);
    mysqli_stmt_close($stmt);
    
    if (!$rec) return;
    
    try {
        // Get HR email configuration
        $hr_config = mysqli_query($conn, "SELECT * FROM `onboarding_email_config` WHERE `config_type` = 'hr_admin'");
        $hr_data = mysqli_fetch_assoc($hr_config);
        
        $hr_emails = $hr_data ? array_filter(array_map('trim', explode("\n", $hr_data['email_addresses']))) : ['<EMAIL>'];
        
        $workflow_link = (isset($_SERVER['HTTP_HOST']) ? ( (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://') . $_SERVER['HTTP_HOST'] . dirname($_SERVER['PHP_SELF']) ) : 'https://cis.cllsystems.com:9443/staging');
        $workflow_link = rtrim($workflow_link,'/')."/onboarding_workflow.php?id=".urlencode($rec['onboarding_id']);
        
        $email_subject = 'Laptop Setup Completed - ' . $rec['full_name'];
        $email_body = "Dear HR Team,\n\n";
        $email_body .= "The laptop setup has been completed for:\n\n";
        $email_body .= "Employee Information:\n";
        $email_body .= "- Name: " . $rec['full_name'] . "\n";
        $email_body .= "- Employee ID: " . $rec['employee_id'] . "\n";
        $email_body .= "- Department: " . ($rec['department_name'] ?? 'Not specified') . "\n";
        $email_body .= "- Designation: " . $rec['job_title'] . "\n";
        $email_body .= "- Company Email: " . ($rec['company_email'] ?? 'Not set') . "\n";
        if (!empty($rec['company_email_password'])) {
            $email_body .= "- Company Email Password: " . $rec['company_email_password'] . "\n";
        }
        $email_body .= "- Personal Email: " . $rec['personal_email'] . "\n";
        $email_body .= "- Primary IT Member: " . $rec['it_primary_email'] . "\n";
        if (!empty($rec['it_backup_email'])) {
            $email_body .= "- Backup IT Member: " . $rec['it_backup_email'] . "\n";
        }
        $email_body .= "- Completed By: " . $completed_by . "\n";
        $email_body .= "- Completion Date: " . date('Y-m-d H:i:s') . "\n";
        if (!empty($completion_remarks)) {
            $email_body .= "- Completion Remarks: " . $completion_remarks . "\n";
        }
        if (!empty($rec['it_remarks'])) {
            $email_body .= "- Setup Remarks: " . $rec['it_remarks'] . "\n";
        }
        $email_body .= "\nNext Step: Please complete the HR checklist for this employee.\n\n";
        $email_body .= "Complete HR Checklist: " . $workflow_link . "\n\n";
        $email_body .= "Best regards,\nIT Support Team";
        
        $mail = new PHPMailer(true);
        $mail->isSMTP();
        $mail->Host='smtp.office365.com';
        $mail->SMTPAuth=true;
        $mail->Username='<EMAIL>';
        $mail->Password='&[i3F6}0hOw6';
        $mail->SMTPSecure='tls';
        $mail->Port=587;
        $mail->setFrom('<EMAIL>','CLLXWARE');
        
        foreach($hr_emails as $email) {
            $mail->addAddress($email);
        }
        $mail->isHTML(false);
        $mail->Subject = $email_subject;
        $mail->Body = $email_body;
        $mail->send();
        
    } catch (Exception $e) {
        // Log error but don't stop the process
        error_log('HR notification error: ' . $e->getMessage());
    }
}

// Ensure completion tracking columns exist
function ensure_column_exists($conn, $table, $column, $definition) {
    $result = mysqli_query($conn, "SHOW COLUMNS FROM `$table` LIKE '$column'");
    if (mysqli_num_rows($result) == 0) {
        mysqli_query($conn, "ALTER TABLE `$table` ADD COLUMN `$column` $definition");
    }
}

ensure_column_exists($conn, 'onboarding_list', 'completed_by', 'varchar(255) NULL');
ensure_column_exists($conn, 'onboarding_list', 'completion_remarks', 'TEXT NULL');
ensure_column_exists($conn, 'onboarding_list', 'completion_date', 'DATETIME NULL');
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IT Laptop Setup Completion - <?php echo htmlspecialchars($record['full_name']); ?></title>
    <link rel="shortcut icon" type="image/x-icon" href="assets/img/favicon.jpg">
    <link rel="stylesheet" href="assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="assets/css/animate.css">
    <link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">
    <link rel="stylesheet" href="assets/css/dataTables.bootstrap4.min.css">
    <link rel="stylesheet" href="assets/plugins/fontawesome/css/fontawesome.min.css">
    <link rel="stylesheet" href="assets/plugins/fontawesome/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">

    <style>
        .form-group { position: relative; }
        .form-group input,
        .form-group textarea,
        .form-group select { padding-left: 12px; }
        .card { border: 1px solid #f0f0f0; padding: 20px; box-shadow: 0 4px 8px rgba(0,0,0,0.1); margin-bottom: 20px; }
        .section-header { 
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%); 
            color: white; 
            padding: 15px; 
            border-radius: 8px; 
            margin-bottom: 20px;
            text-align: center;
        }
        .employee-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #28a745;
            margin-bottom: 20px;
        }
        .credentials-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .password-field {
            font-family: monospace;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 8px 12px;
            border-radius: 4px;
            display: inline-block;
            min-width: 200px;
        }
        .btn-complete {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 5px;
            font-weight: bold;
            font-size: 16px;
        }
        .btn-complete:hover {
            background: linear-gradient(135deg, #218838 0%, #1ea085 100%);
            color: white;
        }
        .completed-status {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
    </style>
</head>
<body>

<div class="main-wrapper">
    <div class="page-wrapper">
        <div class="content">
            <div class="page-header">
                <div class="page-title form-header text-start">
                    <h4>IT Laptop Setup Completion</h4>
                    <p>Complete laptop setup for - <?php echo htmlspecialchars($record['full_name']); ?></p>
                </div>
            </div>

            <?php if (isset($completion_message)): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle"></i> <?php echo $completion_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <?php endif; ?>

            <!-- Employee Information -->
            <div class="card">
                <div class="card-body">
                    <div class="section-header">
                        <h5><i class="fas fa-user me-2"></i>Employee Information</h5>
                    </div>
                    
                    <div class="employee-info">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>Name:</strong> <?php echo htmlspecialchars($record['full_name']); ?></p>
                                <p><strong>Employee ID:</strong> <?php echo htmlspecialchars($record['employee_id']); ?></p>
                                <p><strong>Department:</strong> <?php echo htmlspecialchars($record['department_name'] ?? 'Not specified'); ?></p>
                                <p><strong>Company Email:</strong> <?php echo htmlspecialchars($record['company_email'] ?? 'Not set'); ?></p>
                                <?php if (!empty($record['company_email_password'])): ?>
                                <p><strong>Company Email Password:</strong>
                                    <span id="password-display" style="font-family: monospace;">••••••••</span>
                                    <button type="button" id="toggle-password" class="btn btn-sm btn-outline-secondary ms-2">
                                        <i class="fas fa-eye" id="eye-icon"></i>
                                    </button>
                                    <span id="actual-password" style="display: none;"><?php echo htmlspecialchars($record['company_email_password']); ?></span>
                                </p>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Designation:</strong> <?php echo htmlspecialchars($record['job_title']); ?></p>
                                <p><strong>Onboarding Date:</strong> <?php echo htmlspecialchars($record['onboarding_date']); ?></p>
                                <p><strong>Personal Email:</strong> <?php echo htmlspecialchars($record['personal_email']); ?></p>
                                <p><strong>Your Role:</strong> <?php echo ($it_email === $record['it_primary_email']) ? 'Primary IT Member' : 'Backup IT Member'; ?></p>
                            </div>
                        </div>
                    </div>

                    <!-- Company Email Status -->
                    <?php if (!empty($record['company_email'])): ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> <strong>Company email credentials were provided in your assignment email.</strong>
                    </div>
                    <?php endif; ?>

                    <!-- Setup Remarks -->
                    <?php if (!empty($record['it_remarks'])): ?>
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle"></i> Setup Remarks:</h6>
                        <p><?php echo htmlspecialchars($record['it_remarks']); ?></p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Completion Form or Status -->
            <?php if ($already_completed): ?>
            <!-- Already Completed -->
            <div class="card">
                <div class="card-body">
                    <div class="completed-status">
                        <h4><i class="fas fa-check-circle me-2"></i>Laptop Setup Already Completed!</h4>
                        <p class="mb-2">This laptop setup has already been marked as complete and this link has expired.</p>
                        <?php if (!empty($record['completed_by'])): ?>
                        <p class="mb-1"><strong>Completed by:</strong> <?php echo htmlspecialchars($record['completed_by']); ?></p>
                        <?php endif; ?>
                        <?php if (!empty($record['completion_date'])): ?>
                        <p class="mb-1"><strong>Completion Date:</strong> <?php echo htmlspecialchars($record['completion_date']); ?></p>
                        <?php endif; ?>
                        <?php if (!empty($record['completion_remarks'])): ?>
                        <p class="mt-2 mb-1"><strong>Completion Remarks:</strong><br><?php echo htmlspecialchars($record['completion_remarks']); ?></p>
                        <?php endif; ?>
                        <div class="alert alert-info mt-3 mb-0">
                            <i class="fas fa-info-circle"></i> <strong>Note:</strong> HR team has been notified and this completion link is now expired.
                        </div>
                    </div>
                </div>
            </div>
            <?php else: ?>
            <!-- Completion Form -->
            <div class="card">
                <div class="card-body">
                    <div class="section-header">
                        <h5><i class="fas fa-laptop me-2"></i>Mark Setup as Complete</h5>
                    </div>

                    <form method="POST" onsubmit="return confirm('Are you sure the laptop setup is complete? This will notify HR team and expire this link.');">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="form-group">
                                    <label>Completion Remarks (Optional)</label>
                                    <textarea class="form-control" name="completion_remarks" rows="4" placeholder="Enter any remarks about the laptop setup completion, issues encountered, or additional notes..."></textarea>
                                    <small class="form-text text-muted">Optional: Add any notes about the setup process</small>
                                </div>
                            </div>

                            <div class="col-lg-12">
                                <div class="alert alert-warning">
                                    <h6><i class="fas fa-exclamation-triangle"></i> Important:</h6>
                                    <ul class="mb-0">
                                        <li>Only mark as complete when the laptop setup is fully finished</li>
                                        <li>HR team will be automatically notified</li>
                                        <li>This link will expire after completion</li>
                                        <li>This action cannot be undone</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="col-lg-12 text-center">
                                <button type="submit" name="complete_setup" class="btn btn-complete">
                                    <i class="fas fa-check-circle"></i> Mark Laptop Setup as Complete
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <?php endif; ?>

        </div>
    </div>
</div>

<!-- Scripts -->
<script src="assets/js/jquery-3.6.0.min.js"></script>
<script src="assets/js/feather.min.js"></script>
<script src="assets/js/jquery.slimscroll.min.js"></script>
<script src="assets/js/jquery.dataTables.min.js"></script>
<script src="assets/js/dataTables.bootstrap4.min.js"></script>
<script src="assets/js/bootstrap.bundle.min.js"></script>
<script src="assets/plugins/select2/js/select2.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalert2.all.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalerts.min.js"></script>

<script>
// Password toggle functionality
document.addEventListener('DOMContentLoaded', function() {
    const toggleBtn = document.getElementById('toggle-password');
    const passwordDisplay = document.getElementById('password-display');
    const actualPassword = document.getElementById('actual-password');
    const eyeIcon = document.getElementById('eye-icon');

    if (toggleBtn && passwordDisplay && actualPassword && eyeIcon) {
        toggleBtn.addEventListener('click', function() {
            if (passwordDisplay.textContent === '••••••••') {
                passwordDisplay.textContent = actualPassword.textContent;
                eyeIcon.className = 'fas fa-eye-slash';
            } else {
                passwordDisplay.textContent = '••••••••';
                eyeIcon.className = 'fas fa-eye';
            }
        });
    }
});
</script>

<script>
    // No password functionality needed since credentials were provided in assignment email
</script>

<script src="assets/js/script.js"></script>

</body>
</html>
