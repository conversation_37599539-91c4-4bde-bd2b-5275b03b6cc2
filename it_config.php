<?php
require('database.php');

// Check if user is logged in
if (!isset($_SESSION['user'])) {
    header("Location: login.php");
    exit;
}

// Create IT config table if it doesn't exist
$create_table = "CREATE TABLE IF NOT EXISTS `it_config` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `config_key` varchar(255) NOT NULL,
    `config_value` TEXT,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `config_key` (`config_key`)
)";
mysqli_query($conn, $create_table);

// Handle form submission
if ($_POST) {
    $primary_emails = $_POST['primary_emails'] ?? '';
    $backup_emails = $_POST['backup_emails'] ?? '';
    
    // Update or insert primary emails
    $stmt = mysqli_prepare($conn, "INSERT INTO `it_config` (`config_key`, `config_value`) VALUES ('primary_emails', ?) ON DUPLICATE KEY UPDATE `config_value` = ?");
    mysqli_stmt_bind_param($stmt, 'ss', $primary_emails, $primary_emails);
    mysqli_stmt_execute($stmt);
    mysqli_stmt_close($stmt);
    
    // Update or insert backup emails
    $stmt = mysqli_prepare($conn, "INSERT INTO `it_config` (`config_key`, `config_value`) VALUES ('backup_emails', ?) ON DUPLICATE KEY UPDATE `config_value` = ?");
    mysqli_stmt_bind_param($stmt, 'ss', $backup_emails, $backup_emails);
    mysqli_stmt_execute($stmt);
    mysqli_stmt_close($stmt);
    
    $success_message = "IT configuration updated successfully!";
}

// Get current configuration
$primary_emails = '';
$backup_emails = '';

$result = mysqli_query($conn, "SELECT * FROM `it_config` WHERE `config_key` IN ('primary_emails', 'backup_emails')");
while ($row = mysqli_fetch_assoc($result)) {
    if ($row['config_key'] == 'primary_emails') {
        $primary_emails = $row['config_value'];
    } elseif ($row['config_key'] == 'backup_emails') {
        $backup_emails = $row['config_value'];
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IT Configuration</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <?php include('navbar.php'); ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include('sidebar.php'); ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">IT Configuration</h1>
                </div>

                <?php if (isset($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo $success_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-cog"></i> Email Notification Settings</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Primary IT Support Emails:</label>
                                        <textarea class="form-control" name="primary_emails" rows="4" placeholder="Enter email addresses, one per line"><?php echo htmlspecialchars($primary_emails); ?></textarea>
                                        <div class="form-text">These emails will receive laptop setup notifications as primary contacts.</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Backup IT Support Emails:</label>
                                        <textarea class="form-control" name="backup_emails" rows="4" placeholder="Enter email addresses, one per line"><?php echo htmlspecialchars($backup_emails); ?></textarea>
                                        <div class="form-text">These emails will be CC'd on laptop setup notifications as backup contacts.</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-12">
                                    <div class="alert alert-info">
                                        <h6><i class="fas fa-info-circle"></i> Email Priority:</h6>
                                        <ul class="mb-0">
                                            <li><strong>Primary emails</strong> are the main recipients who should handle the laptop setup</li>
                                            <li><strong>Backup emails</strong> are CC'd for awareness and can step in if primary contacts are unavailable</li>
                                            <li>All emails will include a link to assign specific team members to the onboarding task</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Save Configuration
                            </button>
                        </form>
                    </div>
                </div>

                <div class="card mt-4">
                    <div class="card-header">
                        <h5><i class="fas fa-envelope"></i> Email Template Preview</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-light">
                            <h6>Subject: Laptop Setup Required for New Joiner</h6>
                            <hr>
                            <p><strong>New joiner laptop setup required:</strong></p>
                            <ul>
                                <li><strong>Name:</strong> [Employee Name]</li>
                                <li><strong>Employee ID:</strong> [Employee ID]</li>
                                <li><strong>Department:</strong> [Department]</li>
                                <li><strong>Onboarding Date:</strong> [Date]</li>
                            </ul>
                            <p>Please click the link below to assign team members and track progress:</p>
                            <p><a href="#" class="btn btn-primary btn-sm">Manage Onboarding Workflow</a></p>
                            <hr>
                            <small class="text-muted">
                                <strong>Priority:</strong> Primary contacts should handle this request. Backup contacts are CC'd for awareness.
                            </small>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
