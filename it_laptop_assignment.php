<?php
require('database.php');
require_once 'MAILER/vendor/autoload.php';
use PHPMailer\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\Exception;

// Get parameters from URL
$onboarding_id = $_GET['id'] ?? '';
$access_token = $_GET['token'] ?? '';

if (!$onboarding_id || !$access_token) {
    echo "<script>alert('Invalid access link.'); window.close();</script>";
    exit;
}

// Verify access token (security measure)
$expected_token = hash('sha256', $onboarding_id . 'IT_LAPTOP_SETUP_2024');
if ($access_token !== $expected_token) {
    echo "<script>alert('Invalid or expired access token.'); window.close();</script>";
    exit;
}

// Ensure required columns exist
function ensure_column_exists($conn, $table, $column, $definition) {
    $result = mysqli_query($conn, "SHOW COLUMNS FROM `$table` LIKE '$column'");
    if (mysqli_num_rows($result) == 0) {
        mysqli_query($conn, "ALTER TABLE `$table` ADD COLUMN `$column` $definition");
    }
}

ensure_column_exists($conn, 'onboarding_list', 'it_primary_email', 'varchar(255) NULL');
ensure_column_exists($conn, 'onboarding_list', 'it_backup_email', 'varchar(255) NULL');
ensure_column_exists($conn, 'onboarding_list', 'it_remarks', 'TEXT NULL');
ensure_column_exists($conn, 'onboarding_list', 'laptop_email_sent', 'TINYINT(1) NOT NULL DEFAULT 0');
ensure_column_exists($conn, 'onboarding_list', 'it_override_token', 'varchar(255) NULL');
ensure_column_exists($conn, 'onboarding_list', 'it_override_email', 'varchar(255) NULL');



// Get onboarding record with related names
$stmt = mysqli_prepare($conn, "
    SELECT ol.*,
           dl.department_name,
           ml.manager_name,
           ofl.office_name as office_name_actual,
           ofl.location as office_location
    FROM `onboarding_list` ol
    LEFT JOIN `department_list` dl ON ol.department_id = dl.department_id
    LEFT JOIN `manager_list` ml ON ol.manager_id = ml.manager_id
    LEFT JOIN `office_list` ofl ON ol.office_id = ofl.office_id
    WHERE ol.onboarding_id = ?
");
mysqli_stmt_bind_param($stmt, 's', $onboarding_id);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);
$record = mysqli_fetch_assoc($result);
mysqli_stmt_close($stmt);

if (!$record) {
    echo "<script>alert('Onboarding record not found.'); window.close();</script>";
    exit;
}

// Check if IT members are already assigned - if yes, block access
if (!empty($record['it_primary_email'])) {
    $already_assigned = true;
} else {
    $already_assigned = false;
}

// Handle form submission
if ($_POST) {
    if (isset($_POST['assign_members'])) {
        // Handle custom email inputs
        $it_primary_email = $_POST['it_primary_email'] ?? '';
        if ($it_primary_email === 'custom') {
            $it_primary_email = $_POST['it_primary_custom'] ?? '';
        }

        $it_backup_email = $_POST['it_backup_email'] ?? '';
        if ($it_backup_email === 'custom') {
            $it_backup_email = $_POST['it_backup_custom'] ?? '';
        }

        $it_remarks = $_POST['it_remarks'] ?? '';

        $stmt = mysqli_prepare($conn, "UPDATE `onboarding_list` SET `it_primary_email` = ?, `it_backup_email` = ?, `it_remarks` = ? WHERE `onboarding_id` = ?");
        mysqli_stmt_bind_param($stmt, 'ssss', $it_primary_email, $it_backup_email, $it_remarks, $onboarding_id);
        mysqli_stmt_execute($stmt);
        mysqli_stmt_close($stmt);

        // Send calendar invites ONLY to assigned IT members (no assignment emails to them)
        send_calendar_invites_to_outlook($onboarding_id, $conn);

        // Note: Company account details email will be sent via cron job on onboarding date
        // Note: IT support notifications will be sent via cron job on onboarding date at configured time

        $success_message = "IT team members assigned successfully! Calendar invites have been sent. Company account details and IT support notifications will be sent automatically on the onboarding date at the configured time.";

        // Refresh record
        $stmt = mysqli_prepare($conn, "SELECT * FROM `onboarding_list` WHERE `onboarding_id` = ?");
        mysqli_stmt_bind_param($stmt, 's', $onboarding_id);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $record = mysqli_fetch_assoc($result);
        mysqli_stmt_close($stmt);
    }
    
    if (isset($_POST['complete_setup'])) {
        $stmt = mysqli_prepare($conn, "UPDATE `onboarding_list` SET `laptop_email_sent` = 1 WHERE `onboarding_id` = ?");
        mysqli_stmt_bind_param($stmt, 's', $onboarding_id);
        mysqli_stmt_execute($stmt);
        mysqli_stmt_close($stmt);
        
        // Send notification to HR that laptop setup is complete
        send_hr_laptop_completion_notification($onboarding_id, $conn);
        
        $completion_message = "Laptop setup marked as complete! HR team has been notified.";
        
        // Refresh record with related names
        $stmt = mysqli_prepare($conn, "
            SELECT ol.*,
                   dl.department_name,
                   ml.manager_name,
                   ofl.office_name as office_name_actual,
                   ofl.location as office_location
            FROM `onboarding_list` ol
            LEFT JOIN `department_list` dl ON ol.department_id = dl.department_id
            LEFT JOIN `manager_list` ml ON ol.manager_id = ml.manager_id
            LEFT JOIN `office_list` ofl ON ol.office_id = ofl.office_id
            WHERE ol.onboarding_id = ?
        ");
        mysqli_stmt_bind_param($stmt, 's', $onboarding_id);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $record = mysqli_fetch_assoc($result);
        mysqli_stmt_close($stmt);
    }

    // Handle admin override
    if (isset($_POST['admin_override'])) {
        $override_email = $_POST['override_email'] ?? '';

        if (!empty($override_email)) {
            // Generate new token and expire old ones
            $new_token = hash('sha256', $onboarding_id . 'IT_OVERRIDE_' . time());

            // Update the record with new token
            $stmt = mysqli_prepare($conn, "UPDATE `onboarding_list` SET `it_override_token` = ?, `it_override_email` = ? WHERE `onboarding_id` = ?");
            mysqli_stmt_bind_param($stmt, 'sss', $new_token, $override_email, $onboarding_id);
            mysqli_stmt_execute($stmt);
            mysqli_stmt_close($stmt);

            // Send override assignment email
            send_override_assignment_email($onboarding_id, $override_email, $new_token, $conn);

            $success_message = "Override assignment sent successfully to " . htmlspecialchars($override_email) . "! Previous tokens have been expired.";

            // Refresh record
            $stmt = mysqli_prepare($conn, "
                SELECT ol.*,
                       dl.department_name,
                       ml.manager_name,
                       ofl.office_name as office_name_actual,
                       ofl.location as office_location
                FROM `onboarding_list` ol
                LEFT JOIN `department_list` dl ON ol.department_id = dl.department_id
                LEFT JOIN `manager_list` ml ON ol.manager_id = ml.manager_id
                LEFT JOIN `office_list` ofl ON ol.office_id = ofl.office_id
                WHERE ol.onboarding_id = ?
            ");
            mysqli_stmt_bind_param($stmt, 's', $onboarding_id);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);
            $record = mysqli_fetch_assoc($result);
            mysqli_stmt_close($stmt);
        }
    }
}

// Function to send HR notification when laptop setup is complete
function send_hr_laptop_completion_notification($onboarding_id, $conn) {
    // Get employee record with related names
    $stmt = mysqli_prepare($conn, "
        SELECT ol.*,
               dl.department_name,
               ml.manager_name,
               ofl.office_name as office_name_actual,
               ofl.location as office_location
        FROM `onboarding_list` ol
        LEFT JOIN `department_list` dl ON ol.department_id = dl.department_id
        LEFT JOIN `manager_list` ml ON ol.manager_id = ml.manager_id
        LEFT JOIN `office_list` ofl ON ol.office_id = ofl.office_id
        WHERE ol.onboarding_id = ?
    ");
    mysqli_stmt_bind_param($stmt, 's', $onboarding_id);
    mysqli_stmt_execute($stmt);
    $res = mysqli_stmt_get_result($stmt);
    $rec = mysqli_fetch_assoc($res);
    mysqli_stmt_close($stmt);
    
    if (!$rec) return;
    
    function mailer(){
        $m = new PHPMailer(true);
        $m->isSMTP();
        $m->Host='smtp.office365.com';
        $m->SMTPAuth=true;
        $m->Username='<EMAIL>';
        $m->Password='&[i3F6}0hOw6';
        $m->SMTPSecure='tls';
        $m->Port=587;
        $m->setFrom('<EMAIL>','CLLXWARE');
        return $m;
    }
    
    try {
        // Get HR email configuration
        $hr_config = mysqli_query($conn, "SELECT * FROM `onboarding_email_config` WHERE `config_type` = 'hr_admin'");
        $hr_data = mysqli_fetch_assoc($hr_config);
        
        $hr_emails = $hr_data ? array_filter(array_map('trim', explode("\n", $hr_data['email_addresses']))) : ['<EMAIL>'];
        
        $workflow_link = (isset($_SERVER['HTTP_HOST']) ? ( (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://') . $_SERVER['HTTP_HOST'] . dirname($_SERVER['PHP_SELF']) ) : 'https://cis.cllsystems.com:9443/staging');
        $workflow_link = rtrim($workflow_link,'/')."/onboarding_workflow.php?id=".urlencode($rec['onboarding_id']);
        
        $email_subject = 'Laptop Setup Completed - ' . $rec['full_name'];
        $email_body = "Dear HR Team,\n\n";
        $email_body .= "The laptop setup has been completed for:\n\n";
        $email_body .= "Employee Information:\n";
        $email_body .= "- Name: " . $rec['full_name'] . "\n";
        $email_body .= "- Employee ID: " . $rec['employee_id'] . "\n";
        $email_body .= "- Department: " . ($rec['department_name'] ?? 'Not specified') . "\n";
        $email_body .= "- Designation: " . $rec['job_title'] . "\n";
        $email_body .= "- Primary IT Member: " . $rec['it_primary_member'] . "\n";
        $email_body .= "- Backup IT Member: " . $rec['it_backup_member'] . "\n";
        if (!empty($rec['it_remarks'])) {
            $email_body .= "- IT Remarks: " . $rec['it_remarks'] . "\n";
        }
        $email_body .= "\nNext Step: Please complete the HR checklist for this employee.\n\n";
        $email_body .= "Complete HR Checklist: " . $workflow_link . "\n\n";
        $email_body .= "Best regards,\nIT Support Team";
        
        $mail = mailer();
        foreach($hr_emails as $email) {
            $mail->addAddress($email);
        }
        $mail->isHTML(false);
        $mail->Subject = $email_subject;
        $mail->Body = $email_body;
        $mail->send();
        
    } catch (Exception $e) {
        // Log error but don't stop the process
        error_log('HR notification error: ' . $e->getMessage());
    }
}

// Function to send assignment emails to IT members
function send_it_assignment_emails($onboarding_id, $primary_email, $backup_email, $conn) {
    // Get employee record with related names
    $stmt = mysqli_prepare($conn, "
        SELECT ol.*,
               dl.department_name,
               ml.manager_name,
               ofl.office_name as office_name_actual,
               ofl.location as office_location
        FROM `onboarding_list` ol
        LEFT JOIN `department_list` dl ON ol.department_id = dl.department_id
        LEFT JOIN `manager_list` ml ON ol.manager_id = ml.manager_id
        LEFT JOIN `office_list` ofl ON ol.office_id = ofl.office_id
        WHERE ol.onboarding_id = ?
    ");
    mysqli_stmt_bind_param($stmt, 's', $onboarding_id);
    mysqli_stmt_execute($stmt);
    $res = mysqli_stmt_get_result($stmt);
    $rec = mysqli_fetch_assoc($res);
    mysqli_stmt_close($stmt);

    if (!$rec) return;

    try {
        // Create secure completion tokens for each IT member
        $primary_token = hash('sha256', $onboarding_id . $primary_email . 'IT_COMPLETION_2024');
        $backup_token = $backup_email ? hash('sha256', $onboarding_id . $backup_email . 'IT_COMPLETION_2024') : '';

        $base_url = (isset($_SERVER['HTTP_HOST']) ? ( (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://') . $_SERVER['HTTP_HOST'] . dirname($_SERVER['PHP_SELF']) ) : 'https://cis.cllsystems.com:9443/staging');

        $primary_completion_link = rtrim($base_url,'/')."/it_completion.php?id=".urlencode($onboarding_id)."&email=".urlencode($primary_email)."&token=".urlencode($primary_token);
        $backup_completion_link = $backup_email ? rtrim($base_url,'/')."/it_completion.php?id=".urlencode($onboarding_id)."&email=".urlencode($backup_email)."&token=".urlencode($backup_token) : '';

        $mail = new PHPMailer(true);
        $mail->isSMTP();
        $mail->Host='smtp.office365.com';
        $mail->SMTPAuth=true;
        $mail->Username='<EMAIL>';
        $mail->Password='&[i3F6}0hOw6';
        $mail->SMTPSecure='tls';
        $mail->Port=587;
        $mail->setFrom('<EMAIL>','CLLXWARE');

        // Send to primary IT member
        if ($primary_email) {
            $mail->clearAddresses();
            $mail->addAddress($primary_email);

            $mail->Subject = 'Laptop Setup Assignment - ' . $rec['full_name'] . ' (Primary)';
            // Create workflow link
            $workflow_link = rtrim($base_url,'/')."/onboarding_workflow.php?id=".urlencode($onboarding_id);

            $mail->Body = "Dear IT Team Member,\n\n";
            $mail->Body .= "You have been assigned as the PRIMARY member for laptop setup:\n\n";
            $mail->Body .= "Employee Information:\n";
            $mail->Body .= "- Name: " . $rec['full_name'] . "\n";
            $mail->Body .= "- Employee ID: " . $rec['employee_id'] . "\n";
            $mail->Body .= "- Department: " . ($rec['department_name'] ?? 'Not specified') . "\n";
            $mail->Body .= "- Designation: " . $rec['job_title'] . "\n";
            $mail->Body .= "- Reporting Manager: " . ($rec['manager_name'] ?? 'Not specified') . "\n";
            $mail->Body .= "- Office Location: " . ($rec['office_name_actual'] ?? 'Not specified') . "\n";
            $mail->Body .= "- Onboarding Date: " . $rec['onboarding_date'] . "\n";
            $mail->Body .= "- Personal Email: " . $rec['personal_email'] . "\n";
            $mail->Body .= "- Company Email: " . $rec['company_email'] . "\n";
            $mail->Body .= "- Company Password: " . base64_decode($rec['company_email_password']) . "\n\n";
            $mail->Body .= "After completing the laptop setup, please click the link below to mark it as complete:\n";
            $mail->Body .= $primary_completion_link . "\n\n";
            $mail->Body .= "Please click the link below to manage the onboarding workflow:\n";
            $mail->Body .= $workflow_link . "\n\n";
            $mail->Body .= "Note: This link will expire after the setup is marked complete.\n\n";
            $mail->Body .= "Best regards,\nIT Support Team";
            $mail->isHTML(false);
            $mail->send();
        }

        // Send to backup IT member
        if ($backup_email) {
            $mail->clearAddresses();
            $mail->addAddress($backup_email);

            $mail->Subject = 'Laptop Setup Assignment - ' . $rec['full_name'] . ' (Backup)';
            $mail->Body = "Dear IT Team Member,\n\n";
            $mail->Body .= "You have been assigned as the BACKUP member for laptop setup:\n\n";
            $mail->Body .= "Employee Information:\n";
            $mail->Body .= "- Name: " . $rec['full_name'] . "\n";
            $mail->Body .= "- Employee ID: " . $rec['employee_id'] . "\n";
            $mail->Body .= "- Department: " . ($rec['department_name'] ?? 'Not specified') . "\n";
            $mail->Body .= "- Designation: " . $rec['job_title'] . "\n";
            $mail->Body .= "- Reporting Manager: " . ($rec['manager_name'] ?? 'Not specified') . "\n";
            $mail->Body .= "- Office Location: " . ($rec['office_name_actual'] ?? 'Not specified') . "\n";
            $mail->Body .= "- Onboarding Date: " . $rec['onboarding_date'] . "\n";
            $mail->Body .= "- Personal Email: " . $rec['personal_email'] . "\n";
            $mail->Body .= "- Company Email: " . $rec['company_email'] . "\n";
            $mail->Body .= "- Company Password: " . base64_decode($rec['company_email_password']) . "\n\n";
            $mail->Body .= "You are the backup support for this setup. If needed, you can also mark the setup as complete:\n";
            $mail->Body .= $backup_completion_link . "\n\n";
            $mail->Body .= "Please click the link below to manage the onboarding workflow:\n";
            $mail->Body .= $workflow_link . "\n\n";
            $mail->Body .= "Note: This link will expire after the setup is marked complete.\n\n";
            $mail->Body .= "Best regards,\nIT Support Team";
            $mail->isHTML(false);
            $mail->send();
        }

    } catch (Exception $e) {
        error_log('IT assignment email error: ' . $e->getMessage());
    }
}

// Function to send override assignment email
function send_override_assignment_email($onboarding_id, $override_email, $token, $conn) {
    // Get employee record with related names
    $stmt = mysqli_prepare($conn, "
        SELECT ol.*,
               dl.department_name,
               ml.manager_name,
               ofl.office_name as office_name_actual,
               ofl.location as office_location
        FROM `onboarding_list` ol
        LEFT JOIN `department_list` dl ON ol.department_id = dl.department_id
        LEFT JOIN `manager_list` ml ON ol.manager_id = ml.manager_id
        LEFT JOIN `office_list` ofl ON ol.office_id = ofl.office_id
        WHERE ol.onboarding_id = ?
    ");
    mysqli_stmt_bind_param($stmt, 's', $onboarding_id);
    mysqli_stmt_execute($stmt);
    $res = mysqli_stmt_get_result($stmt);
    $rec = mysqli_fetch_assoc($res);
    mysqli_stmt_close($stmt);

    if (!$rec) return;

    try {
        $mail = new PHPMailer(true);
        $mail->isSMTP();
        $mail->Host='smtp.office365.com';
        $mail->SMTPAuth=true;
        $mail->Username='<EMAIL>';
        $mail->Password='&[i3F6}0hOw6';
        $mail->SMTPSecure='tls';
        $mail->Port=587;
        $mail->setFrom('<EMAIL>','CLLXWARE');

        $mail->addAddress($override_email);

        // Create completion link with new token
        $base_url = (isset($_SERVER['HTTP_HOST']) ? ( (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://') . $_SERVER['HTTP_HOST'] . dirname($_SERVER['PHP_SELF']) ) : 'https://cis.cllsystems.com:9443/staging');
        $completion_link = rtrim($base_url,'/')."/it_completion.php?id=".urlencode($rec['onboarding_id'])."&token=".urlencode($token)."&email=".urlencode($override_email);

        $mail->Subject = 'URGENT: Laptop Setup Assignment (Override) - ' . $rec['full_name'];
        $mail->Body = "Dear IT Team Member,\n\n";
        $mail->Body .= "You have been assigned for laptop setup (Admin Override):\n\n";
        $mail->Body .= "Employee Information:\n";
        $mail->Body .= "- Name: " . $rec['full_name'] . "\n";
        $mail->Body .= "- Employee ID: " . $rec['employee_id'] . "\n";
        $mail->Body .= "- Department: " . ($rec['department_name'] ?? 'Not specified') . "\n";
        $mail->Body .= "- Designation: " . $rec['job_title'] . "\n";
        $mail->Body .= "- Personal Email: " . $rec['personal_email'] . "\n";
        $mail->Body .= "- Company Email: " . $rec['company_email'] . "\n";
        $mail->Body .= "- Company Password: " . base64_decode($rec['company_email_password']) . "\n\n";
        $mail->Body .= "IMPORTANT: Previous assignment tokens have been expired. Only this link is now valid.\n\n";
        $mail->Body .= "After completing the laptop setup, please click the link below to mark it as complete:\n";
        $mail->Body .= $completion_link . "\n\n";
        $mail->Body .= "Note: This link will expire after the setup is marked complete.\n\n";
        $mail->Body .= "Best regards,\nIT Support Team";
        $mail->isHTML(false);
        $mail->send();

    } catch (Exception $e) {
        error_log('Override assignment email error: ' . $e->getMessage());
    }
}

// Function to send calendar invites directly to Outlook calendars
function send_calendar_invites_to_outlook($onboarding_id, $conn) {
    // Set timezone to Malaysia
    date_default_timezone_set('Asia/Kuala_Lumpur');

    // Get employee record with related names
    $stmt = mysqli_prepare($conn, "
        SELECT ol.*,
               dl.department_name,
               ml.manager_name,
               ml.email_address as manager_email,
               ofl.office_name as office_name_actual,
               ofl.location as office_location
        FROM `onboarding_list` ol
        LEFT JOIN `department_list` dl ON ol.department_id = dl.department_id
        LEFT JOIN `manager_list` ml ON ol.manager_id = ml.manager_id
        LEFT JOIN `office_list` ofl ON ol.office_id = ofl.office_id
        WHERE ol.onboarding_id = ?
    ");
    mysqli_stmt_bind_param($stmt, 's', $onboarding_id);
    mysqli_stmt_execute($stmt);
    $res = mysqli_stmt_get_result($stmt);
    $rec = mysqli_fetch_assoc($res);
    mysqli_stmt_close($stmt);

    if (!$rec) return;

    // Get time configuration from cron config (default onboarding session time)
    $time_config = mysqli_query($conn, "SELECT cron_time FROM `onboarding_cron_config` WHERE config_type = 'cron_reminder' LIMIT 1");
    $time_data = mysqli_fetch_assoc($time_config);
    $base_time = $time_data ? $time_data['cron_time'] : '09:00';

    // Calculate session time (1 hour duration)
    $start_time = $base_time . ':00';
    $end_time = date('H:i:s', strtotime($start_time . ' +1 hour'));
    $timezone = 'Asia/Kuala_Lumpur';

    // Get HR email configuration
    $hr_config = mysqli_query($conn, "SELECT * FROM `onboarding_email_config` WHERE `config_type` = 'hr_calendar'");
    $hr_data = mysqli_fetch_assoc($hr_config);
    $hr_emails = $hr_data ? array_filter(array_map('trim', explode("\n", $hr_data['email_addresses']))) : ['<EMAIL>'];

    // Create calendar event details with proper timezone
    $event_date = date('Y-m-d', strtotime($rec['onboarding_date']));
    $event_start = $event_date . 'T' . $start_time;
    $event_end = $event_date . 'T' . $end_time;
    $event_uid = 'onboarding-' . $rec['onboarding_id'] . '@cllsystems.com';

    // Format times for display
    $display_start_time = date('g:i A', strtotime($start_time));
    $display_end_time = date('g:i A', strtotime($end_time));

    // Recipients for calendar invite
    $calendar_recipients = [];

    // Add employee personal email
    $calendar_recipients[] = [
        'email' => $rec['personal_email'],
        'name' => $rec['full_name'],
        'role' => 'Employee'
    ];

    // Add reporting manager if available
    if (!empty($rec['manager_email'])) {
        $calendar_recipients[] = [
            'email' => $rec['manager_email'],
            'name' => $rec['manager_name'] ?? 'Manager',
            'role' => 'Manager'
        ];
    }

    // Add HR team
    foreach($hr_emails as $hr_email) {
        $calendar_recipients[] = [
            'email' => $hr_email,
            'name' => 'HR Team',
            'role' => 'HR'
        ];
    }

    // Add IT members
    if (!empty($rec['it_primary_email'])) {
        $calendar_recipients[] = [
            'email' => $rec['it_primary_email'],
            'name' => 'Primary IT Member',
            'role' => 'IT Primary'
        ];
    }

    if (!empty($rec['it_backup_email'])) {
        $calendar_recipients[] = [
            'email' => $rec['it_backup_email'],
            'name' => 'Backup IT Member',
            'role' => 'IT Backup'
        ];
    }

    try {
        // Send calendar invite to each recipient
        foreach($calendar_recipients as $recipient) {
            $mail = new PHPMailer(true);
            $mail->isSMTP();
            $mail->Host='smtp.office365.com';
            $mail->SMTPAuth=true;
            $mail->Username='<EMAIL>';
            $mail->Password='&[i3F6}0hOw6';
            $mail->SMTPSecure='tls';
            $mail->Port=587;
            $mail->setFrom('<EMAIL>','CLLXWARE');

            $mail->addAddress($recipient['email']);

            // Create calendar content that Outlook will recognize
            $mail->Subject = 'Onboarding Session: ' . $rec['full_name'] . ' - ' . $rec['onboarding_date'];
            $mail->isHTML(true);

            // Create HTML email with calendar data - include credentials for employee only
            if ($recipient['role'] === 'Employee') {
                // Employee gets company credentials
                $mail->Body = "
                <html>
                <head>
                    <meta charset='UTF-8'>
                </head>
                <body>
                    <h3>Onboarding Session Invitation</h3>
                    <p>Dear " . htmlspecialchars($recipient['name']) . ",</p>
                    <p>You are invited to the onboarding session:</p>

                    <table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>
                        <tr><td><strong>Employee Name:</strong></td><td>" . htmlspecialchars($rec['full_name']) . "</td></tr>
                        <tr><td><strong>Employee ID:</strong></td><td>" . htmlspecialchars($rec['employee_id']) . "</td></tr>
                        <tr><td><strong>Department:</strong></td><td>" . htmlspecialchars($rec['department_name'] ?? 'Not specified') . "</td></tr>
                        <tr><td><strong>Designation:</strong></td><td>" . htmlspecialchars($rec['job_title']) . "</td></tr>
                        <tr><td><strong>Date:</strong></td><td>" . htmlspecialchars($rec['onboarding_date']) . "</td></tr>
                        <tr><td><strong>Time:</strong></td><td>" . $display_start_time . " - " . $display_end_time . "</td></tr>
                        <tr><td><strong>Location:</strong></td><td>" . htmlspecialchars($rec['office_name_actual'] ?? 'Not specified') . "</td></tr>

                    <div style='background-color: #e8f5e8; padding: 15px; border-radius: 8px; margin: 20px 0;'>
                        <h4>Your Company Account Details:</h4>
                        <table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>
                            <tr><td><strong>Company Email:</strong></td><td>" . htmlspecialchars($rec['company_email']) . "</td></tr>
                            <tr><td><strong>Company Password:</strong></td><td>" . htmlspecialchars(base64_decode($rec['company_email_password'])) . "</td></tr>
                        </table>
                        <p><small><strong>Important:</strong> Please keep these credentials secure and change your password after first login if required.</small></p>
                    </div>

                    <p><strong>Your Role:</strong> " . htmlspecialchars($recipient['role']) . "</p>
                    <p>Please be prepared for the onboarding session.</p>

                    <p>Best regards,<br>HR Team</p>
                </body>
                </html>";
            } else {
                // Other recipients (HR, IT, Manager) get standard invite without credentials
                $mail->Body = "
                <html>
                <head>
                    <meta charset='UTF-8'>
                </head>
                <body>
                    <h3>Onboarding Session Invitation</h3>
                    <p>Dear " . htmlspecialchars($recipient['name']) . ",</p>
                    <p>You are invited to the onboarding session for our new employee:</p>

                    <table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>
                        <tr><td><strong>Employee Name:</strong></td><td>" . htmlspecialchars($rec['full_name']) . "</td></tr>
                        <tr><td><strong>Employee ID:</strong></td><td>" . htmlspecialchars($rec['employee_id']) . "</td></tr>
                        <tr><td><strong>Department:</strong></td><td>" . htmlspecialchars($rec['department_name'] ?? 'Not specified') . "</td></tr>
                        <tr><td><strong>Designation:</strong></td><td>" . htmlspecialchars($rec['job_title']) . "</td></tr>
                        <tr><td><strong>Date:</strong></td><td>" . htmlspecialchars($rec['onboarding_date']) . "</td></tr>
                        <tr><td><strong>Time:</strong></td><td>" . $display_start_time . " - " . $display_end_time . "</td></tr>
                        <tr><td><strong>Location:</strong></td><td>" . htmlspecialchars($rec['office_name_actual'] ?? 'Not specified') . "</td></tr>
                    </table>

                    <p><strong>Your Role:</strong> " . htmlspecialchars($recipient['role']) . "</p>
                    <p>Please be prepared for the onboarding session.</p>

                    <p>Best regards,<br>HR Team</p>
                </body>
                </html>";
            }

            // Add calendar attachment that Outlook will recognize
            $ics_content = "BEGIN:VCALENDAR\r\n";
            $ics_content .= "VERSION:2.0\r\n";
            $ics_content .= "PRODID:-//CLLXWARE//Onboarding System//EN\r\n";
            $ics_content .= "METHOD:REQUEST\r\n";
            $ics_content .= "BEGIN:VEVENT\r\n";
            $ics_content .= "UID:" . $event_uid . "\r\n";
            $ics_content .= "DTSTAMP:" . gmdate('Ymd\THis\Z') . "\r\n";
            // Convert to Malaysia timezone for ICS
            $malaysia_tz = new DateTimeZone('Asia/Kuala_Lumpur');
            $start_dt = new DateTime($event_start, $malaysia_tz);
            $end_dt = new DateTime($event_end, $malaysia_tz);

            $ics_content .= "DTSTART;TZID=Asia/Kuala_Lumpur:" . $start_dt->format('Ymd\THis') . "\r\n";
            $ics_content .= "DTEND;TZID=Asia/Kuala_Lumpur:" . $end_dt->format('Ymd\THis') . "\r\n";
            $ics_content .= "SUMMARY:Onboarding Session - " . $rec['full_name'] . "\r\n";
            $ics_content .= "DESCRIPTION:Onboarding session for " . $rec['full_name'] . " (" . $rec['employee_id'] . ")\r\n";
            $ics_content .= "LOCATION:" . ($rec['office_location'] ?? $rec['office_name_actual'] ?? 'Office') . "\r\n";
            $ics_content .= "STATUS:CONFIRMED\r\n";
            $ics_content .= "SEQUENCE:0\r\n";
            $ics_content .= "ATTENDEE;ROLE=REQ-PARTICIPANT;PARTSTAT=NEEDS-ACTION;CN=" . $recipient['name'] . ":MAILTO:" . $recipient['email'] . "\r\n";
            $ics_content .= "ORGANIZER;CN=CLLXWARE System:MAILTO:<EMAIL>\r\n";
            $ics_content .= "END:VEVENT\r\n";
            $ics_content .= "END:VCALENDAR\r\n";

            // Add as attachment with proper MIME type for Outlook recognition
            $mail->addStringAttachment($ics_content, 'invite.ics', 'base64', 'text/calendar; method=REQUEST');

            $mail->send();
        }

    } catch (Exception $e) {
        error_log('Calendar invite error: ' . $e->getMessage());
    }
}

// Note: Employee company account details email is now handled by the cron job (onboarding_cron.php)
// This ensures emails are sent on the actual onboarding date at the configured time
function send_employee_assignment_email_DISABLED($onboarding_id, $conn) {
    // Get employee record
    $stmt = mysqli_prepare($conn, "
        SELECT ol.*,
               dl.department_name,
               ml.manager_name,
               ofl.office_name as office_name_actual,
               ofl.location as office_location
        FROM `onboarding_list` ol
        LEFT JOIN `department_list` dl ON ol.department_id = dl.department_id
        LEFT JOIN `manager_list` ml ON ol.manager_id = ml.manager_id
        LEFT JOIN `office_list` ofl ON ol.office_id = ofl.office_id
        WHERE ol.onboarding_id = ?
    ");
    mysqli_stmt_bind_param($stmt, 's', $onboarding_id);
    mysqli_stmt_execute($stmt);
    $res = mysqli_stmt_get_result($stmt);
    $rec = mysqli_fetch_assoc($res);
    mysqli_stmt_close($stmt);

    if (!$rec) return;

    try {
        $mail = new PHPMailer(true);
        $mail->isSMTP();
        $mail->Host='smtp.office365.com';
        $mail->SMTPAuth=true;
        $mail->Username='<EMAIL>';
        $mail->Password='&[i3F6}0hOw6';
        $mail->SMTPSecure='tls';
        $mail->Port=587;
        $mail->setFrom('<EMAIL>','CLLXWARE');

        $mail->addAddress($rec['personal_email']);

        $mail->Subject = 'Your Company Account Details - ' . $rec['full_name'];
        $mail->isHTML(true);

        $mail->Body = "
        <html>
        <head>
            <meta charset='UTF-8'>
        </head>
        <body>
            <h3>Welcome to the Team!</h3>
            <p>Dear " . htmlspecialchars($rec['full_name']) . ",</p>
            <p>Your company account has been set up. Here are your login details:</p>

            <div style='background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>
                <h4>Your Company Account Details:</h4>
                <p><strong>Company Email:</strong> " . htmlspecialchars($rec['company_email']) . "</p>
                <p><strong>Company Password:</strong> " . htmlspecialchars(base64_decode($rec['company_email_password'])) . "</p>
                <p><strong>Employee ID:</strong> " . htmlspecialchars($rec['employee_id']) . "</p>
                <p><strong>Department:</strong> " . htmlspecialchars($rec['department_name'] ?? 'Not specified') . "</p>
                <p><strong>Designation:</strong> " . htmlspecialchars($rec['job_title']) . "</p>
            </div>

            <div style='background-color: #fff3cd; padding: 15px; border-radius: 8px; margin: 20px 0;'>
                <h4>Important Information:</h4>
                <p>• IT support has been assigned for your laptop setup</p>
                <p>• You will receive a calendar invite for your onboarding session</p>
                <p>• Please keep your login credentials secure</p>
                <p>• Change your password after first login if required</p>
            </div>

            <p>If you have any questions, please contact HR or IT support.</p>
            <p>Best regards,<br>HR Team</p>
        </body>
        </html>";

        $mail->send();

    } catch (Exception $e) {
        error_log('Employee assignment email error: ' . $e->getMessage());
    }
}

// Note: IT support notifications are now handled by the cron job (onboarding_cron.php)
// This ensures emails are sent on the actual onboarding date at the configured time
function schedule_it_support_notification_DISABLED($onboarding_id, $conn) {
    // Set timezone to Malaysia
    date_default_timezone_set('Asia/Kuala_Lumpur');

    // Get employee record
    $stmt = mysqli_prepare($conn, "
        SELECT ol.*,
               dl.department_name,
               ml.manager_name,
               ofl.office_name as office_name_actual,
               ofl.location as office_location
        FROM `onboarding_list` ol
        LEFT JOIN `department_list` dl ON ol.department_id = dl.department_id
        LEFT JOIN `manager_list` ml ON ol.manager_id = ml.manager_id
        LEFT JOIN `office_list` ofl ON ol.office_id = ofl.office_id
        WHERE ol.onboarding_id = ?
    ");
    mysqli_stmt_bind_param($stmt, 's', $onboarding_id);
    mysqli_stmt_execute($stmt);
    $res = mysqli_stmt_get_result($stmt);
    $rec = mysqli_fetch_assoc($res);
    mysqli_stmt_close($stmt);

    if (!$rec) return;

    // Get IT support configuration
    $config_query = "SELECT * FROM `onboarding_email_config` WHERE `config_type` = 'it_primary'";
    $config_result = mysqli_query($conn, $config_query);
    $config = mysqli_fetch_assoc($config_result);

    if (!$config || empty($config['email_addresses'])) {
        error_log('IT support email addresses not configured');
        return;
    }

    // Get assignment time from config (default 09:00 if not set)
    $assignment_time = $config['cron_time'] ?? '09:00:00';

    // Combine onboarding date with assignment time
    $onboarding_date = $rec['onboarding_date'];
    $scheduled_datetime = $onboarding_date . ' ' . $assignment_time;

    // Convert to timestamp for comparison
    $scheduled_timestamp = strtotime($scheduled_datetime);
    $current_timestamp = time();

    // If the scheduled time is now or in the past, send immediately
    // If it's in the future, we would normally schedule it (but for now, send immediately for testing)
    if ($scheduled_timestamp <= $current_timestamp) {
        send_it_support_notification($onboarding_id, $conn);
    } else {
        // For future implementation: schedule the email to be sent at the specified time
        // For now, we'll send immediately but log the intended schedule time
        error_log("IT notification scheduled for: $scheduled_datetime (Malaysia time)");
        send_it_support_notification($onboarding_id, $conn);
    }
}

// Function to send notification to IT support team
function send_it_support_notification($onboarding_id, $conn) {
    // Set timezone to Malaysia
    date_default_timezone_set('Asia/Kuala_Lumpur');
    // Get employee record
    $stmt = mysqli_prepare($conn, "
        SELECT ol.*,
               dl.department_name,
               ml.manager_name,
               ofl.office_name as office_name_actual,
               ofl.location as office_location
        FROM `onboarding_list` ol
        LEFT JOIN `department_list` dl ON ol.department_id = dl.department_id
        LEFT JOIN `manager_list` ml ON ol.manager_id = ml.manager_id
        LEFT JOIN `office_list` ofl ON ol.office_id = ofl.office_id
        WHERE ol.onboarding_id = ?
    ");
    mysqli_stmt_bind_param($stmt, 's', $onboarding_id);
    mysqli_stmt_execute($stmt);
    $res = mysqli_stmt_get_result($stmt);
    $rec = mysqli_fetch_assoc($res);
    mysqli_stmt_close($stmt);

    if (!$rec) return;

    // Get IT support configuration
    $config_query = "SELECT * FROM `onboarding_email_config` WHERE `config_type` = 'it_support'";
    $config_result = mysqli_query($conn, $config_query);
    $config = mysqli_fetch_assoc($config_result);

    if (!$config || empty($config['email_addresses'])) {
        error_log('IT support email addresses not configured');
        return;
    }

    // Get IT support emails (send to all IT support members since assigned members only get calendar invites)
    $it_emails = array_filter(array_map('trim', explode("\n", $config['email_addresses'])));

    if (empty($it_emails)) {
        error_log('No valid IT support email addresses found');
        return;
    }

    // Get time configuration for display (Malaysia timezone)
    $assignment_time = $config['cron_time'] ?? '09:00';
    $start_time = $assignment_time . ':00';
    $end_time = date('H:i:s', strtotime($start_time . ' +1 hour'));
    $display_start_time = date('g:i A', strtotime($start_time)) . ' (Malaysia Time)';
    $display_end_time = date('g:i A', strtotime($end_time)) . ' (Malaysia Time)';

    try {
        $mail = new PHPMailer(true);
        $mail->isSMTP();
        $mail->Host='smtp.office365.com';
        $mail->SMTPAuth=true;
        $mail->Username='<EMAIL>';
        $mail->Password='&[i3F6}0hOw6';
        $mail->SMTPSecure='tls';
        $mail->Port=587;
        $mail->setFrom('<EMAIL>','CLLXWARE');

        // Send to all IT support emails
        foreach ($it_emails as $it_email) {
            $mail->clearAddresses();
            $mail->addAddress($it_email);

            // Generate completion token for this IT member
            $completion_token = hash('sha256', $onboarding_id . $it_email . 'IT_COMPLETION_2024');
            $base_url = (isset($_SERVER['HTTP_HOST']) ? ( (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://') . $_SERVER['HTTP_HOST'] . dirname($_SERVER['PHP_SELF']) ) : 'https://cis.cllsystems.com:9443/staging');
            $completion_link = rtrim($base_url,'/')."/it_completion.php?id=".urlencode($onboarding_id)."&email=".urlencode($it_email)."&token=".urlencode($completion_token);

            $email_subject = $config['email_subject'] ?? 'Laptop Setup Required for New Employee';
            $email_subject = str_replace('{employee_name}', $rec['full_name'], $email_subject);
            $mail->Subject = $email_subject;
            $mail->isHTML(true);

            // Use configured template or default
            $email_template = $config['email_template'] ?? 'Dear IT Support Team,

A new employee requires laptop setup:

Employee Name: {employee_name}
Employee ID: {employee_id}
Department: {department}
Designation: {designation}
Onboarding Date: {onboarding_date}
Time: {session_time}
Personal Email: {personal_email}

Please complete the laptop setup and mark it as complete using the link below.

Best regards,
HR Team';

            // Create workflow link
            $base_url = (isset($_SERVER['HTTP_HOST']) ? ( (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://') . $_SERVER['HTTP_HOST'] . dirname($_SERVER['PHP_SELF']) ) : 'https://cis.cllsystems.com:9443/staging');
            $workflow_link = rtrim($base_url,'/')."/onboarding_workflow.php?id=".urlencode($onboarding_id);

            // Replace template variables
            $email_body = str_replace('{employee_name}', htmlspecialchars($rec['full_name']), $email_template);
            $email_body = str_replace('{employee_id}', htmlspecialchars($rec['employee_id']), $email_body);
            $email_body = str_replace('{department}', htmlspecialchars($rec['department_name'] ?? 'Not specified'), $email_body);
            $email_body = str_replace('{designation}', htmlspecialchars($rec['job_title']), $email_body);
            $email_body = str_replace('{onboarding_date}', htmlspecialchars($rec['onboarding_date']), $email_body);
            $email_body = str_replace('{session_time}', $display_start_time . " - " . $display_end_time, $email_body);
            $email_body = str_replace('{company_email}', htmlspecialchars($rec['company_email']), $email_body);
            $email_body = str_replace('{company_password}', htmlspecialchars(base64_decode($rec['company_email_password'])), $email_body);
            $email_body = str_replace('{personal_email}', htmlspecialchars($rec['personal_email']), $email_body);
            $email_body = str_replace('{reporting_manager}', htmlspecialchars($rec['manager_name'] ?? 'Not specified'), $email_body);
            $email_body = str_replace('{office_name}', htmlspecialchars($rec['office_name_actual'] ?? 'Not specified'), $email_body);
            $email_body = str_replace('{workflow_link}', $workflow_link, $email_body);
            $email_body = str_replace('{completion_link}', $completion_link, $email_body);

            $mail->Body = "
            <html>
            <body>
                " . nl2br(htmlspecialchars($email_body)) . "

                <div style='background-color: #f8f9fa; padding: 15px; border-left: 4px solid #28a745; margin: 20px 0;'>
                    <h4>Action Required:</h4>
                    <p>Please complete the laptop setup and click the link below to mark it as complete:</p>
                    <p><a href='" . $completion_link . "' style='background-color: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Mark Setup Complete</a></p>
                </div>
            </body>
            </html>";

            $mail->send();
        }

    } catch (Exception $e) {
        error_log('IT support notification email error: ' . $e->getMessage());
    }
}

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IT Laptop Assignment - <?php echo htmlspecialchars($record['full_name']); ?></title>
    <link rel="shortcut icon" type="image/x-icon" href="assets/img/favicon.jpg">
    <link rel="stylesheet" href="assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="assets/css/animate.css">
    <link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">
    <link rel="stylesheet" href="assets/css/dataTables.bootstrap4.min.css">
    <link rel="stylesheet" href="assets/plugins/fontawesome/css/fontawesome.min.css">
    <link rel="stylesheet" href="assets/plugins/fontawesome/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">

    <style>
        .form-group { position: relative; }
        .form-group input,
        .form-group textarea,
        .form-group select { padding-left: 12px; }
        .card { border: 1px solid #f0f0f0; padding: 20px; box-shadow: 0 4px 8px rgba(0,0,0,0.1); margin-bottom: 20px; }
        .section-header { 
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); 
            color: white; 
            padding: 15px; 
            border-radius: 8px; 
            margin-bottom: 20px;
            text-align: center;
        }
        .employee-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #28a745;
            margin-bottom: 20px;
        }
        .credentials-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .password-field {
            font-family: monospace;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 8px 12px;
            border-radius: 4px;
            display: inline-block;
            min-width: 200px;
        }
        .btn-complete {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            font-weight: bold;
        }
        .btn-complete:hover {
            background: linear-gradient(135deg, #218838 0%, #1ea085 100%);
            color: white;
        }
    </style>
</head>
<body>

<div class="main-wrapper">
    <div class="page-wrapper">
        <div class="content">
            <div class="page-header">
                <div class="page-title form-header text-start">
                    <h4>IT Laptop Assignment</h4>
                    <p>Assign team members for laptop setup - <?php echo htmlspecialchars($record['full_name']); ?></p>
                </div>
            </div>

            <?php if (isset($success_message)): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <?php endif; ?>

            <?php if (isset($completion_message)): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle"></i> <?php echo $completion_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <?php endif; ?>

            <!-- Employee Information -->
            <div class="card">
                <div class="card-body">
                    <div class="section-header">
                        <h5><i class="fas fa-user me-2"></i>Employee Information</h5>
                    </div>
                    
                    <div class="employee-info">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>Name:</strong> <?php echo htmlspecialchars($record['full_name']); ?></p>
                                <p><strong>Employee ID:</strong> <?php echo htmlspecialchars($record['employee_id']); ?></p>
                                <p><strong>Department:</strong> <?php echo htmlspecialchars($record['department_name'] ?? 'Not specified'); ?></p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Designation:</strong> <?php echo htmlspecialchars($record['job_title']); ?></p>
                                <p><strong>Onboarding Date:</strong> <?php echo htmlspecialchars($record['onboarding_date']); ?></p>
                                <p><strong>Personal Email:</strong> <?php echo htmlspecialchars($record['personal_email']); ?></p>
                                <p><strong>Company Email:</strong> <?php echo htmlspecialchars($record['company_email'] ?? 'Not set'); ?></p>
                            </div>
                        </div>
                    </div>

                    <!-- Company Email Status -->
                    <?php if (!empty($record['company_email'])): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> <strong>Company email has been created successfully.</strong> IT members will receive the credentials via email.
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- IT Team Assignment -->
            <?php if (!$already_assigned): ?>
            <div class="card">
                <div class="card-body">
                    <div class="section-header">
                        <h5><i class="fas fa-users me-2"></i>IT Team Assignment</h5>
                    </div>

                    <form method="POST">
                        <div class="row">
                            <div class="col-lg-6 col-sm-6 col-12">
                                <div class="form-group">
                                    <label>Primary IT Member *</label>
                                    <select class="form-control select" name="it_primary_email" id="primary_it_select" required>
                                        <option value="">Choose Primary IT Member</option>
                                        <?php
                                        // Fetch IT members from database
                                        $it_members_sql = "SELECT email_address, manager_name FROM `manager_list` WHERE `department_id` = (SELECT department_id FROM department_list WHERE department_name = 'IT' LIMIT 1) ORDER BY manager_name ASC";
                                        $it_members_result = mysqli_query($conn, $it_members_sql);
                                        if ($it_members_result && mysqli_num_rows($it_members_result) > 0) {
                                            while ($it_member = mysqli_fetch_assoc($it_members_result)) {
                                                $selected = ($record['it_primary_email'] == $it_member['email_address']) ? 'selected' : '';
                                                echo "<option value='" . htmlspecialchars($it_member['email_address']) . "' $selected>" . htmlspecialchars($it_member['manager_name']) . " (" . htmlspecialchars($it_member['email_address']) . ")</option>";
                                            }
                                        }
                                        ?>
                                        <option value="custom">Custom Email...</option>
                                    </select>
                                    <input type="email" class="form-control mt-2" name="it_primary_custom" id="primary_custom_email" placeholder="Enter custom email" style="display: none;">
                                    <small class="form-text text-muted">Main person responsible for laptop setup</small>
                                </div>
                            </div>

                            <div class="col-lg-6 col-sm-6 col-12">
                                <div class="form-group">
                                    <label>Backup IT Member</label>
                                    <select class="form-control select" name="it_backup_email" id="backup_it_select">
                                        <option value="">Choose Backup IT Member (Optional)</option>
                                        <?php
                                        // Reset result pointer
                                        mysqli_data_seek($it_members_result, 0);
                                        if ($it_members_result && mysqli_num_rows($it_members_result) > 0) {
                                            while ($it_member = mysqli_fetch_assoc($it_members_result)) {
                                                $selected = ($record['it_backup_email'] == $it_member['email_address']) ? 'selected' : '';
                                                echo "<option value='" . htmlspecialchars($it_member['email_address']) . "' $selected>" . htmlspecialchars($it_member['manager_name']) . " (" . htmlspecialchars($it_member['email_address']) . ")</option>";
                                            }
                                        }
                                        ?>
                                        <option value="custom">Custom Email...</option>
                                    </select>
                                    <input type="email" class="form-control mt-2" name="it_backup_custom" id="backup_custom_email" placeholder="Enter custom email" style="display: none;">
                                    <small class="form-text text-muted">Secondary person for support</small>
                                </div>
                            </div>

                            <div class="col-lg-12">
                                <div class="form-group">
                                    <label>IT Setup Remarks</label>
                                    <textarea class="form-control" name="it_remarks" rows="4" placeholder="Enter any remarks, notes, or special requirements for this laptop setup"><?php echo htmlspecialchars($record['it_remarks'] ?? ''); ?></textarea>
                                </div>
                            </div>

                            <div class="col-lg-12">
                                <button type="submit" name="assign_members" class="btn btn-submit">
                                    <i class="fas fa-user-plus"></i> Assign IT Team Members
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <?php endif; ?>

            <!-- Access Control: Show assignment status or block access -->
            <?php if ($already_assigned): ?>
            <div class="card">
                <div class="card-body">
                    <div class="alert alert-success text-center">
                        <h4><i class="fas fa-check-circle me-2"></i>IT Members Already Assigned!</h4>
                        <p class="mb-2">The IT team members have been successfully assigned for this laptop setup.</p>
                        <p><strong>Primary IT Member:</strong> <?php echo htmlspecialchars($record['it_primary_email']); ?></p>
                        <?php if (!empty($record['it_backup_email'])): ?>
                        <p><strong>Backup IT Member:</strong> <?php echo htmlspecialchars($record['it_backup_email']); ?></p>
                        <?php endif; ?>
                        <?php if (!empty($record['it_remarks'])): ?>
                        <p><strong>Remarks:</strong> <?php echo htmlspecialchars($record['it_remarks']); ?></p>
                        <?php endif; ?>
                        <div class="alert alert-info mt-3 mb-0">
                            <i class="fas fa-info-circle"></i> <strong>Note:</strong> Assignment emails have been sent to the IT members.
                        </div>

                        <!-- Admin Override Section -->
                        <div class="mt-4">
                            <h6><i class="fas fa-user-shield me-2"></i>Admin Override</h6>
                            <p class="text-muted">If IT members haven't taken action, you can reassign or send to a custom email:</p>
                            <form method="POST">
                                <div class="row">
                                    <div class="col-lg-8">
                                        <div class="form-group">
                                            <label>Override Email</label>
                                            <input type="email" class="form-control" name="override_email" placeholder="Enter email to send new assignment" required>
                                            <small class="form-text text-muted">This will generate a new token and expire previous links</small>
                                        </div>
                                    </div>
                                    <div class="col-lg-4">
                                        <div class="form-group">
                                            <label>&nbsp;</label>
                                            <button type="submit" name="admin_override" class="btn btn-warning w-100">
                                                <i class="fas fa-paper-plane me-2"></i>Send Override Assignment
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>

        </div>
    </div>
</div>

<!-- Scripts -->
<script src="assets/js/jquery-3.6.0.min.js"></script>
<script src="assets/js/feather.min.js"></script>
<script src="assets/js/jquery.slimscroll.min.js"></script>
<script src="assets/js/jquery.dataTables.min.js"></script>
<script src="assets/js/dataTables.bootstrap4.min.js"></script>
<script src="assets/js/bootstrap.bundle.min.js"></script>
<script src="assets/plugins/select2/js/select2.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalert2.all.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalerts.min.js"></script>

<script>
$(document).ready(function() {
    // Initialize Select2
    $('.select').select2();

    // Handle custom email selection for primary IT
    $('#primary_it_select').change(function() {
        if ($(this).val() === 'custom') {
            $('#primary_custom_email').show().attr('required', true);
        } else {
            $('#primary_custom_email').hide().attr('required', false);
        }
    });

    // Handle custom email selection for backup IT
    $('#backup_it_select').change(function() {
        if ($(this).val() === 'custom') {
            $('#backup_custom_email').show();
        } else {
            $('#backup_custom_email').hide();
        }
    });

    // Trigger change events on page load to handle pre-selected values
    $('#primary_it_select').trigger('change');
    $('#backup_it_select').trigger('change');
});
</script>

<script src="assets/js/script.js"></script>

</body>
</html>
