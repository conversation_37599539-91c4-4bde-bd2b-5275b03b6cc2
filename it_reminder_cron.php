<?php
/**
 * IT Reminder Cron Job
 * Sends daily reminders to IT support team about laptop setups scheduled for today
 * Run this script via cron job at the configured time
 */

// Set timezone to Malaysia
date_default_timezone_set('Asia/Kuala_Lumpur');

require_once(__DIR__ . '/database.php');
require_once(__DIR__ . '/MAILER/vendor/autoload.php');

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

// Create email log table if it doesn't exist
$email_log_table = "CREATE TABLE IF NOT EXISTS `email_log` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `email_type` varchar(50) NOT NULL,
    `recipients` TEXT NOT NULL,
    `subject` varchar(255) NOT NULL,
    `sent_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
)";
mysqli_query($conn, $email_log_table);

// Function to log messages
if (!function_exists('log_message')) {
    function log_message($message) {
        $timestamp = date('Y-m-d H:i:s');
        $log_entry = "[$timestamp] $message" . PHP_EOL;
        file_put_contents('logs/it_reminder_cron.log', $log_entry, FILE_APPEND | LOCK_EX);
        echo $log_entry;
    }
}

// Create logs directory if it doesn't exist
if (!file_exists('logs')) {
    mkdir('logs', 0755, true);
}

log_message("IT Reminder Cron Job Started");

try {
    // Get IT support configuration
    $config_query = "SELECT * FROM `onboarding_email_config` WHERE `config_type` = 'it_support'";
    $config_result = mysqli_query($conn, $config_query);
    $config = mysqli_fetch_assoc($config_result);

    if (!$config || empty($config['cron_time'])) {
        log_message("IT support reminder time is not configured. Exiting.");
        exit;
    }

    // This script will be run every minute by Task Scheduler; gate by configured time (1-minute tolerance)
    $current_minute = date('H:i');
    $config_time = substr($config['cron_time'], 0, 5);
    $time_diff = abs(strtotime($current_minute) - strtotime($config_time));
    $force_send = isset($_GET['force_send']) && $_GET['force_send'] == '1';
    if (!$force_send && $time_diff > 60) {
        log_message("Current time ($current_minute) doesn't match configured time ($config_time). Exiting.");
        exit;
    }
    if ($force_send) {
        log_message("Force send enabled - bypassing time check.");
    }

    // Get today's onboarding records with IT assignments
    $today = date('Y-m-d');
    $query = "
        SELECT ol.*,
               dl.department_name,
               ml.manager_name,
               ofl.office_name as office_name_actual
        FROM `onboarding_list` ol
        LEFT JOIN `department_list` dl ON ol.department_id = dl.department_id
        LEFT JOIN `manager_list` ml ON ol.manager_id = ml.manager_id
        LEFT JOIN `office_list` ofl ON ol.office_id = ofl.office_id
        WHERE DATE(ol.onboarding_date) = ?
        AND (ol.it_primary_email IS NOT NULL OR ol.it_backup_email IS NOT NULL)
        ORDER BY ol.onboarding_date ASC
    ";

    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 's', $today);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);

    $onboarding_records = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $onboarding_records[] = $row;
    }
    mysqli_stmt_close($stmt);

    if (empty($onboarding_records)) {
        log_message("No onboarding records with IT assignments found for today ($today). Exiting.");
        exit;
    }

    log_message("Found " . count($onboarding_records) . " onboarding records for today");

    // Determine display session time from configured IT assignment time
    $assignment_time = isset($config['cron_time']) ? $config['cron_time'] : '09:00';
    $start_time = $assignment_time . ':00';
    $end_time = date('H:i:s', strtotime($start_time . ' +1 hour'));
    $display_start_time = date('g:i A', strtotime($start_time));
    $display_end_time = date('g:i A', strtotime($end_time));

    // Build IT assignment list
    $it_assignment_list = "";
    $total_count = 0;

    foreach ($onboarding_records as $record) {
        $total_count++;
        $it_assignment_list .= "Employee #$total_count:\n";
        $it_assignment_list .= "- Name: " . $record['full_name'] . "\n";
        $it_assignment_list .= "- Employee ID: " . $record['employee_id'] . "\n";
        $it_assignment_list .= "- Department: " . ($record['department_name'] ?? 'Not specified') . "\n";
        $it_assignment_list .= "- Designation: " . $record['job_title'] . "\n";
        $it_assignment_list .= "- Time: $display_start_time - $display_end_time\n";
        $it_assignment_list .= "- Company Email: " . $record['company_email'] . "\n";
        $it_assignment_list .= "- Personal Email: " . $record['personal_email'] . "\n";

        if (!empty($record['it_primary_email'])) {
            $it_assignment_list .= "- Primary IT: " . $record['it_primary_email'] . "\n";
        }
        if (!empty($record['it_backup_email'])) {
            $it_assignment_list .= "- Backup IT: " . $record['it_backup_email'] . "\n";
        }

        // Add completion links
        if (!empty($record['it_primary_email'])) {
            $primary_token = hash('sha256', $record['onboarding_id'] . $record['it_primary_email'] . 'IT_COMPLETION_2024');
            $primary_link = "https://cis.cllsystems.com:9443/staging/it_completion.php?id=" . urlencode($record['onboarding_id']) . "&email=" . urlencode($record['it_primary_email']) . "&token=" . urlencode($primary_token);
            $it_assignment_list .= "- Primary Completion Link: " . $primary_link . "\n";
        }

        if (!empty($record['it_backup_email'])) {
            $backup_token = hash('sha256', $record['onboarding_id'] . $record['it_backup_email'] . 'IT_COMPLETION_2024');
            $backup_link = "https://cis.cllsystems.com:9443/staging/it_completion.php?id=" . urlencode($record['onboarding_id']) . "&email=" . urlencode($record['it_backup_email']) . "&token=" . urlencode($backup_token);
            $it_assignment_list .= "- Backup Completion Link: " . $backup_link . "\n";
        }

        $it_assignment_list .= "\n";
    }

    // Prepare email content
    $email_subject = "Daily IT Reminder: Laptop Setup Schedule - " . date('Y-m-d');
    $current_date = date('Y-m-d');

    // Create email body
    $email_body = "Dear IT Support Team,\n\n";
    $email_body .= "Today's Laptop Setup Schedule:\n\n";
    $email_body .= $it_assignment_list;
    $email_body .= "Please ensure you are prepared for the laptop setup sessions at the scheduled times.\n\n";
    $email_body .= "Important:\n";
    $email_body .= "- Check that all necessary software and accounts are ready\n";
    $email_body .= "- Prepare hardware and accessories\n";
    $email_body .= "- Review employee requirements and company email credentials\n";
    $email_body .= "- Use the completion link after finishing each setup\n\n";
    $email_body .= "Best regards,\nIT Support Team";

    // Send individual emails to assigned IT members
    foreach ($onboarding_records as $record) {
        $it_emails = [];

        // Collect IT emails for this record
        if (!empty($record['it_primary_email'])) {
            $it_emails[] = $record['it_primary_email'];
        }
        if (!empty($record['it_backup_email'])) {
            $it_emails[] = $record['it_backup_email'];
        }

        if (empty($it_emails)) {
            log_message("No IT emails assigned for " . $record['full_name'] . ". Skipping.");
            continue;
        }

        // Generate workflow link
        $base_url = (isset($_SERVER['HTTP_HOST']) ? ( (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://') . $_SERVER['HTTP_HOST'] . dirname($_SERVER['PHP_SELF']) ) : 'https://cis.cllsystems.com:9443/staging');
        $workflow_link = rtrim($base_url,'/')."/onboarding_workflow.php?id=".urlencode($record['onboarding_id']);

        // Use the template from database or default
        $email_template = $config['email_template'] ?? 'Dear IT Support Team,

A new employee requires laptop setup:

Employee Information:
- Name: {employee_name}
- Employee ID: {employee_id}
- Department: {department}
- Designation: {designation}
- Onboarding Date: {onboarding_date}
- Reporting Manager: {reporting_manager}
- Office Location: {office_name}

Please click the link below to manage the onboarding workflow:
{workflow_link}

Priority: Primary contacts should handle this request. Backup contacts are CC\'d for awareness.

Best regards,
HR Team';

        // Replace template variables
        $email_body = str_replace('{employee_name}', htmlspecialchars($record['full_name']), $email_template);
        $email_body = str_replace('{employee_id}', htmlspecialchars($record['employee_id']), $email_body);
        $email_body = str_replace('{department}', htmlspecialchars($record['department_name'] ?? 'Not specified'), $email_body);
        $email_body = str_replace('{designation}', htmlspecialchars($record['job_title']), $email_body);
        $email_body = str_replace('{onboarding_date}', htmlspecialchars($record['onboarding_date']), $email_body);
        $email_body = str_replace('{reporting_manager}', htmlspecialchars($record['manager_name'] ?? 'Not assigned'), $email_body);
        $email_body = str_replace('{office_name}', htmlspecialchars($record['office_name_actual'] ?? 'Not specified'), $email_body);
        $email_body = str_replace('{workflow_link}', $workflow_link, $email_body);

        // Send email to each IT member
        foreach ($it_emails as $it_email) {
            try {
                $mail = new PHPMailer(true);
                $mail->isSMTP();
                $mail->Host = 'smtp.office365.com';
                $mail->SMTPAuth = true;
                $mail->Username = '<EMAIL>';
                $mail->Password = '&[i3F6}0hOw6';
                $mail->SMTPSecure = 'tls';
                $mail->Port = 587;
                $mail->setFrom('<EMAIL>', 'CLLXWARE IT Assignment');

                $mail->addAddress($it_email);

                $mail->isHTML(false);
                $mail->Subject = $config['email_subject'] ?? 'Laptop Setup Required for New Employee';
                $mail->Body = $email_body;

                if ($mail->send()) {
                    log_message("IT assignment email sent to " . $it_email . " for " . $record['full_name']);
                } else {
                    log_message("Failed to send IT assignment email to " . $it_email . ": " . $mail->ErrorInfo);
                }

            } catch (Exception $e) {
                log_message("Error sending email to " . $it_email . ": " . $e->getMessage());
            }
        }
    }

    log_message("IT assignment emails completed");

} catch (Exception $e) {
    log_message("Error: " . $e->getMessage());
} catch (mysqli_sql_exception $e) {
    log_message("Database Error: " . $e->getMessage());
}

log_message("IT Reminder Cron Job Completed");
?>