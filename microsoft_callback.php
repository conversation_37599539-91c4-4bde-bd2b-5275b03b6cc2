<?php
session_start();
require("database.php"); // Include your database connection

// JavaScript to extract access token and reload with it as a query parameter
echo '
<script>
    const url = window.location.href;
    if (url.includes("#")) {
        const params = new URLSearchParams(url.split("#")[1]);
        if (params.has("access_token")) {
            const accessToken = params.get("access_token");
            window.location.href = "microsoft_callback.php?access_token=" + accessToken;
        }
    }
</script>
';

// Define the tenant configurations
$tenants = [
    "cllsystems.com" => [
        "client_id" => "804483f9-e751-4dbd-a691-9a03b7e70ca3",
        "tenant_id" => "febb7525-8406-4e37-b57c-d97da0cf0172"
    ]
];

// Check if the access token was added to the URL query string by the JavaScript above
if (isset($_GET['access_token'])) {
    $access_token = $_GET['access_token'];

    // Use access token to retrieve user information from Microsoft Graph
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, "https://graph.microsoft.com/v1.0/me/");
    curl_setopt($ch, CURLOPT_HTTPHEADER, ["Authorization: Bearer $access_token"]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $user_info = json_decode(curl_exec($ch), true);
    curl_close($ch);

    if (isset($user_info['mail'])) {
        $email = $user_info['mail'];
        $domain = explode('@', $email)[1];

        // Determine the tenant based on the email domain
        if (isset($tenants[$domain])) {
            // Correct tenant found, proceed with login or registration logic
            $client_id = $tenants[$domain]["client_id"];
            $tenant_id = $tenants[$domain]["tenant_id"];
            $name = $user_info['displayName'];
            $designation = $user_info['jobTitle'] ?? null;
            $department = $user_info['department'] ?? null;

            // Check if user already exists in `useracc`
            $sql = "SELECT * FROM useracc WHERE email = '$email'";
            $result = mysqli_query($conn, $sql);

            if ($result && mysqli_num_rows($result) > 0) {
                // User exists, log them in
                $row = mysqli_fetch_assoc($result);
                if ($row["ban"] != "1") {
                    $_SESSION["user"] = $row["email"];
                    $_SESSION["role_id"] = $row["role_id"];
                    $_SESSION["acc_id"] = $row["acc_id"];
                    
                    header("Location: ./index.php");
                    exit();
                } else {
                    echo "<script>alert('Your account is suspended. Contact the Technical Department.'); window.location='signin.php';</script>";
                }
            } else {
                // User not found, insert new user
                function generateUniqueId($length = 10) {
                    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
                    $randomString = '';
                    for ($i = 0; $i < $length; $i++) {
                        $randomString .= $characters[rand(0, strlen($characters) - 1)];
                    }
                    return $randomString;
                }

                $acc_id = generateUniqueId();
                $role_id = 'UzZhqYA3OJ';

                // Insert into `useracc`
                $sql_useracc = "INSERT INTO useracc (email, acc_id, role_id) VALUES ('$email', '$acc_id', '$role_id')";
                $insert_useracc = mysqli_query($conn, $sql_useracc);

                // Insert into `user-list`
                $sql_userlist = "INSERT INTO `user-list` (acc_id, email, name, designation, department) 
                                 VALUES ('$acc_id', '$email', '$name', ".($designation ? "'$designation'" : "NULL").", ".($department ? "'$department'" : "NULL").")";
                $insert_userlist = mysqli_query($conn, $sql_userlist);

                if ($insert_useracc && $insert_userlist) {
                    $_SESSION["user"] = $email;
                    $_SESSION["role_id"] = $role_id;
                    $_SESSION["acc_id"] = $acc_id;
                    
                    header("Location: ./index.php");
                    exit();
                } else {
                    echo "<script>alert('Failed to create account. Please contact the Technical Department.'); window.location='signin.php';</script>";
                }
            }
        } else {
            echo "<script>alert('Invalid email domain. Please use a company email.'); window.location='signin.php';</script>";
        }
    } else {
        echo "<script>alert('Could not retrieve user email.'); window.location='signin.php';</script>";
    }
} else {
    echo "<script>alert('Authorization token not found.'); window.location='signin.php';</script>";
}
