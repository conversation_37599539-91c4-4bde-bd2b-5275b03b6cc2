<?php
session_start();

$redirect_uri = "https://cis.cllsystems.com:9443/staging/microsoft_callback.php";
$_SESSION['state'] = session_id(); // CSRF protection

// Use the tenant-specific endpoint instead of 'common'
$login_url = "https://login.microsoftonline.com/febb7525-8406-4e37-b57c-d97da0cf0172/oauth2/v2.0/authorize";
$params = [
    'client_id' => "804483f9-e751-4dbd-a691-9a03b7e70ca3",  // Use your application's client ID
    'redirect_uri' => $redirect_uri,
    'response_type' => 'token',
    'scope' => 'https://graph.microsoft.com/User.Read',
    'state' => $_SESSION['state']
];

// Redirect to the Microsoft login page
header("Location: " . $login_url . '?' . http_build_query($params));
exit();
