<?php 
require("database.php");
require 'MAILER/vendor/autoload.php'; // PHPMailer autoload

use <PERSON><PERSON><PERSON>ailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

// Enable error reporting for debugging (disable in production)
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (isset($_SESSION["user"])) {
    $email = $_SESSION["user"];
    $acc_id = $_SESSION["acc_id"];

    // Fetch user details
    $sql4 = "SELECT * FROM `user-list` WHERE `acc_id` = ?"; 
    $stmt = mysqli_prepare($conn, $sql4);
    mysqli_stmt_bind_param($stmt, "s", $acc_id);
    mysqli_stmt_execute($stmt);
    $result4 = mysqli_stmt_get_result($stmt);

    if ($row4 = mysqli_fetch_assoc($result4)) { 
        $fullname = $row4['name']; 
        $email2 = $row4['email']; 

        if (is_null($fullname) || trim($fullname) === "") {
            $fullname = $email2; 
        }
    } else {
        echo "<script>alert('No record found for the current user!');</script>";
        exit;
    }

    // Fetch checklist items
    $sql_checklist = "SELECT * FROM `offboarding_checklist`";
    $result_checklist = mysqli_query($conn, $sql_checklist);

    // Fetch positions, departments, managers, and persons in charge
    $result_position = mysqli_query($conn, "SELECT * FROM `position_list`");
    $result_department = mysqli_query($conn, "SELECT * FROM `department_list`");
    $result_manager = mysqli_query($conn, "SELECT * FROM `manager_list`");
    $result_persons_in_charge = mysqli_query($conn, "SELECT * FROM `person_in_charge_list`");

    // Handle form submission
    if (isset($_POST["submit"])) {
        // Get form input data
        $employee_id = $_POST["employee_id"];
        $employee_email = $_POST["employee_email"]; // Work Email
        $name = $_POST["name"];
        $position_id = $_POST["position"]; // Capturing position_id
        $department_id = $_POST["department"]; // Capturing department_id
        $personal_email = $_POST["personal_email"];
        $physical_last_day = $_POST["physical_last_day"];
        $official_last_day = $_POST["official_last_day"];
        $reporting_manager_id = $_POST["reporting_manager"]; // Capturing reporting_manager_id
        $remark = isset($_POST['remark']) ? $_POST['remark'] : null;
        $persons_in_charge = isset($_POST['persons_in_charge']) ? $_POST['persons_in_charge'] : [];

        // Fetch position name based on position_id to avoid "undefined" errors
        $sql_position = "SELECT position_name FROM `position_list` WHERE `position_id` = ?";
        $stmt_position = mysqli_prepare($conn, $sql_position);
        mysqli_stmt_bind_param($stmt_position, "i", $position_id);
        mysqli_stmt_execute($stmt_position);
        $result_position_db = mysqli_stmt_get_result($stmt_position);
        if ($position_data = mysqli_fetch_assoc($result_position_db)) {
            $position_name = $position_data['position_name'];
        } else {
            $position_name = "Unknown Position";
        }
        mysqli_stmt_close($stmt_position);

        // Generate unique offboarding ID
        $random_string = uniqid("OFF-", true); 
        $offboarding_id = substr(str_shuffle($random_string), 0, 10);

        // Convert persons in charge array to comma-separated string
        $persons_in_charge_ids = !empty($persons_in_charge) ? implode(',', $persons_in_charge) : null;

        // Handle file uploads
        $fileDestinations = []; // Array to store file paths
        if (isset($_FILES['uploaded_files']) && $_FILES['uploaded_files']['error'][0] !== UPLOAD_ERR_NO_FILE) {
            $maxFileSize = 250 * 1024 * 1024; // 250MB in bytes
            $allowed = array('jpg', 'jpeg', 'png', 'gif', 'pdf', 'xls', 'xlsx', 'csv');
            $totalFiles = count($_FILES['uploaded_files']['name']);
            for ($i = 0; $i < $totalFiles; $i++) {
                $fileName = $_FILES['uploaded_files']['name'][$i];
                $fileTmpName = $_FILES['uploaded_files']['tmp_name'][$i];
                $fileSize = $_FILES['uploaded_files']['size'][$i];
                $fileError = $_FILES['uploaded_files']['error'][$i];
                $fileType = $_FILES['uploaded_files']['type'][$i];
                $fileExt = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
                if (in_array($fileExt, $allowed)) {
                    if ($fileSize <= $maxFileSize) {
                        if ($fileError === 0) {
                            $newFileName = uniqid('', true) . "." . $fileExt;
                            $uploadDirectory = 'uploads/offboarding_files/';
                            if (!is_dir($uploadDirectory)) {
                                mkdir($uploadDirectory, 0755, true);
                            }
                            $fileDestination = $uploadDirectory . $newFileName;
                            if (move_uploaded_file($fileTmpName, $fileDestination)) {
                                $fileDestinations[] = $fileDestination;
                            } else {
                                echo "<script>alert('Error uploading the file: $fileName'); window.location='offboarding.php';</script>";
                                exit;
                            }
                        } else {
                            echo "<script>alert('Error with the file upload: $fileName. Error code: $fileError'); window.location='offboarding.php';</script>";
                            exit;
                        }
                    } else {
                        echo "<script>alert('File size exceeds 250MB limit for file: $fileName'); window.location='offboarding.php';</script>";
                        exit;
                    }
                } else {
                    echo "<script>alert('Invalid file type for file: $fileName. Allowed types: JPG, JPEG, PNG, GIF, PDF, XLS, XLSX, CSV.'); window.location='offboarding.php';</script>";
                    exit;
                }
            }
            $filePathsJson = json_encode($fileDestinations);
        } else {
            $filePathsJson = null;
        }

        // Insert offboarding data into the database
        $sql = "INSERT INTO `offboarding_list` 
                (`offboarding_id`, `employee_id`, `employee_email`, `name`, `position_id`, `department_id`, `personal_email`, 
                `physical_last_day`, `official_last_day`, `reporting_manager_id`, `remark`, `person_in_charge_ids`, `file_path`) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, "sssssssssssss", 
            $offboarding_id, 
            $employee_id, 
            $employee_email, 
            $name, 
            $position_id, 
            $department_id, 
            $personal_email, 
            $physical_last_day, 
            $official_last_day, 
            $reporting_manager_id, 
            $remark, 
            $persons_in_charge_ids, 
            $filePathsJson
        );

        if (mysqli_stmt_execute($stmt)) {
            // Insert checklist items
            if (isset($_POST['checklist'])) {
                foreach ($_POST['checklist'] as $checklist_id => $value) {
                    $is_checked = 1;
                    $sql_checklist_status = "INSERT INTO `offboarding_checklist_status` (`offboarding_id`, `checklist_id`, `is_checked`) 
                                             VALUES (?, ?, ?)";
                    $stmt_checklist_status = mysqli_prepare($conn, $sql_checklist_status);
                    mysqli_stmt_bind_param($stmt_checklist_status, "ssi", $offboarding_id, $checklist_id, $is_checked);
                    mysqli_stmt_execute($stmt_checklist_status);
                    mysqli_stmt_close($stmt_checklist_status);
                }
            }

            // Fetch the email and name of the selected manager based on reporting_manager_id
            $sql_manager = "SELECT manager_name, email_address FROM `manager_list` WHERE `manager_id` = ?";
            $stmt_manager = mysqli_prepare($conn, $sql_manager);
            mysqli_stmt_bind_param($stmt_manager, "i", $reporting_manager_id);
            mysqli_stmt_execute($stmt_manager);
            $result_manager = mysqli_stmt_get_result($stmt_manager);
            $manager = mysqli_fetch_assoc($result_manager);
            mysqli_stmt_close($stmt_manager);

            $manager_email = $manager['email_address'];
            $manager_name = $manager['manager_name'];

            // Send email notification to the reporting manager
            if (!empty($manager_email)) {
                echo "<script>console.log('Sending email to: " . $manager_email . "');</script>";
                $mail = new PHPMailer(true);
                try {
                    $mail->isSMTP();
                    $mail->Host       = 'smtp.office365.com';
                    $mail->SMTPAuth   = true;
                    $mail->Username   = '<EMAIL>'; 
                    $mail->Password   = '&[i3F6}0hOw6';
                    $mail->SMTPSecure = 'tls';
                    $mail->Port       = 587;

                    $mail->setFrom('<EMAIL>', 'CLLXWARE');
                    $mail->addAddress($manager_email);

                    $mail->isHTML(true);
                    $mail->Subject = 'Offboarding Notification';
                    $mail->Body = "
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <style>
                            body { font-family: Arial, sans-serif; background-color: #f4f4f4; margin: 0; padding: 0; }
                            .container { max-width: 600px; margin: 0 auto; padding: 20px; background-color: #fff; 
                                        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1); border-radius: 10px; }
                            .header { text-align: center; padding: 20px; background-color: #004085; color: white; 
                                    border-radius: 10px 10px 0 0; }
                            .content { padding: 20px; color: #333; }
                            .content strong { color: #007bff; }
                            .footer { text-align: center; padding: 10px; background-color: #f4f4f4; color: #888; 
                                    font-size: 12px; }
                            .footer a { color: #007bff; text-decoration: none; }
                            .btn { display: inline-block; padding: 10px 20px; color: #fff; background-color: #007bff;
                                text-decoration: none; border-radius: 5px; margin-top: 10px; }
                        </style>
                    </head>
                    <body>
                        <div class='container'>
                            <div class='header'>
                                <h2>Offboarding Notification</h2>
                            </div>
                            <div class='content'>
                                <p>Dear " . htmlspecialchars($manager_name) . ",</p>
                                <p>We would like to formally notify you that the offboarding process has been initiated for the following employee:</p>
                                <p><strong>Employee ID:</strong> " . htmlspecialchars($employee_id) . "</p>
                                <p><strong>Name:</strong> " . htmlspecialchars($name) . "</p>
                                <p><strong>Position:</strong> " . htmlspecialchars($position_name) . "</p>
                                <p><strong>Physical Last Day:</strong> " . htmlspecialchars($physical_last_day) . "</p>
                                <p><strong>Official Last Day:</strong> " . htmlspecialchars($official_last_day) . "</p>
                                <p>Please log in to the system at your earliest convenience to review the complete details regarding this transition.</p>
                                <p>You may access further offboarding particulars by clicking the button below:</p>
                                <a href='https://cis.cllsystems.com:9443/staging/offboarding_details.php?offboarding_id=" . urlencode($offboarding_id) . "' class='btn'>View Offboarding Details</a>
                                <p>Thank you for your prompt attention and cooperation during this transition.</p>
                                <p>Sincerely,<br>The HR Team</p>
                            </div>
                            <div class='footer'>
                                <p>CLLXWARE | Powered by CLL Systems Sdn Bhd</p>
                                <p>If you require any assistance, please contact us at <a href='mailto:<EMAIL>'><EMAIL></a>.</p>
                            </div>
                        </div>
                    </body>
                    </html>";
                    $mail->send();
                } catch (Exception $e) {
                    error_log("Email could not be sent to $manager_email. PHPMailer Error: {$mail->ErrorInfo}");
                    echo "<script>console.log('PHPMailer Error: {$mail->ErrorInfo}');</script>";
                }
            }

            // Fetch persons in charge details
            $persons_in_charge_details = [];
            if (!empty($persons_in_charge_ids)) {
                $selected_persons_in_charge_ids = explode(',', $persons_in_charge_ids);
                $placeholders = implode(',', array_fill(0, count($selected_persons_in_charge_ids), '?'));
                $types = str_repeat('i', count($selected_persons_in_charge_ids));
                $sql_persons_in_charge = "SELECT person_in_charge_id, person_in_charge_name, email_address FROM `person_in_charge_list` WHERE `person_in_charge_id` IN ($placeholders)";
                $stmt_persons_in_charge = mysqli_prepare($conn, $sql_persons_in_charge);
                mysqli_stmt_bind_param($stmt_persons_in_charge, $types, ...$selected_persons_in_charge_ids);
                mysqli_stmt_execute($stmt_persons_in_charge);
                $result_persons_in_charge = mysqli_stmt_get_result($stmt_persons_in_charge);

                while ($row_pic = mysqli_fetch_assoc($result_persons_in_charge)) {
                    $persons_in_charge_details[] = $row_pic;
                }
                mysqli_stmt_close($stmt_persons_in_charge);
            }

            // Send "Your Offboarding Information" email to both Employee Email and Personal Email
            $recipient_emails = [$employee_email, $personal_email];
            foreach ($recipient_emails as $recipient_email) {
                if (!empty($recipient_email)) {
                    try {
                        $mail_employee = new PHPMailer(true);

                        $mail_employee->isSMTP();
                        $mail_employee->Host       = 'smtp.office365.com';
                        $mail_employee->SMTPAuth   = true;
                        $mail_employee->Username   = '<EMAIL>';
                        $mail_employee->Password   = '&[i3F6}0hOw6';
                        $mail_employee->SMTPSecure = 'tls';
                        $mail_employee->Port       = 587;

                        $mail_employee->setFrom('<EMAIL>', 'CLLXWARE');
                        $mail_employee->addAddress($recipient_email);

                        $mail_employee->isHTML(true);
                        $mail_employee->Subject = 'Your Offboarding Information';

                        // Fetch department name
                        $sql_department = "SELECT department_name FROM `department_list` WHERE `department_id` = ?";
                        $stmt_department = mysqli_prepare($conn, $sql_department);
                        mysqli_stmt_bind_param($stmt_department, "i", $department_id);
                        mysqli_stmt_execute($stmt_department);
                        $result_department = mysqli_stmt_get_result($stmt_department);
                        $department_data = mysqli_fetch_assoc($result_department);
                        $department_name = $department_data['department_name'];
                        mysqli_stmt_close($stmt_department);

                        // Fetch reporting manager name
                        $sql_reporting_manager = "SELECT manager_name FROM `manager_list` WHERE `manager_id` = ?";
                        $stmt_reporting_manager = mysqli_prepare($conn, $sql_reporting_manager);
                        mysqli_stmt_bind_param($stmt_reporting_manager, "i", $reporting_manager_id);
                        mysqli_stmt_execute($stmt_reporting_manager);
                        $result_reporting_manager = mysqli_stmt_get_result($stmt_reporting_manager);
                        $reporting_manager_data = mysqli_fetch_assoc($result_reporting_manager);
                        $reporting_manager_name = $reporting_manager_data['manager_name'];
                        mysqli_stmt_close($stmt_reporting_manager);

                        // Fetch persons in charge names
                        if (!empty($persons_in_charge_details)) {
                            $persons_in_charge_names = array_map(function($pic) {
                                return htmlspecialchars($pic['person_in_charge_name']);
                            }, $persons_in_charge_details);
                            $persons_in_charge_string = implode(', ', $persons_in_charge_names);
                        } else {
                            $persons_in_charge_string = "None";
                        }

                        $mail_employee->Body = "
                            <!DOCTYPE html>
                            <html>
                            <head>
                                <style>
                                    body { font-family: Arial, sans-serif; background-color: #f4f4f4; margin: 0; padding: 0; }
                                    .container { max-width: 600px; margin: 0 auto; padding: 20px; background-color: #fff; 
                                                 box-shadow: 0 0 10px rgba(0, 0, 0, 0.1); border-radius: 10px; }
                                    .header { text-align: center; padding: 20px; background-color: #004085; color: white; 
                                              border-radius: 10px 10px 0 0; }
                                    .content { padding: 20px; color: #333; }
                                    .content strong { color: #007bff; }
                                    .footer { text-align: center; padding: 10px; background-color: #f4f4f4; color: #888; 
                                              font-size: 12px; }
                                    .footer a { color: #007bff; text-decoration: none; }
                                </style>
                            </head>
                            <body>
                                <div class='container'>
                                    <div class='header'>
                                        <h2>Your Offboarding Information</h2>
                                    </div>
                                    <div class='content'>
                                        <p><strong>Employee ID:</strong> " . htmlspecialchars($employee_id) . "</p>
                                        <p><strong>Name:</strong> " . htmlspecialchars($name) . "</p>
                                        <p><strong>Employee Email (Work):</strong> " . htmlspecialchars($employee_email) . "</p>
                                        <p><strong>Personal Email:</strong> " . htmlspecialchars($personal_email) . "</p>
                                        <p><strong>Position:</strong> " . htmlspecialchars($position_name) . "</p>
                                        <p><strong>Department:</strong> " . htmlspecialchars($department_name) . "</p>
                                        <p><strong>Reporting Manager:</strong> " . htmlspecialchars($reporting_manager_name) . "</p>
                                        <p><strong>Physical Last Day:</strong> " . htmlspecialchars($physical_last_day) . "</p>
                                        <p><strong>Official Last Day:</strong> " . htmlspecialchars($official_last_day) . "</p>
                                        <p><strong>Persons in Charge:</strong> " . htmlspecialchars($persons_in_charge_string) . "</p>
                                        <p><strong>Remarks:</strong> " . nl2br(htmlspecialchars($remark)) . "</p>
                                        <p>If you have any concerns or questions regarding your offboarding process, please contact HR.</p>
                                        <p>You can reach HR at: <a href='mailto:<EMAIL>'><EMAIL></a></p>
                                    </div>
                                    <div class='footer'>
                                        <p>CLLXWARE | Powered by CLL Systems Sdn Bhd</p>
                                        <p>For support, contact <a href='mailto:<EMAIL>'><EMAIL></a></p>
                                    </div>
                                </div>
                            </body>
                            </html>";
                        $mail_employee->send();
                        echo "<script>console.log('Email sent to: $recipient_email');</script>";
                    } catch (Exception $e) {
                        error_log("Email could not be sent to $recipient_email. PHPMailer Error: {$mail_employee->ErrorInfo}");
                        echo "<script>console.log('PHPMailer Error: {$mail_employee->ErrorInfo}');</script>";
                    }
                }
            }

            // Send "Offboarding Notification" emails to each person in charge
            if (!empty($persons_in_charge_details)) {
                foreach ($persons_in_charge_details as $pic) {
                    $pic_email = $pic['email_address'];
                    $pic_name = $pic['person_in_charge_name'];

                    if (!empty($pic_email)) {
                        try {
                            $mail_pic = new PHPMailer(true);
                            $mail_pic->isSMTP();
                            $mail_pic->Host       = 'smtp.office365.com';
                            $mail_pic->SMTPAuth   = true;
                            $mail_pic->Username   = '<EMAIL>';
                            $mail_pic->Password   = '&[i3F6}0hOw6';
                            $mail_pic->SMTPSecure = 'tls';
                            $mail_pic->Port       = 587;

                            $mail_pic->setFrom('<EMAIL>', 'CLLXWARE');
                            $mail_pic->addAddress($pic_email);

                            $mail_pic->isHTML(true);
                            $mail_pic->Subject = 'Offboarding Notification';
                            $mail_pic->Body = "
                            <!DOCTYPE html>
                            <html>
                            <head>
                                <style>
                                    body { font-family: Arial, sans-serif; background-color: #f4f4f4; margin: 0; padding: 0; }
                                    .container { max-width: 600px; margin: 0 auto; padding: 20px; background-color: #fff; 
                                                box-shadow: 0 0 10px rgba(0, 0, 0, 0.1); border-radius: 10px; }
                                    .header { text-align: center; padding: 20px; background-color: #004085; color: white; 
                                            border-radius: 10px 10px 0 0; }
                                    .content { padding: 20px; color: #333; }
                                    .content strong { color: #007bff; }
                                    .footer { text-align: center; padding: 10px; background-color: #f4f4f4; color: #888; 
                                            font-size: 12px; }
                                    .footer a { color: #007bff; text-decoration: none; }
                                    .btn { display: inline-block; padding: 10px 20px; color: #fff; background-color: #007bff;
                                        text-decoration: none; border-radius: 5px; margin-top: 10px; }
                                </style>
                            </head>
                            <body>
                                <div class='container'>
                                    <div class='header'>
                                        <h2>Offboarding Notification</h2>
                                    </div>
                                    <div class='content'>
                                        <p>Dear " . htmlspecialchars($pic_name) . ",</p>
                                        <p>We would like to formally notify you that the offboarding process has been initiated for the following employee:</p>
                                        <p><strong>Employee ID:</strong> " . htmlspecialchars($employee_id) . "</p>
                                        <p><strong>Name:</strong> " . htmlspecialchars($name) . "</p>
                                        <p><strong>Position:</strong> " . htmlspecialchars($position_name) . "</p>
                                        <p><strong>Physical Last Day:</strong> " . htmlspecialchars($physical_last_day) . "</p>
                                        <p><strong>Official Last Day:</strong> " . htmlspecialchars($official_last_day) . "</p>
                                        <p>Please log in to the system at your earliest convenience to review the complete details regarding this transition.</p>
                                        <p>You may access further offboarding particulars by clicking the button below:</p>
                                        <a href='https://cis.cllsystems.com:9443/staging/offboarding_details.php?offboarding_id=" . urlencode($offboarding_id) . "' class='btn'>View Offboarding Details</a>
                                        <p>Thank you for your attention and cooperation during this process.</p>
                                        <p>Sincerely,<br>The HR Team</p>
                                    </div>
                                    <div class='footer'>
                                        <p>CLLXWARE | Powered by CLL Systems Sdn Bhd</p>
                                        <p>If you require any assistance, please contact us at <a href='mailto:<EMAIL>'><EMAIL></a>.</p>
                                    </div>
                                </div>
                            </body>
                            </html>";

                            $mail_pic->send();
                            echo "<script>console.log('Offboarding notification sent to: $pic_email');</script>";
                        } catch (Exception $e) {
                            error_log("Email could not be sent to $pic_email. PHPMailer Error: {$mail_pic->ErrorInfo}");
                            echo "<script>console.log('PHPMailer Error: {$mail_pic->ErrorInfo}');</script>";
                        }
                    }
                }
            }

            echo "<script>alert('The offboarding has been added and notifications sent.'); window.location='offboarding_list.php';</script>";
        } else {
            echo "<script>alert('Error: " . mysqli_error($conn) . "'); window.location='offboarding.php';</script>";
        }

        mysqli_stmt_close($stmt);
    }
?>
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0">
    <meta name="description" content="CLL Ticketing System">
    <meta name="author" content="Powered by CLL Systems Sdn Bhd">
    <meta name="robots" content="noindex, nofollow">
    <title>CLLXWARE - Offboarding Form</title>

    <!-- Favicon -->
    <link rel='icon' type='image/x-icon' href='https://www.cllsystems.com/wp-content/themes/cllsystems/img/favicon.png'>

    <!-- Stylesheets -->
    <link rel="stylesheet" href="assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="assets/css/animate.css">
    <link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">
    <link rel="stylesheet" href="assets/css/dataTables.bootstrap4.min.css">
    <link rel="stylesheet" href="assets/plugins/fontawesome/css/fontawesome.min.css">
    <link rel="stylesheet" href="assets/plugins/fontawesome/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">

    <style>
        .form-check-input {
            margin-left: 0;
            margin-top: 0.3rem;
        }
        .form-check-label {
            margin-left: 1.5rem;
        }
        .form-section-title {
            margin-bottom: 20px;
            font-size: 18px;
            font-weight: bold;
            color: #000;
            text-transform: uppercase;
        }
        .card {
            border: 1px solid #e0e0e0;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .existing-files img {
            max-width: 100px;
            max-height: 100px;
        }
        .download-file {
            margin-left: 15px;
            color: blue;
            text-decoration: none;
            font-weight: bold;
        }
        .download-file:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
<div id="global-loader">
    <div class="whirly-loader"></div>
</div>

<div class="main-wrapper">
<?php 
    $role_id = $_SESSION["role_id"];
    $sql20 = "SELECT * FROM `role-list` WHERE `role-id` = ?";
    $stmt20 = mysqli_prepare($conn, $sql20);
    mysqli_stmt_bind_param($stmt20, "s", $role_id);
    mysqli_stmt_execute($stmt20);
    $result20 = mysqli_stmt_get_result($stmt20);

    if ($row20 = mysqli_fetch_assoc($result20)) {
        $all = $row20['offboarding_all'];
        $create = $row20['offboarding_create'];
        if($create != '1' and $all !='1'){
            header("location: ./index.php");
            exit;
        }
    } else {
        echo "<script>alert('Role data not found');</script>";
        exit;
    }

    mysqli_stmt_close($stmt20);
    include("header.php");
?>

    <div class="page-wrapper">
        <div class="content">
            <div class="page-header">
                <div class="page-title">
                    <h4>Offboarding Form</h4>
                    <h6>Please fill in the details below to complete the offboarding process.</h6>
                </div>
            </div>

            <form action="#" method="post" enctype="multipart/form-data">
                <div class="card">
                    <div class="card-body">
                        <div class="form-section-title">Employee Information</div>
                        <div class="row">
                            <!-- Employee ID -->
                            <div class="col-md-6 col-sm-12">
                                <div class="form-group">
                                    <label>Employee ID *</label>
                                    <input type="text" name="employee_id" required class="form-control" placeholder="Employee ID">
                                </div>
                            </div>
                            <!-- Name -->
                            <div class="col-md-6 col-sm-12">
                                <div class="form-group">
                                    <label>Name *</label>
                                    <input type="text" name="name" required class="form-control" placeholder="Full Name">
                                </div>
                            </div>
                            <!-- Department (Dropdown) -->
                            <div class="col-md-6 col-sm-12">
                                <div class="form-group">
                                    <label>Department *</label>
                                    <select name="department" id="departmentSelect" required class="form-control select">
                                        <option value="" disabled selected>Select Department</option>
                                        <?php 
                                        mysqli_data_seek($result_department, 0); 
                                        while ($row_department = mysqli_fetch_assoc($result_department)): ?>
                                            <option value="<?php echo htmlspecialchars($row_department['department_id']); ?>">
                                                <?php echo htmlspecialchars($row_department['department_name']); ?>
                                            </option>
                                        <?php endwhile; ?>
                                    </select>
                                </div>
                            </div>
                            <!-- Position (Dropdown) -->
                            <div class="col-md-6 col-sm-12">
                                <div class="form-group">
                                    <label>Position *</label>
                                    <select name="position" id="positionSelect" required class="form-control select" disabled>
                                        <option value="" disabled selected>Select Department First</option>
                                    </select>
                                </div>
                            </div>
                            <!-- Employee Email (Work Email) -->
                            <div class="col-md-6 col-sm-12">
                                <div class="form-group">
                                    <label>Employee Email *</label>
                                    <input type="email" name="employee_email" required class="form-control" placeholder="Employee Work Email">
                                </div>
                            </div>
                            <!-- Personal Email -->
                            <div class="col-md-6 col-sm-12">
                                <div class="form-group">
                                    <label>Personal Email *</label>
                                    <input type="email" name="personal_email" required class="form-control" placeholder="Personal Email">
                                </div>
                            </div>
                            <!-- Persons in Charge (Multi-Select Dropdown) -->
                            <div class="col-md-6 col-sm-12">
                                <div class="form-group">
                                    <label>Persons in Charge *</label>
                                    <select name="persons_in_charge[]" class="form-control select" multiple required>
                                        <option value="" disabled>Select Persons in Charge</option>
                                        <?php 
                                        mysqli_data_seek($result_persons_in_charge, 0); 
                                        while ($row_pic = mysqli_fetch_assoc($result_persons_in_charge)): ?>
                                            <option value="<?php echo htmlspecialchars($row_pic['person_in_charge_id']); ?>">
                                                <?php echo htmlspecialchars($row_pic['person_in_charge_name']) . " (" . htmlspecialchars($row_pic['email_address']) . ")"; ?>
                                            </option>
                                        <?php endwhile; ?>
                                    </select>
                                </div>
                            </div>
                            <!-- Reporting Manager (Dropdown) -->
                            <div class="col-md-6 col-sm-12">
                                <div class="form-group">
                                    <label>Reporting Manager *</label>
                                    <select name="reporting_manager" required class="form-control select">
                                        <option value="" disabled selected>Select Reporting Manager</option>
                                        <?php 
                                        mysqli_data_seek($result_manager, 0); 
                                        while ($row_manager = mysqli_fetch_assoc($result_manager)): ?>
                                            <option value="<?php echo htmlspecialchars($row_manager['manager_id']); ?>">
                                                <?php echo htmlspecialchars($row_manager['manager_name']); ?>
                                            </option>
                                        <?php endwhile; ?>
                                    </select>
                                </div>
                            </div>
                            <!-- Physical Last Day -->
                            <div class="col-md-6 col-sm-12">
                                <div class="form-group">
                                    <label>Physical Last Day *</label>
                                    <input type="date" name="physical_last_day" required class="form-control">
                                </div>
                            </div>
                            <!-- Official Last Day -->
                            <div class="col-md-6 col-sm-12">
                                <div class="form-group">
                                    <label>Official Last Day *</label>
                                    <input type="date" name="official_last_day" required class="form-control">
                                </div>
                            </div>
                        </div>

                        <div class="form-section-title">Offboarding Checklist</div>
                        <div class="row">
                            <?php 
                            mysqli_data_seek($result_checklist, 0); 
                            while ($checklist_item = mysqli_fetch_assoc($result_checklist)): ?>
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" name="checklist[<?php echo htmlspecialchars($checklist_item['checklist_id']); ?>]">
                                        <label class="form-check-label"><?php echo htmlspecialchars($checklist_item['checklist_name']); ?></label>
                                    </div>
                                </div>
                            <?php endwhile; ?>
                        </div>
                        <br>

                        <!-- Remark Section -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label>Remark *</label>
                                    <textarea name="remark" class="form-control" rows="4" placeholder="Add any remarks or notes here" required></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- File or Image Upload Section -->
                        <div class="col-lg-12">
                            <div class="form-group">
                                <label>File or Image</label>
                                <div class="image-upload">
                                    <input type="file" name="uploaded_files[]" id="imageInput" multiple 
                                           onchange="handleFileUpload(event)" 
                                           accept=".jpg, .jpeg, .png, .gif, .pdf, .xls, .xlsx, .csv">
                                    <div class="image-uploads" id="uploadPlaceholder">
                                        <img src="assets/img/icons/upload.svg" alt="img">
                                        <h4>Drag and drop files to upload</h4>
                                    </div>
                                    <div id="uploadedFiles" style="margin-top: 10px; display: none;"></div>
                                </div>
                                <small>Maximum file size: 250MB per file. Allowed types: JPG, JPEG, PNG, GIF, PDF, XLS, XLSX, CSV.</small>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="col-lg-12">
                            <input type='submit' class='btn btn-primary me-2' name='submit' value='Submit'>
                            <a href="offboarding_list.php" class="btn btn-secondary">Cancel</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
<!-- Scripts -->
<script src="assets/js/jquery-3.6.0.min.js"></script>
<script src="assets/js/feather.min.js"></script>
<script src="assets/js/jquery.slimscroll.min.js"></script>
<script src="assets/js/jquery.dataTables.min.js"></script>
<script src="assets/js/dataTables.bootstrap4.min.js"></script>
<script src="assets/js/bootstrap.bundle.min.js"></script>
<script src="assets/plugins/select2/js/select2.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalert2.all.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalerts.min.js"></script>
<script src="assets/js/script.js"></script>

<script>
    $(document).ready(function() {
        // Initialize select2
        $('.select').select2({
            placeholder: "Select an option",
            allowClear: true
        });

        // Handle department selection
        $('#departmentSelect').change(function() {
            var departmentId = $(this).val();
            var positionSelect = $('#positionSelect');
            
            if (departmentId) {
                // Enable position select
                positionSelect.prop('disabled', false);
                
                // Fetch positions for selected department using AJAX
                $.ajax({
                    url: 'get_positions.php',
                    type: 'POST',
                    data: { department_id: departmentId },
                    dataType: 'json',
                    success: function(data) {
                        // Clear current options
                        positionSelect.empty();
                        positionSelect.append('<option value="" disabled selected>Select Position</option>');
                        
                        // Add new options
                        $.each(data, function(index, item) {
                            positionSelect.append('<option value="' + item.position_id + '">' + item.position_name + '</option>');
                        });
                        
                        // Refresh select2
                        positionSelect.trigger('change');
                    },
                    error: function() {
                        alert('Error fetching positions. Please try again.');
                    }
                });
            } else {
                // If no department selected, disable and clear position select
                positionSelect.prop('disabled', true);
                positionSelect.empty();
                positionSelect.append('<option value="" disabled selected>Select Department First</option>');
                positionSelect.trigger('change');
            }
        });
    });

    function handleFileUpload(event) {
        const files = event.target.files;
        const uploadedFilesDiv = document.getElementById("uploadedFiles");
        const uploadPlaceholder = document.getElementById("uploadPlaceholder");

        if (files.length > 0) {
            uploadPlaceholder.style.display = "none";
            uploadedFilesDiv.style.display = "block";
            uploadedFilesDiv.innerHTML = "";
            Array.from(files).forEach((file) => {
                const fileDiv = document.createElement("div");
                fileDiv.style.marginBottom = "10px";
                const fileLink = document.createElement("span");
                fileLink.style.textDecoration = "none";
                fileLink.style.color = "blue";
                fileLink.style.display = "flex";
                fileLink.style.alignItems = "center";
                const fileType = file.type.split('/')[0];
                const fileExt = file.name.split('.').pop().toLowerCase();
                if (fileType === "image") {
                    const img = document.createElement("img");
                    img.src = URL.createObjectURL(file);
                    img.style.maxWidth = "100px";
                    img.style.maxHeight = "100px";
                    img.style.marginRight = "10px";
                    fileLink.appendChild(img);
                } else if (fileExt === "csv") {
                    const csvIcon = document.createElement("img");
                    csvIcon.src = "assets/img/icons/csv-icon.svg";
                    csvIcon.alt = "CSV Icon";
                    csvIcon.style.width = "24px";
                    csvIcon.style.height = "24px";
                    csvIcon.style.marginRight = "10px";
                    fileLink.appendChild(csvIcon);
                } else {
                    const fileIcon = document.createElement("img");
                    fileIcon.src = "assets/img/icons/file-icon.svg";
                    fileIcon.alt = "File Icon";
                    fileIcon.style.width = "24px";
                    fileIcon.style.height = "24px";
                    fileIcon.style.marginRight = "10px";
                    fileLink.appendChild(fileIcon);
                }
                const fileName = document.createElement("span");
                fileName.textContent = file.name;
                fileName.style.marginLeft = "10px";
                fileLink.appendChild(fileName);
                fileDiv.appendChild(fileLink);
                uploadedFilesDiv.appendChild(fileDiv);
            });
        } else {
            uploadPlaceholder.style.display = "block";
            uploadedFilesDiv.style.display = "none";
        }
    }
</script>
</body>
</html>
<?php
} else {
    header("location: ./signin.php");
    exit;
}
?>
