<?php
require("database.php");

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (isset($_SESSION["user"])) {
    $email = $_SESSION["user"];
    $acc_id = $_SESSION["acc_id"];

    // Fetch user details
    $sql4 = "SELECT * FROM `user-list` WHERE `acc_id` = ?"; 
    $stmt = mysqli_prepare($conn, $sql4);
    mysqli_stmt_bind_param($stmt, "s", $acc_id);
    mysqli_stmt_execute($stmt);
    $result4 = mysqli_stmt_get_result($stmt);

    if ($row4 = mysqli_fetch_assoc($result4)) { 
        $fullname = $row4['name']; 
        $email2 = $row4['email']; 

        if (is_null($fullname) || trim($fullname) === "") {
            $fullname = $email2; 
        }
    } else {
        echo "No record found!";
        exit;
    }

    // Fetch offboarding details for viewing
    if (isset($_GET['offboarding_id'])) {
        $offboarding_id = $_GET['offboarding_id'];

        // Enhanced SQL Query with JOINs
        $sql_edit = "
            SELECT 
                ol.*, 
                pl.position_name, 
                dl.department_name, 
                ml.manager_name 
            FROM 
                `offboarding_list` ol
            LEFT JOIN 
                `position_list` pl ON ol.position_id = pl.position_id
            LEFT JOIN 
                `department_list` dl ON ol.department_id = dl.department_id
            LEFT JOIN 
                `manager_list` ml ON ol.reporting_manager_id = ml.manager_id
            WHERE 
                ol.offboarding_id = ?
        ";
        $stmt_edit = mysqli_prepare($conn, $sql_edit);
        mysqli_stmt_bind_param($stmt_edit, "s", $offboarding_id);
        mysqli_stmt_execute($stmt_edit);
        $result_edit = mysqli_stmt_get_result($stmt_edit);

        if ($row_edit = mysqli_fetch_assoc($result_edit)) {
            $employee_id = $row_edit["employee_id"];
            $employee_email = $row_edit["employee_email"]; // New Field
            $name = $row_edit["name"];
            $position = $row_edit["position_name"]; // Updated
            $department = $row_edit["department_name"]; // Updated
            $personal_email = $row_edit["personal_email"];
            $physical_last_day = $row_edit["physical_last_day"];
            $official_last_day = $row_edit["official_last_day"];
            $reporting_manager = $row_edit["manager_name"]; // Updated
            $remark = $row_edit["remark"];
            $file_path = $row_edit["file_path"];
        } else {
            echo "<script>alert('Offboarding record not found!'); window.location='offboarding_list.php';</script>";
            exit;
        }

        // Fetch checklist items
        $sql_checklist = "SELECT * FROM `offboarding_checklist`";
        $result_checklist = mysqli_query($conn, $sql_checklist);

        // Fetch checklist status
        $checklist_status = [];
        $sql_checklist_status = "SELECT * FROM `offboarding_checklist_status` WHERE `offboarding_id` = ?";
        $stmt_checklist_status = mysqli_prepare($conn, $sql_checklist_status);
        mysqli_stmt_bind_param($stmt_checklist_status, "s", $offboarding_id);
        mysqli_stmt_execute($stmt_checklist_status);
        $result_checklist_status = mysqli_stmt_get_result($stmt_checklist_status);

        while ($row_checklist_status = mysqli_fetch_assoc($result_checklist_status)) {
            $checklist_status[$row_checklist_status['checklist_id']] = $row_checklist_status['is_checked'];
        }

        // Decode existing file paths
        $existingFiles = [];
        if (!empty($file_path)) {
            $existingFiles = json_decode($file_path, true);
        }
    } else {
        echo "<script>alert('No offboarding ID provided!'); window.location='offboarding_list.php';</script>";
        exit;
    }

    // Fetch persons in charge
    $sql_persons_in_charge = "SELECT person_in_charge_id, person_in_charge_name, email_address FROM `person_in_charge_list`";
    $result_persons_in_charge = mysqli_query($conn, $sql_persons_in_charge);

    // Fetch selected persons in charge for this offboarding
    // Assuming you have stored them as comma-separated values in `person_in_charge_ids`
    $selected_persons_in_charge_ids = [];
    if (isset($row_edit['person_in_charge_ids']) && !empty($row_edit['person_in_charge_ids'])) {
        $selected_persons_in_charge_ids = explode(',', $row_edit['person_in_charge_ids']);
    }
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0">
    <meta name="description" content="CLL Ticketing System">
    <meta name="author" content="Powered by CLL Systems Sdn Bhd">
    <meta name="robots" content="noindex, nofollow">
    <title>CLLXWARE - View Offboarding Details</title>

    <!-- Favicon -->
    <link rel='icon' type='image/x-icon' href='https://www.cllsystems.com/wp-content/themes/cllsystems/img/favicon.png'>

    <!-- Stylesheets -->
    <link rel="stylesheet" href="assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="assets/css/animate.css">
    <link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">
    <link rel="stylesheet" href="assets/css/dataTables.bootstrap4.min.css">
    <link rel="stylesheet" href="assets/plugins/fontawesome/css/fontawesome.min.css">
    <link rel="stylesheet" href="assets/plugins/fontawesome/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">

    <!-- Custom CSS for better form design -->
    <style>
        .form-section-title {
            margin-bottom: 20px;
            font-size: 18px;
            font-weight: bold;
            color: #000;
            text-transform: uppercase;
        }
        .card {
            border: 1px solid #e0e0e0;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .existing-files img {
            max-width: 100px;
            max-height: 100px;
        }
        .download-file {
            margin-left: 15px;
            color: blue;
            text-decoration: none;
            font-weight: bold;
        }
        .download-file:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
<div id="global-loader">
    <div class="whirly-loader"></div>
</div>

<div class="main-wrapper">
<?php 
    $role_id = $_SESSION["role_id"];
    $sql20 = "SELECT * FROM `role-list` WHERE `role-id` = ?";
    $stmt20 = mysqli_prepare($conn, $sql20);
    mysqli_stmt_bind_param($stmt20, "s", $role_id);
    mysqli_stmt_execute($stmt20);
    $result20 = mysqli_stmt_get_result($stmt20);

    if ($row20 = mysqli_fetch_assoc($result20)) {
        $view = $row20['offboarding_view'];
        $all = $row20['offboarding_all'];
        if($view != '1' and $all !='1'){
            header("location: ./index.php");
            exit;
        }
    } else {
        echo "<script>alert('Role data not found');</script>";
        exit;
    }

    mysqli_stmt_close($stmt20);
    include("header.php");
?>
</div>

<div class="page-wrapper">
    <div class="content">
        <div class="page-header">
            <div class="page-title">
                <h4>View Offboarding Details</h4>
                <h6>Detailed information about the offboarding process.</h6>
            </div>
            <div class="page-btn">
                <a href="offboarding_list.php" class="btn btn-added">Back to Offboarding List</a>
            </div>
        </div>

        <div class="card">
            <div class="card-body">
                <table class="table table-bordered">
                    <tbody>

                        <tr>
                            <th scope="row" style="width: 25%;">Employee ID</th>
                            <td><?php echo htmlspecialchars($employee_id); ?></td>
                        </tr>
                        <tr>
                            <th scope="row">Name</th>
                            <td><?php echo htmlspecialchars($name); ?></td>
                        </tr>
                        <tr>
                            <th scope="row">Position</th>
                            <td><?php echo htmlspecialchars($position); ?></td>
                        </tr>
                        <tr>
                            <th scope="row">Department</th>
                            <td><?php echo htmlspecialchars($department); ?></td>
                        </tr>
                        <tr>
                            <th scope="row">Employee Email</th>
                            <td><?php echo htmlspecialchars($employee_email); ?></td>
                        </tr>
                        <tr>
                            <th scope="row">Personal Email</th>
                            <td><?php echo htmlspecialchars($personal_email); ?></td>
                        </tr>
                        <tr>
                            <th scope="row">Physical Last Day</th>
                            <td><?php echo htmlspecialchars($physical_last_day); ?></td>
                        </tr>
                        <tr>
                            <th scope="row">Official Last Day</th>
                            <td><?php echo htmlspecialchars($official_last_day); ?></td>
                        </tr>
                        <tr>
                            <th scope="row">Reporting Manager</th>
                            <td><?php echo htmlspecialchars($reporting_manager); ?></td>
                        </tr>
                        <tr>
                            <th scope="row">Remark</th>
                            <td><?php echo nl2br(htmlspecialchars($remark)); ?></td>
                        </tr>
                        <tr>
                            <th scope="row">Persons in Charge</th>
                            <td>
                                <?php
                                    if (!empty($selected_persons_in_charge_ids)) {
                                        // Fetch names of selected persons in charge
                                        $persons_in_charge_names = [];
                                        foreach ($selected_persons_in_charge_ids as $pic_id) {
                                            $sql_pic = "SELECT person_in_charge_name, email_address FROM `person_in_charge_list` WHERE `person_in_charge_id` = ?";
                                            $stmt_pic = mysqli_prepare($conn, $sql_pic);
                                            mysqli_stmt_bind_param($stmt_pic, "s", $pic_id);
                                            mysqli_stmt_execute($stmt_pic);
                                            $result_pic = mysqli_stmt_get_result($stmt_pic);
                                            if ($row_pic = mysqli_fetch_assoc($result_pic)) {
                                                $persons_in_charge_names[] = htmlspecialchars($row_pic['person_in_charge_name']) . " (" . htmlspecialchars($row_pic['email_address']) . ")";
                                            }
                                            mysqli_stmt_close($stmt_pic);
                                        }
                                        echo implode(", ", $persons_in_charge_names);
                                    } else {
                                        echo "None";
                                    }
                                ?>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">Files</th>
                            <td>
                                <?php
                                    if (!empty($existingFiles)) {
                                        foreach ($existingFiles as $file_path) {
                                            $file_name = basename($file_path);
                                            $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));
                                            if (in_array($file_ext, ['jpg', 'jpeg', 'png', 'gif'])) {
                                                echo "<a href='" . htmlspecialchars($file_path) . "' target='_blank'><img src='" . htmlspecialchars($file_path) . "' alt='" . htmlspecialchars($file_name) . "' style='max-width:100px; max-height:100px; margin-right:10px;'></a>";
                                            } else {
                                                echo "<a href='" . htmlspecialchars($file_path) . "' download>" . htmlspecialchars($file_name) . "</a><br>";
                                            }
                                        }
                                    } else {
                                        echo "No files uploaded.";
                                    }
                                ?>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">Checklist</th>
                            <td>
                            <!-- Adjusted table without the Status column -->
                            <table class="table table-sm table-bordered">
                                <thead>
                                    <tr>
                                        <th>Completed Checklist Items</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php 
                                    mysqli_data_seek($result_checklist, 0);
                                    while ($checklist_item = mysqli_fetch_assoc($result_checklist)): 
                                        if (isset($checklist_status[$checklist_item['checklist_id']]) && $checklist_status[$checklist_item['checklist_id']] == 1):
                                    ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($checklist_item['checklist_name']); ?></td>
                                    </tr>
                                    <?php 
                                        endif;
                                    endwhile; 
                                    ?>
                                </tbody>
                            </table>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

    </div>
</div>
</div>

<!-- Scripts -->
<script src="assets/js/jquery-3.6.0.min.js"></script>
<script src="assets/js/feather.min.js"></script>
<script src="assets/js/jquery.slimscroll.min.js"></script>
<script src="assets/js/jquery.dataTables.min.js"></script>
<script src="assets/js/dataTables.bootstrap4.min.js"></script>
<script src="assets/js/bootstrap.bundle.min.js"></script>
<script src="assets/plugins/select2/js/select2.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalert2.all.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalerts.min.js"></script>
<script src="assets/js/script.js"></script>
</body>
</html>
<?php
} else {
    header("location: ./signin.php");
    exit;
}
?>
