<?php
// Main cron job file for onboarding notifications
// This file should be called by the server's cron job at the configured time
require_once __DIR__ . '/database.php';
require_once __DIR__ . '/MAILER/vendor/autoload.php';
use <PERSON><PERSON>Mailer\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\PHPMailer\Exception;

// Set timezone to Malaysia
date_default_timezone_set('Asia/Kuala_Lumpur');

// Log function for debugging (write to logs/onboarding_cron.log)
function log_message($message) {
    $timestamp = date('Y-m-d H:i:s');
    if (!file_exists(__DIR__ . '/logs')) {
        @mkdir(__DIR__ . '/logs', 0755, true);
    }
    $log_file = __DIR__ . '/logs/onboarding_cron.log';
    $entry = "[$timestamp] $message" . PHP_EOL;
    file_put_contents($log_file, $entry, FILE_APPEND | LOCK_EX);
    // Also echo for manual runs
    echo $entry;
}

log_message("Cron job started");

try {
    // Get cron configuration
    $config_query = "SELECT * FROM `onboarding_cron_config` WHERE `config_type` = 'cron_reminder' AND `is_enabled` = 1";
    $config_result = mysqli_query($conn, $config_query);
    $config = mysqli_fetch_assoc($config_result);

    if (!$config) {
        log_message("Cron job is disabled or not configured. Exiting.");
        exit;
    }

    // Check if current time matches configured time (1-minute tolerance), allow manual force override
    $current_time = date('H:i');
    $config_time = $config['cron_time'] ?? '09:00';
    $time_diff = abs(strtotime($current_time) - strtotime($config_time));
    $force_send = isset($_GET['force_send']) && $_GET['force_send'] == '1';

    if (!$force_send && $time_diff > 60) {
        log_message("Current time ($current_time) doesn't match configured time ($config_time). Exiting.");
        exit;
    }
    if ($force_send) {
        log_message("Force send enabled - bypassing time check.");
    }

    log_message("Time matches (or forced). Processing onboarding notifications...");

    // Get today's onboarding records
    $today = date('Y-m-d');
    $query = "
        SELECT ol.*,
               dl.department_name,
               ml.manager_name,
               ofl.office_name as office_name_actual
        FROM `onboarding_list` ol
        LEFT JOIN `department_list` dl ON ol.department_id = dl.department_id
        LEFT JOIN `manager_list` ml ON ol.manager_id = ml.manager_id
        LEFT JOIN `office_list` ofl ON ol.office_id = ofl.office_id
        WHERE DATE(ol.onboarding_date) = ?
        ORDER BY ol.onboarding_date ASC
    ";

    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 's', $today);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    $onboarding_records = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $onboarding_records[] = $row;
    }
    mysqli_stmt_close($stmt);

    if (empty($onboarding_records)) {
        log_message("No onboarding records found for today ($today). Exiting.");
        exit;
    }

    log_message("Found " . count($onboarding_records) . " onboarding records for today");

    // Only send general reminder to HR team - no individual emails

    // Send general reminder email to configured recipients
    send_general_onboarding_reminder($onboarding_records, $config, $conn);
    log_message("Sent general onboarding reminder to configured recipients");

    log_message("Cron job completed successfully");

} catch (Exception $e) {
    log_message("Error: " . $e->getMessage());
}

// Function to send company account details to employee
function send_employee_company_account_email($record, $conn) {
    try {
        $mail = new PHPMailer(true);
        $mail->isSMTP();
        $mail->Host='smtp.office365.com';
        $mail->SMTPAuth=true;
        $mail->Username='<EMAIL>';
        $mail->Password='&[i3F6}0hOw6';
        $mail->SMTPSecure='tls';
        $mail->Port=587;
        $mail->setFrom('<EMAIL>','CLLXWARE');

        $mail->addAddress($record['personal_email']);

        $mail->Subject = 'Your Company Account Details - ' . $record['full_name'];
        $mail->isHTML(true);

        $mail->Body = "
        <html>
        <head>
            <meta charset='UTF-8'>
        </head>
        <body>
            <h3>Welcome to the Team!</h3>
            <p>Dear " . htmlspecialchars($record['full_name']) . ",</p>
            <p>Your company account has been set up. Here are your login details:</p>
            
            <div style='background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>
                <h4>Your Company Account Details:</h4>
                <p><strong>Email:</strong> " . htmlspecialchars($record['company_email']) . "</p>
                <p><strong>Password:</strong> " . htmlspecialchars(base64_decode($record['company_email_password'])) . "</p>
                <p><strong>Onboarding Date:</strong> " . htmlspecialchars($record['onboarding_date']) . "</p>
            </div>

            <div style='background-color: #fff3cd; padding: 15px; border-radius: 8px; margin: 20px 0;'>
                <h4>Important Information:</h4>
                <p>• IT support has been assigned for your laptop setup</p>
                <p>• You will receive a calendar invite for your onboarding session</p>
                <p>• Please keep your login credentials secure</p>
                <p>• Change your password after first login if required</p>
            </div>

            <p>If you have any questions, please contact HR or IT support.</p>
            <p>Best regards,<br>HR Team</p>
        </body>
        </html>";

        $mail->send();

    } catch (Exception $e) {
        error_log('Employee company account email error: ' . $e->getMessage());
    }
}

// Function to send IT support daily notification
function send_it_support_daily_notification($record, $conn) {
    // Get IT support configuration
    $config_query = "SELECT * FROM `onboarding_email_config` WHERE `config_type` = 'it_support'";
    $config_result = mysqli_query($conn, $config_query);
    $config = mysqli_fetch_assoc($config_result);

    if (!$config || empty($config['email_addresses'])) {
        error_log('IT support email addresses not configured');
        return;
    }

    // Get IT support emails
    $it_emails = array_filter(array_map('trim', explode("\n", $config['email_addresses'])));

    if (empty($it_emails)) {
        error_log('No valid IT support email addresses found');
        return;
    }

    // Get time configuration for display (Malaysia timezone)
    $assignment_time = $config['cron_time'] ?? '09:00';
    $start_time = $assignment_time . ':00';
    $end_time = date('H:i:s', strtotime($start_time . ' +1 hour'));
    $display_start_time = date('g:i A', strtotime($start_time)) . ' (Malaysia Time)';
    $display_end_time = date('g:i A', strtotime($end_time)) . ' (Malaysia Time)';

    try {
        $mail = new PHPMailer(true);
        $mail->isSMTP();
        $mail->Host='smtp.office365.com';
        $mail->SMTPAuth=true;
        $mail->Username='<EMAIL>';
        $mail->Password='&[i3F6}0hOw6';
        $mail->SMTPSecure='tls';
        $mail->Port=587;
        $mail->setFrom('<EMAIL>','CLLXWARE');

        // Send to all IT support emails
        foreach ($it_emails as $it_email) {
            $mail->clearAddresses();
            $mail->addAddress($it_email);

            // Generate completion token for this IT member
            $completion_token = hash('sha256', $record['onboarding_id'] . $it_email . 'IT_COMPLETION_2024');
            $base_url = (isset($_SERVER['HTTP_HOST']) ? ( (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://') . $_SERVER['HTTP_HOST'] . dirname($_SERVER['PHP_SELF']) ) : 'https://cis.cllsystems.com:9443/staging');
            $completion_link = rtrim($base_url,'/')."/it_completion.php?id=".urlencode($record['onboarding_id'])."&email=".urlencode($it_email)."&token=".urlencode($completion_token);

            $email_subject = $config['email_subject'] ?? 'Laptop Setup Required for New Employee';
            $email_subject = str_replace('{employee_name}', $record['full_name'], $email_subject);
            $mail->Subject = $email_subject;
            $mail->isHTML(true);

            // Use configured template or default
            $email_template = $config['email_template'] ?? 'Dear IT Support Team,

A new employee requires laptop setup:

Employee Name: {employee_name}
Employee ID: {employee_id}
Department: {department}
Designation: {designation}
Onboarding Date: {onboarding_date}
Time: {session_time}
Personal Email: {personal_email}

Please complete the laptop setup and mark it as complete using the link below.

Best regards,
HR Team';

            // Generate workflow link
            $base_url = (isset($_SERVER['HTTP_HOST']) ? ( (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://') . $_SERVER['HTTP_HOST'] . dirname($_SERVER['PHP_SELF']) ) : 'https://cis.cllsystems.com:9443/staging');
            $workflow_link = rtrim($base_url,'/')."/onboarding_workflow.php?id=".urlencode($record['onboarding_id']);

            // Replace template variables
            $email_body = str_replace('{employee_name}', htmlspecialchars($record['full_name']), $email_template);
            $email_body = str_replace('{employee_id}', htmlspecialchars($record['employee_id']), $email_body);
            $email_body = str_replace('{department}', htmlspecialchars($record['department_name'] ?? 'Not specified'), $email_body);
            $email_body = str_replace('{designation}', htmlspecialchars($record['job_title']), $email_body);
            $email_body = str_replace('{onboarding_date}', htmlspecialchars($record['onboarding_date']), $email_body);
            $email_body = str_replace('{reporting_manager}', htmlspecialchars($record['manager_name'] ?? 'Not assigned'), $email_body);
            $email_body = str_replace('{office_name}', htmlspecialchars($record['office_name_actual'] ?? 'Not specified'), $email_body);
            $email_body = str_replace('{workflow_link}', $workflow_link, $email_body);
            $email_body = str_replace('{session_time}', $display_start_time . " - " . $display_end_time, $email_body);
            $email_body = str_replace('{company_email}', htmlspecialchars($record['company_email']), $email_body);
            $email_body = str_replace('{company_password}', htmlspecialchars(base64_decode($record['company_email_password'])), $email_body);
            $email_body = str_replace('{personal_email}', htmlspecialchars($record['personal_email']), $email_body);
            // Handle personal image - show image or remove the line if NULL
            if (!empty($record['personal_image']) && file_exists($record['personal_image'])) {
                // Use embedded image for better email client compatibility
                $image_path = $record['personal_image'];
                $cid = 'employee_photo_' . $record['id'];

                // Add embedded attachment
                $mail->addEmbeddedImage($image_path, $cid, basename($image_path));

                // Use CID reference in email body
                $email_body = str_replace('{personal_image}', '<img src="cid:' . $cid . '" alt="Personal Image" style="max-width: 150px; max-height: 150px; border-radius: 8px;">', $email_body);
            } else {
                // Remove the personal image line entirely if no image
                $email_body = preg_replace('/.*Personal Image.*{personal_image}.*\n?/', '', $email_body);
            }

            $mail->Body = "
            <html>
            <body>
                " . nl2br($email_body) . "

                <div style='background-color: #f8f9fa; padding: 15px; border-left: 4px solid #28a745; margin: 20px 0;'>
                    <h4>Action Required:</h4>
                    <p>Please complete the laptop setup and click the link below to mark it as complete:</p>
                    <p><a href='" . $completion_link . "' style='background-color: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Mark Setup Complete</a></p>
                </div>
            </body>
            </html>";

            $mail->send();
        }

    } catch (Exception $e) {
        error_log('IT support daily notification email error: ' . $e->getMessage());
    }
}

// Function to send general onboarding reminder
function send_general_onboarding_reminder($records, $config, $conn) {
    $email_addresses = array_filter(array_map('trim', explode("\n", $config['email_addresses'])));
    
    if (empty($email_addresses)) {
        error_log('No email addresses configured for general reminder');
        return;
    }

    // Build onboarding list
    $onboarding_list = "";
    foreach ($records as $record) {
        $onboarding_list .= "- " . $record['full_name'] . " (" . $record['employee_id'] . ") - " . $record['department_name'] . "\n";
    }

    // Replace template variables
    $email_body = str_replace('{onboarding_list}', $onboarding_list, $config['email_template']);
    $email_body = str_replace('{current_date}', date('Y-m-d'), $email_body);
    $email_body = str_replace('{total_count}', count($records), $email_body);

    try {
        $mail = new PHPMailer(true);
        $mail->isSMTP();
        $mail->Host='smtp.office365.com';
        $mail->SMTPAuth=true;
        $mail->Username='<EMAIL>';
        $mail->Password='&[i3F6}0hOw6';
        $mail->SMTPSecure='tls';
        $mail->Port=587;
        $mail->setFrom('<EMAIL>','CLLXWARE');

        foreach ($email_addresses as $email) {
            $mail->addAddress($email);
        }

        $mail->isHTML(false);
        $email_subject = str_replace('{employee_name}', $record['full_name'], $config['email_subject']);
        $mail->Subject = $email_subject;
        $mail->Body = $email_body;
        $mail->send();

    } catch (Exception $e) {
        error_log('General onboarding reminder email error: ' . $e->getMessage());
    }
}
?>
