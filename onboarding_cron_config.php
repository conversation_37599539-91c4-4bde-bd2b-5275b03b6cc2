<?php
require('database.php');

// Check if user is logged in and has admin permissions
if (!isset($_SESSION['user'])) {
    header("location: ./signin.php");
    exit;
}

// Check admin permissions
$role_id = $_SESSION["role_id"];
$sql_admin = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
$result_admin = mysqli_query($conn, $sql_admin);
$row_admin = mysqli_fetch_assoc($result_admin);

if (!$row_admin || $row_admin['onboarding_all'] != '1') {
    echo "<script>alert('Access denied. Admin permissions required.'); window.location='dashboard.php';</script>";
    exit;
}

$current_user = $_SESSION['user'];

// Create cron configuration table if it doesn't exist
$create_table_sql = "CREATE TABLE IF NOT EXISTS `onboarding_cron_config` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `config_type` varchar(50) NOT NULL,
    `email_addresses` TEXT NULL,
    `cron_time` varchar(20) NULL,
    `is_enabled` TINYINT(1) NOT NULL DEFAULT 1,
    `email_subject` varchar(255) NULL,
    `email_template` TEXT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `config_type` (`config_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

mysqli_query($conn, $create_table_sql);

// Get existing configurations
$configs = [];
$result = mysqli_query($conn, "SELECT * FROM `onboarding_cron_config`");
while ($row = mysqli_fetch_assoc($result)) {
    $configs[$row['config_type']] = $row;
}

// Get email configurations for dropdown
$email_configs = [];
$email_result = mysqli_query($conn, "SELECT * FROM `email_config_list` ORDER BY `config_name`");
while ($row = mysqli_fetch_assoc($email_result)) {
    $email_configs[] = $row;
}

// Default templates
$default_reminder_template = "<!DOCTYPE html>
<html>
<head>
    <meta charset='UTF-8'>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 20px; background-color: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); overflow: hidden; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px 20px; text-align: center; }
        .header h1 { margin: 0; font-size: 24px; font-weight: 300; }
        .content { padding: 30px; }
        .employee-info { background: #f8f9fa; border-radius: 6px; padding: 20px; margin: 20px 0; }
        .info-row { display: flex; margin-bottom: 12px; }
        .info-label { font-weight: 600; color: #495057; min-width: 140px; }
        .info-value { color: #6c757d; }
        .image-container { text-align: center; margin: 20px 0; }
        .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #6c757d; font-size: 14px; }
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <h1>Daily Onboarding Reminder</h1>
            <p style='margin: 10px 0 0 0; opacity: 0.9;'>New Joiner Information</p>
        </div>
        <div class='content'>
            <p>Dear Team,</p>
            <p>This is a reminder about today's onboarding activities:</p>

            <div class='employee-info'>
                <div class='info-row'>
                    <span class='info-label'>Name:</span>
                    <span class='info-value'>{employee_name}</span>
                </div>
                <div class='info-row'>
                    <span class='info-label'>Employee ID:</span>
                    <span class='info-value'>{employee_id}</span>
                </div>
                <div class='info-row'>
                    <span class='info-label'>Department:</span>
                    <span class='info-value'>{department}</span>
                </div>
                <div class='info-row'>
                    <span class='info-label'>Designation:</span>
                    <span class='info-value'>{designation}</span>
                </div>
                <div class='info-row'>
                    <span class='info-label'>Onboarding Date:</span>
                    <span class='info-value'>{onboarding_date}</span>
                </div>
                <div class='info-row'>
                    <span class='info-label'>Reporting Manager:</span>
                    <span class='info-value'>{reporting_manager}</span>
                </div>
                <div class='info-row'>
                    <span class='info-label'>Office Location:</span>
                    <span class='info-value'>{office_name}</span>
                </div>
                <div class='info-row'>
                    <span class='info-label'>Company Email:</span>
                    <span class='info-value'>{company_email}</span>
                </div>
            </div>

            <div class='image-container'>
                {personal_image}
            </div>

            <p>Please ensure all preparations are completed for the new joiners.</p>
        </div>
        <div class='footer'>
            <p><strong>Best regards,</strong><br>HR Team</p>
        </div>
    </div>
</body>
</html>";

$default_it_reminder_template = "Dear IT Support Team,

Today's Laptop Setup Schedule:

{it_assignment_list}

Please ensure you are prepared for the laptop setup sessions at the scheduled times.

Important:
- Check that all necessary software and accounts are ready
- Prepare hardware and accessories
- Review employee requirements and company email credentials
- Use the completion link after finishing each setup

Best regards,
IT Support Team";

// Handle form submissions
if ($_POST) {
    $success_message = "";

    // Cron Job Email Configuration
    if (isset($_POST['update_cron'])) {
        $cron_emails = $_POST['cron_emails'] ?? '';
        $cron_time = $_POST['cron_time'] ?? '';
        $is_enabled = isset($_POST['is_enabled']) ? 1 : 0;
        $email_subject = $_POST['email_subject'] ?? '';
        $email_template = $_POST['email_template'] ?? '';

        $sql = "INSERT INTO `onboarding_cron_config` (`config_type`, `email_addresses`, `cron_time`, `is_enabled`, `email_subject`, `email_template`)
                VALUES ('cron_reminder', ?, ?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE
                `email_addresses` = VALUES(`email_addresses`),
                `cron_time` = VALUES(`cron_time`),
                `is_enabled` = VALUES(`is_enabled`),
                `email_subject` = VALUES(`email_subject`),
                `email_template` = VALUES(`email_template`)";
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, 'ssiss', $cron_emails, $cron_time, $is_enabled, $email_subject, $email_template);
        mysqli_stmt_execute($stmt);
        mysqli_stmt_close($stmt);

        $success_message = "Cron job configuration updated successfully!";

        // Refresh configs
        $configs = [];
        $result = mysqli_query($conn, "SELECT * FROM `onboarding_cron_config`");
        while ($row = mysqli_fetch_assoc($result)) {
            $configs[$row['config_type']] = $row;
        }
    }


}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0">
    <title>Onboarding Cron Job Setup - CLLXWARE</title>
    <link rel="shortcut icon" type="image/x-icon" href="assets/img/favicon.jpg">
    <link rel="stylesheet" href="assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="assets/css/animate.css">
    <link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">
    <link rel="stylesheet" href="assets/css/dataTables.bootstrap4.min.css">
    <link rel="stylesheet" href="assets/plugins/fontawesome/css/fontawesome.min.css">
    <link rel="stylesheet" href="assets/plugins/fontawesome/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">

    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #64748b;
            --success-color: #059669;
            --warning-color: #d97706;
            --info-color: #0ea5e9;
            --light-bg: #f8fafc;
            --border-color: #e2e8f0;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
        }

        html { scroll-behavior: smooth; }

        body {
            background-color: var(--light-bg);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }

        .page-wrapper {
            background: transparent;
        }

        .content {
            padding: 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        /* Modern Page Header */
        .page-header {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-color);
        }

        .page-title h4 {
            color: var(--text-primary);
            font-weight: 700;
            font-size: 1.875rem;
            margin-bottom: 0.5rem;
        }

        .page-title h6 {
            color: var(--text-secondary);
            font-weight: 400;
            font-size: 1rem;
            margin: 0;
        }

        /* Modern Cards */
        .card {
            background: white;
            border-radius: 16px;
            border: 1px solid var(--border-color);
            box-shadow: var(--shadow-sm);
            margin-bottom: 2rem;
            overflow: hidden;
        }

        .card-body {
            padding: 2rem;
        }

        /* Quick Navigation */
        .quick-nav {
            background: linear-gradient(135deg, var(--info-color), #0284c7);
            color: white;
            padding: 1rem 2rem;
            margin-bottom: 2rem;
            border-radius: 16px;
            box-shadow: var(--shadow-md);
        }

        .quick-nav .btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            transition: all 0.2s ease;
            text-decoration: none;
            font-weight: 500;
        }

        .quick-nav .btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-1px);
        }

        /* Section Headers */
        .section-header {
            background: var(--light-bg);
            padding: 1.5rem;
            margin: -2rem -2rem 2rem -2rem;
            border-bottom: 1px solid var(--border-color);
            position: relative;
        }

        .section-header::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: var(--info-color);
            border-radius: 0 4px 4px 0;
        }

        .section-header h5 {
            color: var(--text-primary);
            font-weight: 600;
            font-size: 1.25rem;
            margin: 0;
        }

        /* Form Styling */
        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            color: var(--text-primary);
            font-weight: 600;
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
            display: block;
        }

        .form-control {
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 0.75rem 1rem;
            font-size: 0.875rem;
            transition: all 0.2s ease;
            background: white;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
            outline: none;
        }

        .form-text {
            color: var(--text-secondary);
            font-size: 0.75rem;
            margin-top: 0.25rem;
        }

        /* Template Variables */
        .template-variables {
            background: var(--light-bg);
            padding: 1.5rem;
            border-radius: 12px;
            border: 1px solid var(--border-color);
        }

        .variable-tag {
            background: white;
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            padding: 0.375rem 0.75rem;
            border-radius: 6px;
            font-family: 'JetBrains Mono', 'Fira Code', monospace;
            font-size: 0.75rem;
            margin: 0.25rem;
            display: inline-block;
            cursor: pointer;
            transition: all 0.2s ease;
            font-weight: 500;
        }

        .variable-tag:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-1px);
            box-shadow: var(--shadow-sm);
        }

        /* Time Input */
        .time-input {
            display: flex;
            align-items: center;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .time-input input {
            width: 120px;
        }

        /* Cron Examples */
        .cron-examples {
            background: var(--light-bg);
            padding: 1.5rem;
            border-radius: 12px;
            border: 1px solid var(--border-color);
            margin-top: 1rem;
        }

        .cron-examples h6 {
            color: var(--text-primary);
            margin-bottom: 1rem;
            font-weight: 600;
        }

        .cron-examples ul {
            margin: 0;
            padding-left: 1.5rem;
        }

        .cron-examples li {
            color: var(--text-secondary);
            margin-bottom: 0.5rem;
            font-size: 0.875rem;
        }

        /* Email Config Selector */
        .email-config-selector {
            background: var(--light-bg);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .config-option {
            display: flex;
            align-items: center;
            padding: 1rem;
            margin: 0.5rem 0;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: white;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .config-option:hover {
            border-color: var(--primary-color);
            background: rgba(37, 99, 235, 0.05);
            transform: translateY(-1px);
            box-shadow: var(--shadow-sm);
        }

        .config-option.selected {
            border-color: var(--primary-color);
            background: rgba(37, 99, 235, 0.1);
        }

        .config-option input[type="checkbox"] {
            margin-right: 1rem;
            transform: scale(1.2);
        }

        .config-details {
            flex: 1;
        }

        .config-name {
            font-weight: 600;
            color: var(--text-primary);
            font-size: 0.875rem;
        }

        .config-emails {
            font-size: 0.75rem;
            color: var(--text-secondary);
            font-family: 'JetBrains Mono', 'Fira Code', monospace;
            margin-top: 0.25rem;
        }

        /* Buttons */
        .btn-submit {
            background: var(--primary-color);
            border: none;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 600;
            font-size: 0.875rem;
            transition: all 0.2s ease;
            width: 100%;
        }

        .btn-submit:hover {
            background: #1d4ed8;
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        /* Alerts */
        .alert {
            border-radius: 12px;
            border: none;
            padding: 1rem 1.5rem;
            margin-bottom: 2rem;
        }

        .alert-info {
            background: rgba(14, 165, 233, 0.1);
            color: var(--info-color);
            border-left: 4px solid var(--info-color);
        }

        .alert-success {
            background: rgba(5, 150, 105, 0.1);
            color: var(--success-color);
            border-left: 4px solid var(--success-color);
        }

        /* Preview Button Positioning */
        .position-relative .btn {
            box-shadow: var(--shadow-sm);
        }

        .position-relative .btn:hover {
            box-shadow: var(--shadow-md);
        }

        /* Modal Close Button Fix */
        .btn-close {
            background: transparent;
            border: none;
            font-size: 1.5rem;
            font-weight: 700;
            line-height: 1;
            color: #000;
            text-shadow: 0 1px 0 #fff;
            opacity: 0.5;
            padding: 0.25rem;
            width: 1.5rem;
            height: 1.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .btn-close:hover {
            opacity: 0.75;
        }

        .btn-close span {
            font-size: 1.5rem;
            line-height: 1;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .content { padding: 1rem; }
            .page-header { padding: 1.5rem; }
            .card-body { padding: 1.5rem; }
            .time-input { flex-direction: column; align-items: flex-start; }
        }
    </style>
</head>

<body>
<div id="global-loader">
    <div class="whirly-loader"> </div>
</div>

<div class="main-wrapper">
    <?php include 'header.php'; ?>

    <div class="page-wrapper">
        <div class="content">
            <div class="page-header">
                <div class="page-title">
                    <h4><i class="fas fa-clock me-2"></i>Cron Job Setup</h4>
                    <h6>Automate onboarding reminder emails with clear scheduling</h6>
                </div>
            </div>


            <?php if (isset($success_message)): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <?php endif; ?>





            <!-- Cron Job Configuration -->
            <div id="cron-config" class="card">
                <div class="card-body">
                    <div class="section-header cron">
                        <h5><i class="fas fa-clock me-2"></i>Cron Job Email Configuration</h5>
                    </div>

                    <form method="POST">
                        <div class="row">
                            <div class="col-lg-6 col-sm-6 col-12">
                                <div class="form-group">
                                    <label>Select Email Configurations *</label>
                                    <div class="email-config-selector">
                                        <p class="mb-3"><strong>Choose multiple email configurations:</strong></p>
                                        <?php foreach($email_configs as $config): ?>
                                        <div class="config-option" onclick="toggleConfig(this)">
                                            <input type="checkbox"
                                                   value="<?php echo htmlspecialchars($config['email_addresses']); ?>"
                                                   data-name="<?php echo htmlspecialchars($config['config_name']); ?>"
                                                   onchange="updateEmailList()">
                                            <div class="config-details">
                                                <div class="config-name"><?php echo htmlspecialchars($config['config_name']); ?></div>
                                                <div class="config-emails"><?php echo htmlspecialchars(substr($config['email_addresses'], 0, 60)) . (strlen($config['email_addresses']) > 60 ? '...' : ''); ?></div>
                                            </div>
                                        </div>
                                        <?php endforeach; ?>

                                        <div class="config-option" onclick="toggleConfig(this)">
                                            <input type="checkbox" value="custom" data-name="Custom" onchange="updateEmailList()">
                                            <div class="config-details">
                                                <div class="config-name">Custom Email Addresses</div>
                                                <div class="config-emails">Enter your own email addresses manually</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label>Final Email List *</label>
                                    <textarea class="form-control" name="cron_emails" id="cron_emails" rows="6" placeholder="Email addresses will appear here based on your selections above" required><?php echo htmlspecialchars($configs['cron_reminder']['email_addresses'] ?? '<EMAIL>'); ?></textarea>
                                    <small class="form-text text-muted">This list is automatically generated from your selections above. You can also edit manually.</small>
                                </div>
                            </div>

                            <div class="col-lg-6 col-sm-6 col-12">
                                <div class="form-group">
                                    <label>Cron Schedule Time *</label>
                                    <div class="time-input">
                                        <input type="time" class="form-control" name="cron_time" value="<?php echo htmlspecialchars($configs['cron_reminder']['cron_time'] ?? '09:00'); ?>" required>
                                        <span>daily</span>
                                    </div>
                                    <small class="form-text text-muted">Time when the cron job should run daily</small>
                                </div>

                                <div class="form-group">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="is_enabled" id="is_enabled" <?php echo (isset($configs['cron_reminder']) && $configs['cron_reminder']['is_enabled']) ? 'checked' : 'checked'; ?>>
                                        <label class="form-check-label" for="is_enabled">
                                            Enable Cron Job
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-12">
                                <div class="form-group">
                                    <label>Email Subject *</label>
                                    <input type="text" class="form-control" name="email_subject" value="<?php echo htmlspecialchars($configs['cron_reminder']['email_subject'] ?? 'Daily Onboarding Reminder - New Joiner: {employee_name}'); ?>" required>
                                    <small class="form-text text-muted">Note: {employee_name} will be automatically replaced with the actual employee name</small>
                                </div>
                            </div>

                            <div class="col-lg-12">
                                <div class="form-group">
                                    <label>Email Template *</label>
                                    <div class="position-relative">
                                        <textarea class="form-control" name="email_template" rows="8" required><?php echo htmlspecialchars($configs['cron_reminder']['email_template'] ?? $default_reminder_template); ?></textarea>
                                        <button type="button" class="btn btn-outline-primary btn-sm position-absolute" style="top: 8px; right: 8px; z-index: 10;" onclick="previewCronTemplate()" title="Preview Email">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-12">
                                <div class="cron-examples">
                                    <h6><i class="fas fa-lightbulb"></i> Cron Job Information:</h6>
                                    <p><strong>How it works:</strong></p>
                                    <ul>
                                        <li>The cron job will run daily at the specified time</li>
                                        <li>It will check for onboarding activities scheduled for the next day</li>
                                        <li>Reminder emails will be sent to the configured recipients</li>
                                        <li>The email will include a list of upcoming onboarding activities</li>
                                    </ul>
                                    <p><strong>Note:</strong> You need to set up the actual cron job on your server to run the script at the specified time.</p>
                                </div>
                            </div>

                            <div class="col-lg-12">
                                <button type="submit" name="update_cron" class="btn btn-submit">
                                    <i class="fas fa-save"></i> Update Cron Configuration
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>



        </div>
    </div>
</div>

<!-- Email Template Preview Modal -->
<div class="modal fade" id="templatePreviewModal" tabindex="-1" aria-labelledby="templatePreviewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="templatePreviewModalLabel">
                    <i class="fas fa-eye me-2"></i>Cron Email Template Preview
                </h5>
                <button type="button" class="btn-close" onclick="closeCronModal()" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <!-- Left side - Edit Email Template -->
                    <div class="col-lg-9 col-md-8">
                        <h6 class="mb-3"><i class="fas fa-edit me-2"></i>Edit Email Template</h6>
                        <div class="form-group mb-3">
                            <label class="form-label">Email Subject</label>
                            <input type="text" class="form-control" id="preview_email_subject" oninput="updateCronPreview()" onchange="updateCronPreview()" style="font-size: 14px;">
                        </div>
                        <div class="form-group mb-3">
                            <label class="form-label">Email Template</label>
                            <textarea class="form-control" id="preview_email_template" rows="20" oninput="updateCronPreview()" onchange="updateCronPreview()" style="font-family: 'Courier New', monospace; font-size: 14px; line-height: 1.5; min-height: 500px;"></textarea>
                            <div class="mt-2 p-3" style="background: #f8f9fa; border-radius: 8px; border: 1px solid #e2e8f0;">
                                <small class="text-muted">
                                    <strong>Available variables:</strong><br>
                                    <span class="d-inline-block me-2 mb-1"><code>{onboarding_list}</code></span>
                                    <span class="d-inline-block me-2 mb-1"><code>{current_date}</code></span>
                                    <span class="d-inline-block me-2 mb-1"><code>{total_count}</code></span>
                                    <span class="d-inline-block me-2 mb-1"><code>{employee_name}</code></span>
                                </small>
                            </div>
                        </div>
                    </div>

                    <!-- Right side - Live Preview -->
                    <div class="col-lg-3 col-md-4">
                        <h6 class="mb-3"><i class="fas fa-envelope me-2"></i>Live Preview</h6>
                        <div class="email-preview">
                            <div class="email-header mb-3 p-2" style="background: #e3f2fd; border-radius: 8px; border-left: 4px solid #2196f3; font-size: 12px;">
                                <strong>Subject:</strong><br><span id="previewSubject" style="word-break: break-word;"></span>
                            </div>
                            <div class="email-body p-2" id="previewBody" style="background: #f8f9fa; border-radius: 8px; border: 1px solid #e2e8f0; white-space: pre-wrap; font-family: Arial, sans-serif; line-height: 1.4; min-height: 400px; max-height: 500px; overflow-y: auto; font-size: 11px;">
                            </div>
                        </div>
                        <div class="alert alert-info mt-2 p-2">
                            <small><i class="fas fa-info-circle me-1"></i>Preview shows template with sample data. Variables will be replaced with actual onboarding data when emails are sent.</small>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" onclick="saveCronTemplateChanges()">
                    <i class="fas fa-save me-2"></i>Save Changes
                </button>
                <button type="button" class="btn btn-secondary" onclick="closeCronModal()">Cancel</button>
            </div>
        </div>
    </div>
</div>

<!-- Scripts -->
<script src="assets/js/jquery-3.6.0.min.js"></script>
<script src="assets/js/feather.min.js"></script>
<script src="assets/js/jquery.slimscroll.min.js"></script>
<script src="assets/js/jquery.dataTables.min.js"></script>
<script src="assets/js/dataTables.bootstrap4.min.js"></script>
<script src="assets/js/bootstrap.bundle.min.js"></script>
<script src="assets/plugins/select2/js/select2.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalert2.all.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalerts.min.js"></script>
<script>
    // Toggle configuration selection
    function toggleConfig(element) {
        const checkbox = element.querySelector('input[type="checkbox"]');
        checkbox.checked = !checkbox.checked;

        if (checkbox.checked) {
            element.classList.add('selected');
        } else {
            element.classList.remove('selected');
        }

        updateEmailList();
    }

    // Update email list based on selections
    function updateEmailList() {
        const checkboxes = document.querySelectorAll('.config-option input[type="checkbox"]:checked');
        const textarea = document.getElementById('cron_emails');
        const emailSet = new Set();
        let hasCustom = false;

        checkboxes.forEach(checkbox => {
            if (checkbox.value === 'custom') {
                hasCustom = true;
            } else {
                // Split email addresses and add to set to avoid duplicates
                const emails = checkbox.value.split('\n').map(email => email.trim()).filter(email => email);
                emails.forEach(email => emailSet.add(email));
            }
        });

        // Convert set back to array and join
        const emailList = Array.from(emailSet).join('\n');

        if (hasCustom && emailList) {
            // If custom is selected and there are other emails, append them
            textarea.value = emailList + '\n';
            textarea.readOnly = false;
            textarea.placeholder = 'Add custom email addresses below the existing ones';
        } else if (hasCustom) {
            // Only custom selected
            textarea.value = '';
            textarea.readOnly = false;
            textarea.placeholder = 'Enter custom email addresses, one per line';
        } else if (emailList) {
            // Only predefined configurations selected
            textarea.value = emailList;
            textarea.readOnly = false;
            textarea.placeholder = 'Email addresses from selected configurations';
        } else {
            // Nothing selected
            textarea.value = '';
            textarea.readOnly = false;
            textarea.placeholder = 'Select email configurations above or enter custom emails';
        }
    }

    // Initialize on page load
    document.addEventListener('DOMContentLoaded', function() {
        // Check current email list and try to match configurations
        const currentEmails = document.getElementById('cron_emails').value.trim();
        const checkboxes = document.querySelectorAll('.config-option input[type="checkbox"]');

        if (currentEmails) {
            const currentEmailArray = currentEmails.split('\n').map(email => email.trim()).filter(email => email);

            checkboxes.forEach(checkbox => {
                if (checkbox.value !== 'custom') {
                    const configEmails = checkbox.value.split('\n').map(email => email.trim()).filter(email => email);

                    // Check if all config emails are in current email list
                    const allMatch = configEmails.every(email => currentEmailArray.includes(email));

                    if (allMatch && configEmails.length > 0) {
                        checkbox.checked = true;
                        checkbox.closest('.config-option').classList.add('selected');
                    }
                }
            });
        }

        // Add click handlers to prevent checkbox toggle when clicking checkbox directly
        document.querySelectorAll('.config-option input[type="checkbox"]').forEach(checkbox => {
            checkbox.addEventListener('click', function(e) {
                e.stopPropagation();

                if (this.checked) {
                    this.closest('.config-option').classList.add('selected');
                } else {
                    this.closest('.config-option').classList.remove('selected');
                }

                updateEmailList();
            });
        });
    });

    // Copy tag function
    function copyTag(el){
        const text = el.textContent;
        navigator.clipboard.writeText(text);
        el.classList.add('bg-success','text-white');
        setTimeout(()=>{ el.classList.remove('bg-success','text-white'); },600);
    }

    let currentCronTemplateTextarea = null;
    let currentCronSubjectInput = null;

    // Preview cron email template
    function previewCronTemplate() {
        // Get the textarea and subject input
        currentCronTemplateTextarea = document.querySelector('textarea[name="email_template"]');
        currentCronSubjectInput = document.querySelector('input[name="email_subject"]');

        // Load current template and subject into modal
        document.getElementById('preview_email_template').value = currentCronTemplateTextarea.value;
        document.getElementById('preview_email_subject').value = currentCronSubjectInput.value;

        // Update preview
        updateCronPreview();

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('templatePreviewModal'));
        modal.show();
    }

    function updateCronPreview() {
        // Get template and subject from modal inputs
        const templateElement = document.getElementById('preview_email_template');
        const subjectElement = document.getElementById('preview_email_subject');

        if (!templateElement || !subjectElement) {
            console.log('Template or subject element not found');
            return;
        }

        const template = templateElement.value;
        const subject = subjectElement.value;

        // Sample data for cron template preview
        const sampleData = {
            '{onboarding_list}': `1. John Doe (EMP001) - Software Developer - IT Department
   Onboarding Date: 2024-01-15
   Manager: Jane Smith
   Office: Kuala Lumpur Office

2. Sarah Wilson (EMP002) - Marketing Specialist - Marketing Department
   Onboarding Date: 2024-01-15
   Manager: Mike Johnson
   Office: Kuala Lumpur Office`,
            '{current_date}': new Date().toLocaleDateString('en-MY', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            }),
            '{total_count}': '2',
            '{employee_name}': 'John Doe, Sarah Wilson'
        };

        // Replace template variables with sample data
        let previewContent = template;
        let previewSubject = subject;

        for (const [variable, value] of Object.entries(sampleData)) {
            const regex = new RegExp(variable.replace(/[{}]/g, '\\$&'), 'g');
            previewContent = previewContent.replace(regex, value);
            previewSubject = previewSubject.replace(regex, value);
        }

        // Update preview content
        const previewSubjectElement = document.getElementById('previewSubject');
        const previewBodyElement = document.getElementById('previewBody');

        if (previewSubjectElement) {
            previewSubjectElement.textContent = previewSubject;
        }
        if (previewBodyElement) {
            previewBodyElement.textContent = previewContent;
        }
    }

    function saveCronTemplateChanges() {
        // Get the edited template and subject from modal
        const editedTemplate = document.getElementById('preview_email_template').value;
        const editedSubject = document.getElementById('preview_email_subject').value;

        // Update the original form fields
        if (currentCronTemplateTextarea) {
            currentCronTemplateTextarea.value = editedTemplate;
        }
        if (currentCronSubjectInput) {
            currentCronSubjectInput.value = editedSubject;
        }

        // Close modal
        closeCronModal();

        // Show success message
        const toast = document.createElement('div');
        toast.className = 'toast-notification';
        toast.innerHTML = '<i class="fas fa-check-circle me-2"></i>Template updated successfully!';
        toast.style.cssText = 'position: fixed; top: 20px; right: 20px; background: #059669; color: white; padding: 12px 20px; border-radius: 8px; z-index: 9999; box-shadow: 0 4px 12px rgba(0,0,0,0.15);';
        document.body.appendChild(toast);

        setTimeout(() => {
            toast.remove();
        }, 3000);
    }

    function closeCronModal() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('templatePreviewModal'));
        if (modal) {
            modal.hide();
        } else {
            // Fallback if modal instance not found
            document.getElementById('templatePreviewModal').style.display = 'none';
            document.body.classList.remove('modal-open');
            const backdrop = document.querySelector('.modal-backdrop');
            if (backdrop) {
                backdrop.remove();
            }
        }
    }
</script>

<script src="assets/js/script.js"></script>

</body>
</html>
