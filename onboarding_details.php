<?php 
require("database.php");

if (isset($_SESSION["user"])) {
    $email = $_SESSION["user"];
    $acc_id = $_SESSION["acc_id"];
    
    // Fetch user details
    $sql4 = "SELECT * FROM `user-list` WHERE `acc_id` = ?"; 
    $stmt = mysqli_prepare($conn, $sql4);
    mysqli_stmt_bind_param($stmt, "s", $acc_id);
    mysqli_stmt_execute($stmt);
    $result4 = mysqli_stmt_get_result($stmt);
  
    if ($row4 = mysqli_fetch_assoc($result4)) { 
        $fullname = $row4['name']; 
        $email2 = $row4['email']; 

        // Use email if full name is empty
        if (is_null($fullname) || trim($fullname) === "") {
            $fullname = $email2; 
        }
    } else {
        echo "No record found!";
    }

    // Fetch onboarding details for viewing
    if (isset($_GET['onboarding_id'])) {
        $onboarding_id = $_GET['onboarding_id'];

        $sql_view = "SELECT ol.*,
                            dl.department_name as department,
                            ml.manager_name as reporting_manager,
                            ofl.office_name as office_name
                     FROM `onboarding_list` ol
                     LEFT JOIN `department_list` dl ON ol.department_id = dl.department_id
                     LEFT JOIN `manager_list` ml ON ol.manager_id = ml.manager_id
                     LEFT JOIN `office_list` ofl ON ol.office_id = ofl.office_id
                     WHERE ol.`onboarding_id` = ?";
        $stmt_view = mysqli_prepare($conn, $sql_view);
        mysqli_stmt_bind_param($stmt_view, "s", $onboarding_id);
        mysqli_stmt_execute($stmt_view);
        $result_view = mysqli_stmt_get_result($stmt_view);

        if ($row_view = mysqli_fetch_assoc($result_view)) {
            // Store the current data with fallback values
            $current_data = $row_view;

            // Add fallback values for missing data
            if (!isset($current_data['department']) || empty($current_data['department'])) {
                $current_data['department'] = 'Not specified';
            }
            if (!isset($current_data['reporting_manager']) || empty($current_data['reporting_manager'])) {
                $current_data['reporting_manager'] = 'Not specified';
            }
            if (!isset($current_data['office_name']) || empty($current_data['office_name'])) {
                $current_data['office_name'] = 'Not specified';
            }
        } else {
            echo "<script>alert('Onboarding record not found.'); window.location='onboarding_list.php';</script>";
            exit;
        }
        mysqli_stmt_close($stmt_view);
    } else {
        echo "<script>alert('No onboarding ID provided.'); window.location='onboarding_list.php';</script>";
        exit;
    }
} else {
    header("location: ./signin.php");
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0">
    <meta name="description" content="CLL Warehouse Inventory System">
    <meta name="author" content="Powered by CLL Systems Sdn Bhd">
    <meta name="robots" content="noindex, nofollow">
    <title>CLLXWARE - Onboarding Details</title>

    <link rel='icon' type='image/x-icon' href='https://www.cllsystems.com/wp-content/themes/cllsystems/img/favicon.png'>
    <link rel="stylesheet" href="assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="assets/css/animate.css">
    <link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">
    <link rel="stylesheet" href="assets/css/dataTables.bootstrap4.min.css">
    <link rel="stylesheet" href="assets/plugins/fontawesome/css/fontawesome.min.css">
    <link rel="stylesheet" href="assets/plugins/fontawesome/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
<div id="global-loader">
    <div class="whirly-loader"> </div>
</div>

<div class="main-wrapper">
    <?php 
    $role_id = $_SESSION["role_id"];
    $sql20 = "SELECT * FROM `role-list` WHERE `role-id` = ?";
    $stmt20 = mysqli_prepare($conn, $sql20);
    mysqli_stmt_bind_param($stmt20, "s", $role_id);
    mysqli_stmt_execute($stmt20);
    $result20 = mysqli_stmt_get_result($stmt20);

    if ($row20 = mysqli_fetch_assoc($result20)) {
        $all = $row20['onboarding_all'];
        $view = $row20['onboarding_view'];
        if($view != '1' and $all !='1'){
            header("location: ./index.php");
            exit;
        }
    } else {
        echo "<script>alert('Role data not found');</script>";
        exit;
    }

    mysqli_stmt_close($stmt20);
    include("header.php");
    ?>

    <div class="page-wrapper">
        <div class="content">
            <div class="page-header">
                <div class="page-title">
                    <h4>Onboarding Details</h4>
                    <h6>View onboarding information</h6>
                </div>
                <div class="page-btn">
                    <a href="onboarding_list.php" class="btn btn-secondary">Back to List</a>
                    <?php 
                    $role_id = $_SESSION["role_id"];
                    $sql20 = "SELECT * FROM `role-list` WHERE `role-id` = ?";
                    $stmt20 = mysqli_prepare($conn, $sql20);
                    mysqli_stmt_bind_param($stmt20, "s", $role_id);
                    mysqli_stmt_execute($stmt20);
                    $result20 = mysqli_stmt_get_result($stmt20);

                    if ($row20 = mysqli_fetch_assoc($result20)) {
                        $edit = $row20['onboarding_edit'];
                        $all = $row20['onboarding_all'];
                        if($edit == '1' or $all =='1'){
                            echo '<a href="edit_onboarding.php?onboarding_id=' . $onboarding_id . '" class="btn btn-primary ms-2">Edit</a>';
                        }
                    }
                    mysqli_stmt_close($stmt20);
                    ?>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <!-- Personal Image Row -->
                    <?php if (!empty($current_data['personal_image']) && file_exists($current_data['personal_image'])): ?>
                    <div class="row mb-4">
                        <div class="col-lg-3 col-md-4 col-sm-6 col-12">
                            <img src="<?php echo $current_data['personal_image']; ?>" alt="Personal Image" style="max-width: 220px; max-height: 220px; border-radius: 10px; object-fit: cover; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Employee Information Row -->
                    <div class="row">
                        <!-- Full Name -->
                        <div class="col-lg-6 col-sm-6 col-12">
                            <div class="form-group">
                                <label><strong>Full Name:</strong></label>
                                <p><?php echo htmlspecialchars($current_data['full_name']); ?></p>
                            </div>
                        </div>

                        <!-- Employee ID -->
                        <div class="col-lg-6 col-sm-6 col-12">
                            <div class="form-group">
                                <label><strong>Employee ID:</strong></label>
                                <p><?php echo htmlspecialchars($current_data['employee_id']); ?></p>
                            </div>
                        </div>

                        <!-- Preferred Name -->
                        <div class="col-lg-6 col-sm-6 col-12">
                            <div class="form-group">
                                <label><strong>Preferred Name:</strong></label>
                                <p><?php echo htmlspecialchars($current_data['preferred_name']); ?></p>
                            </div>
                        </div>

                        <!-- Department -->
                        <div class="col-lg-6 col-sm-6 col-12">
                            <div class="form-group">
                                <label><strong>Department:</strong></label>
                                <p><?php echo htmlspecialchars($current_data['department']); ?></p>
                            </div>
                        </div>

                        <!-- IC Number -->
                        <div class="col-lg-6 col-sm-6 col-12">
                            <div class="form-group">
                                <label><strong>IC Number:</strong></label>
                                <p><?php echo htmlspecialchars($current_data['ic_number']); ?></p>
                            </div>
                        </div>

                        <!-- Gender -->
                        <div class="col-lg-6 col-sm-6 col-12">
                            <div class="form-group">
                                <label><strong>Gender:</strong></label>
                                <p><?php echo htmlspecialchars($current_data['gender']); ?></p>
                            </div>
                        </div>

                        <!-- Job Title -->
                        <div class="col-lg-6 col-sm-6 col-12">
                            <div class="form-group">
                                <label><strong>Job Title:</strong></label>
                                <p><?php echo htmlspecialchars($current_data['job_title']); ?></p>
                            </div>
                        </div>

                        <!-- Personal Email -->
                        <div class="col-lg-6 col-sm-6 col-12">
                            <div class="form-group">
                                <label><strong>Personal Email:</strong></label>
                                <p><?php echo htmlspecialchars($current_data['personal_email']); ?></p>
                            </div>
                        </div>

                        <!-- Address -->
                        <div class="col-lg-12">
                            <div class="form-group">
                                <label><strong>Address:</strong></label>
                                <p><?php echo nl2br(htmlspecialchars($current_data['address'])); ?></p>
                            </div>
                        </div>

                        <!-- Phone Number -->
                        <div class="col-lg-6 col-sm-6 col-12">
                            <div class="form-group">
                                <label><strong>Phone Number:</strong></label>
                                <p><?php echo htmlspecialchars($current_data['phone_number']); ?></p>
                            </div>
                        </div>

                        <!-- Company Email -->
                        <div class="col-lg-6 col-sm-6 col-12">
                            <div class="form-group">
                                <label><strong>Company Email:</strong></label>
                                <p><?php echo htmlspecialchars($current_data['company_email']); ?></p>
                            </div>
                        </div>

                        <!-- Reporting Manager -->
                        <div class="col-lg-6 col-sm-6 col-12">
                            <div class="form-group">
                                <label><strong>Reporting Manager:</strong></label>
                                <p><?php echo htmlspecialchars($current_data['reporting_manager']); ?></p>
                            </div>
                        </div>

                        <!-- On Boarding Date -->
                        <div class="col-lg-6 col-sm-6 col-12">
                            <div class="form-group">
                                <label><strong>On Boarding Date:</strong></label>
                                <p><?php echo htmlspecialchars($current_data['onboarding_date']); ?></p>
                            </div>
                        </div>

                        <!-- Office Name -->
                        <div class="col-lg-6 col-sm-6 col-12">
                            <div class="form-group">
                                <label><strong>Office Name:</strong></label>
                                <p><?php echo htmlspecialchars($current_data['office_name']); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="assets/js/jquery-3.6.0.min.js"></script>
<script src="assets/js/feather.min.js"></script>
<script src="assets/js/jquery.slimscroll.min.js"></script>
<script src="assets/js/jquery.dataTables.min.js"></script>
<script src="assets/js/dataTables.bootstrap4.min.js"></script>
<script src="assets/js/bootstrap.bundle.min.js"></script>
<script src="assets/plugins/select2/js/select2.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalert2.all.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalerts.min.js"></script>
<script src="assets/js/script.js"></script>

</body>
</html>
