<?php
require("database.php");

if (!isset($_SESSION["user"])) {
    header("Location: login.php");
    exit;
}

// Create email config table if it doesn't exist
$create_table = "CREATE TABLE IF NOT EXISTS `onboarding_email_config` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `config_type` varchar(50) NOT NULL,
    `email_addresses` TEXT,
    `email_subject` varchar(255),
    `email_template` TEXT,
    PRIMARY KEY (`id`),
    UNIQUE KEY `config_type` (`config_type`)
)";
mysqli_query($conn, $create_table);

// Add cron_time column if it doesn't exist
$check_column = mysqli_query($conn, "SHOW COLUMNS FROM `onboarding_email_config` LIKE 'cron_time'");
if (mysqli_num_rows($check_column) == 0) {
    $add_cron_time = "ALTER TABLE `onboarding_email_config` ADD COLUMN `cron_time` TIME NULL";
    mysqli_query($conn, $add_cron_time);
}

// Handle form submission
if ($_POST) {

    // CTO Request Configuration
    if (isset($_POST['update_cto'])) {
        $cto_emails = $_POST['cto_emails'] ?? '';
        $cto_subject = $_POST['cto_subject'] ?? '';
        $cto_template = $_POST['cto_template'] ?? '';

        $stmt = mysqli_prepare($conn, "INSERT INTO `onboarding_email_config` (`config_type`, `email_addresses`, `email_subject`, `email_template`) VALUES ('cto_request', ?, ?, ?) ON DUPLICATE KEY UPDATE `email_addresses` = ?, `email_subject` = ?, `email_template` = ?");
        mysqli_stmt_bind_param($stmt, 'ssssss', $cto_emails, $cto_subject, $cto_template, $cto_emails, $cto_subject, $cto_template);
        mysqli_stmt_execute($stmt);
        mysqli_stmt_close($stmt);

        $success_message = "CTO Request email configuration updated successfully!";
    }

    // IT Support Configuration
    if (isset($_POST['update_it'])) {
        $it_primary_emails = $_POST['it_primary_emails'] ?? '';
        $it_assignment_time = $_POST['it_assignment_time'] ?? '';
        $it_subject = $_POST['it_subject'] ?? '';
        $it_template = $_POST['it_template'] ?? '';

        $stmt = mysqli_prepare($conn, "INSERT INTO `onboarding_email_config` (`config_type`, `email_addresses`, `cron_time`, `email_subject`, `email_template`) VALUES ('it_support', ?, ?, ?, ?) ON DUPLICATE KEY UPDATE `email_addresses` = ?, `cron_time` = ?, `email_subject` = ?, `email_template` = ?");
        mysqli_stmt_bind_param($stmt, 'ssssssss', $it_primary_emails, $it_assignment_time, $it_subject, $it_template, $it_primary_emails, $it_assignment_time, $it_subject, $it_template);
        mysqli_stmt_execute($stmt);
        mysqli_stmt_close($stmt);

        $success_message = "IT Support email configuration updated successfully!";
    }

    // HR Admin Configuration
    if (isset($_POST['update_hr'])) {
        $hr_emails = $_POST['hr_emails'] ?? '';
        $hr_subject = $_POST['hr_subject'] ?? '';
        $hr_template = $_POST['hr_template'] ?? '';

        $stmt = mysqli_prepare($conn, "INSERT INTO `onboarding_email_config` (`config_type`, `email_addresses`, `email_subject`, `email_template`) VALUES ('hr_admin', ?, ?, ?) ON DUPLICATE KEY UPDATE `email_addresses` = ?, `email_subject` = ?, `email_template` = ?");
        mysqli_stmt_bind_param($stmt, 'ssssss', $hr_emails, $hr_subject, $hr_template, $hr_emails, $hr_subject, $hr_template);
        mysqli_stmt_execute($stmt);
        mysqli_stmt_close($stmt);

        $success_message = "HR Admin email configuration updated successfully!";
    }

    // Boss/Management Configuration
    if (isset($_POST['update_boss'])) {
        $boss_emails = $_POST['boss_emails'] ?? '';

        $stmt = mysqli_prepare($conn, "INSERT INTO `onboarding_email_config` (`config_type`, `email_addresses`) VALUES ('boss_cc', ?) ON DUPLICATE KEY UPDATE `email_addresses` = ?");
        mysqli_stmt_bind_param($stmt, 'ss', $boss_emails, $boss_emails);
        mysqli_stmt_execute($stmt);
        mysqli_stmt_close($stmt);

        $success_message = "Boss/Management CC configuration updated successfully!";
    }




}

// Get current configurations
$configs = [];
$result = mysqli_query($conn, "SELECT * FROM `onboarding_email_config`");
while ($row = mysqli_fetch_assoc($result)) {
    $configs[$row['config_type']] = $row;
}

// Default templates
$default_cto_template = "Dear CTO Team,

A new employee requires company email setup:

Employee Information:
- Name: {employee_name}
- Employee ID: {employee_id}
- Department: {department}
- Designation: {designation}
- Onboarding Date: {onboarding_date}
- Personal Email: {personal_email}

Please use the link below to set up the company email and password:
{setup_link}

Best regards,
HR Team";

$default_it_template = "<!DOCTYPE html>
<html>
<head>
    <meta charset='UTF-8'>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 20px; background-color: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); overflow: hidden; }
        .header { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 30px 20px; text-align: center; }
        .header h1 { margin: 0; font-size: 24px; font-weight: 300; }
        .content { padding: 30px; }
        .employee-info { background: #f8f9fa; border-radius: 6px; padding: 20px; margin: 20px 0; }
        .info-row { display: flex; margin-bottom: 12px; }
        .info-label { font-weight: 600; color: #495057; min-width: 140px; }
        .info-value { color: #6c757d; }
        .action-button { display: inline-block; background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin: 20px 0; font-weight: 500; }
        .priority-notice { background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin: 20px 0; color: #856404; }
        .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #6c757d; font-size: 14px; }
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <h1>IT Setup Request</h1>
            <p style='margin: 10px 0 0 0; opacity: 0.9;'>New Employee Laptop Assignment</p>
        </div>
        <div class='content'>
            <p>Dear IT Support Team,</p>
            <p>A new employee requires laptop setup:</p>

            <div class='employee-info'>
                <div class='info-row'>
                    <span class='info-label'>Name:</span>
                    <span class='info-value'>{employee_name}</span>
                </div>
                <div class='info-row'>
                    <span class='info-label'>Employee ID:</span>
                    <span class='info-value'>{employee_id}</span>
                </div>
                <div class='info-row'>
                    <span class='info-label'>Department:</span>
                    <span class='info-value'>{department}</span>
                </div>
                <div class='info-row'>
                    <span class='info-label'>Designation:</span>
                    <span class='info-value'>{designation}</span>
                </div>
                <div class='info-row'>
                    <span class='info-label'>Onboarding Date:</span>
                    <span class='info-value'>{onboarding_date}</span>
                </div>
                <div class='info-row'>
                    <span class='info-label'>Reporting Manager:</span>
                    <span class='info-value'>{reporting_manager}</span>
                </div>
                <div class='info-row'>
                    <span class='info-label'>Office Location:</span>
                    <span class='info-value'>{office_name}</span>
                </div>
                <div class='info-row'>
                    <span class='info-label'>Company Email:</span>
                    <span class='info-value'>{company_email}</span>
                </div>
                <div class='info-row'>
                    <span class='info-label'>Company Password:</span>
                    <span class='info-value'>{company_password}</span>
                </div>
            </div>

            <div style='text-align: center;'>
                <a href='{completion_link}' class='action-button'>Complete IT Setup</a>
            </div>

            <div class='priority-notice'>
                <strong>Priority:</strong> Primary contacts should handle this request. Backup contacts are CC'd for awareness.
            </div>
        </div>
        <div class='footer'>
            <p><strong>Best regards,</strong><br>HR Team</p>
        </div>
    </div>
</body>
</html>";

$default_hr_template = "Dear HR Team,

A new employee onboarding checklist requires completion:

Employee Information:
- Name: {employee_name}
- Employee ID: {employee_id}
- Department: {department}
- Designation: {designation}
- Onboarding Date: {onboarding_date}
- Personal Email: {personal_email}
- Phone Number: {phone_number}

Please complete the HR checklist items for this new joiner:
{workflow_link}

Best regards,
System Administrator";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Onboarding Email Configuration</title>
    <link rel="shortcut icon" type="image/x-icon" href="assets/img/favicon.jpg">
    <link rel="stylesheet" href="assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="assets/css/animate.css">
    <link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">
    <link rel="stylesheet" href="assets/css/dataTables.bootstrap4.min.css">
    <link rel="stylesheet" href="assets/plugins/fontawesome/css/fontawesome.min.css">
    <link rel="stylesheet" href="assets/plugins/fontawesome/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">

    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #64748b;
            --success-color: #059669;
            --warning-color: #d97706;
            --danger-color: #dc2626;
            --light-bg: #f8fafc;
            --border-color: #e2e8f0;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
        }

        html { scroll-behavior: smooth; }

        body {
            background-color: var(--light-bg);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }

        .page-wrapper {
            background: transparent;
        }

        .content {
            padding: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }

        /* Modern Page Header */
        .page-header {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-color);
        }

        .page-title h4 {
            color: var(--text-primary);
            font-weight: 700;
            font-size: 1.875rem;
            margin-bottom: 0.5rem;
        }

        .page-title h6 {
            color: var(--text-secondary);
            font-weight: 400;
            font-size: 1rem;
            margin: 0;
        }

        /* Modern Cards */
        .card {
            background: white;
            border-radius: 16px;
            border: 1px solid var(--border-color);
            box-shadow: var(--shadow-sm);
            margin-bottom: 2rem;
            overflow: hidden;
        }

        .card-body {
            padding: 2rem;
        }

        /* Quick Navigation */
        .quick-nav {
            background: linear-gradient(135deg, var(--primary-color), #3b82f6);
            color: white;
            padding: 1rem 2rem;
            margin-bottom: 2rem;
            border-radius: 16px;
            box-shadow: var(--shadow-md);
        }

        .quick-nav .btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            transition: all 0.2s ease;
            text-decoration: none;
            font-weight: 500;
        }

        .quick-nav .btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-1px);
        }

        /* Section Headers */
        .section-header {
            background: var(--light-bg);
            padding: 1.5rem;
            margin: -2rem -2rem 2rem -2rem;
            border-bottom: 1px solid var(--border-color);
            position: relative;
        }

        .section-header::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            border-radius: 0 4px 4px 0;
        }

        .section-header.cto::before { background: var(--primary-color); }
        .section-header.it::before { background: var(--success-color); }
        .section-header.hr::before { background: var(--warning-color); }
        .section-header.boss::before { background: var(--secondary-color); }

        .section-header h5 {
            color: var(--text-primary);
            font-weight: 600;
            font-size: 1.25rem;
            margin: 0;
        }

        /* Form Styling */
        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            color: var(--text-primary);
            font-weight: 600;
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
            display: block;
        }

        .form-control {
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 0.75rem 1rem;
            font-size: 0.875rem;
            transition: all 0.2s ease;
            background: white;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
            outline: none;
        }

        .form-text {
            color: var(--text-secondary);
            font-size: 0.75rem;
            margin-top: 0.25rem;
        }

        /* Template Variables */
        .template-variables {
            background: var(--light-bg);
            padding: 1.5rem;
            border-radius: 12px;
            border: 1px solid var(--border-color);
        }

        .variable-tag {
            background: white;
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            padding: 0.375rem 0.75rem;
            border-radius: 6px;
            font-family: 'JetBrains Mono', 'Fira Code', monospace;
            font-size: 0.75rem;
            margin: 0.25rem;
            display: inline-block;
            cursor: pointer;
            transition: all 0.2s ease;
            font-weight: 500;
        }

        .variable-tag:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-1px);
            box-shadow: var(--shadow-sm);
        }

        /* Buttons */
        .btn-submit {
            background: var(--primary-color);
            border: none;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 600;
            font-size: 0.875rem;
            transition: all 0.2s ease;
            width: 100%;
        }

        .btn-submit:hover {
            background: #1d4ed8;
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .btn-added {
            background: white;
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 500;
            text-decoration: none;
            transition: all 0.2s ease;
        }

        .btn-added:hover {
            background: var(--light-bg);
            transform: translateY(-1px);
            box-shadow: var(--shadow-sm);
        }

        /* Grid Layout */
        .config-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        /* Preview Button Positioning */
        .position-relative .btn {
            box-shadow: var(--shadow-sm);
        }

        .position-relative .btn:hover {
            box-shadow: var(--shadow-md);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .content { padding: 1rem; }
            .page-header { padding: 1.5rem; }
            .card-body { padding: 1.5rem; }
            .config-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }
        }

        /* Alert Styling */
        .alert {
            border-radius: 12px;
            border: none;
            padding: 1rem 1.5rem;
            margin-bottom: 2rem;
        }

        .alert-success {
            background: rgba(5, 150, 105, 0.1);
            color: var(--success-color);
            border-left: 4px solid var(--success-color);
        }

        /* Modal Close Button Fix */
        .btn-close {
            background: transparent;
            border: none;
            font-size: 1.5rem;
            font-weight: 700;
            line-height: 1;
            color: #000;
            text-shadow: 0 1px 0 #fff;
            opacity: 0.5;
            padding: 0.25rem;
            width: 1.5rem;
            height: 1.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .btn-close:hover {
            opacity: 0.75;
        }

        .btn-close span {
            font-size: 1.5rem;
            line-height: 1;
        }
    </style>
</head>
<body>

<div class="main-wrapper">
    <?php include("header.php"); ?>

    <div class="page-wrapper">
        <div class="content">
            <div class="page-header">
                <div class="page-title">
                    <h4><i class="fas fa-envelope-open-text me-2"></i>Email Configuration</h4>
                    <h6>Configure email notifications for onboarding workflow</h6>
                </div>
                <div class="page-btn">
                    <a href="onboarding_list.php" class="btn btn-added">
                        <img src="assets/img/icons/back.svg" alt="img" class="me-1">Back to Onboarding
                    </a>
                </div>
            </div>


            <?php if (isset($success_message)): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?php echo $success_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <?php endif; ?>



            <!-- Boss/Management CC Configuration -->
            <div class="card">
                <div class="card-body">
                    <div id="section-boss" class="section-header boss">
                        <h5><i class="fas fa-user-tie me-2"></i>Boss/Management CC Configuration</h5>
                    </div>
                    <form method="POST">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="form-group">
                                    <label>Boss/Management Email Addresses</label>
                                    <textarea class="form-control" name="boss_emails" rows="5" placeholder="Enter boss/management emails to CC on IT notifications, one per line&#10;Example:&#10;<EMAIL>&#10;<EMAIL>&#10;<EMAIL>"><?php echo htmlspecialchars($configs['boss_cc']['email_addresses'] ?? "<EMAIL>\<EMAIL>"); ?></textarea>
                                    <small class="form-text text-muted">Enter multiple email addresses, one per line. These emails will be CC'd on IT support notifications for management awareness.</small>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <button type="submit" name="update_boss" class="btn btn-submit">
                                    <i class="fas fa-save"></i> Update Boss/Management Configuration
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>



            <!-- Email Configuration Grid Layout -->
            <div class="config-grid">
                <!-- CTO Request Email Configuration -->
                <div class="card">
                    <div class="card-body">
                        <div id="section-cto" class="section-header cto">
                            <h5><i class="fas fa-envelope me-2"></i>CTO Request</h5>
                        </div>
                            <form method="POST">
                                <div class="form-group">
                                    <label>Email Addresses *</label>
                                    <textarea class="form-control" name="cto_emails" rows="3" placeholder="Enter email addresses, one per line" required><?php echo htmlspecialchars($configs['cto_request']['email_addresses'] ?? '<EMAIL>'); ?></textarea>
                                    <small class="form-text text-muted">One email per line</small>
                                </div>

                                <div class="form-group">
                                    <label>Email Subject *</label>
                                    <input type="text" class="form-control" name="cto_subject" value="<?php echo htmlspecialchars($configs['cto_request']['email_subject'] ?? 'New Employee Email Setup Required - New Joiner: {employee_name}'); ?>" required>
                                    <small class="form-text text-muted">Note: {employee_name} will be automatically replaced with the actual employee name</small>
                                </div>

                                <div class="form-group">
                                    <label>Email Template *</label>
                                    <div class="position-relative">
                                        <textarea class="form-control" name="cto_template" rows="8" required><?php echo htmlspecialchars($configs['cto_request']['email_template'] ?? $default_cto_template); ?></textarea>
                                        <button type="button" class="btn btn-outline-primary btn-sm position-absolute" style="top: 8px; right: 8px; z-index: 10;" onclick="previewTemplate('cto_template', 'CTO Request Email Template')" title="Preview Email">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <button type="submit" name="update_cto" class="btn btn-submit btn-sm w-100">
                                        <i class="fas fa-save"></i> Update CTO Config
                                    </button>
                                </div>
                        </form>
                    </div>
                </div>

                <!-- IT Support Email Configuration -->
                <div class="card">
                    <div class="card-body">
                        <div id="section-it" class="section-header it">
                            <h5><i class="fas fa-headset me-2"></i>IT Support</h5>
                        </div>
                            <form method="POST">
                                <div class="form-group">
                                    <label>IT Support Email *</label>
                                    <textarea class="form-control" name="it_primary_emails" rows="4" placeholder="Enter IT support emails, one per line" required><?php echo htmlspecialchars($configs['it_support']['email_addresses'] ?? '<EMAIL>'); ?></textarea>
                                    <small class="form-text text-muted">IT support team members</small>
                                </div>

                                <div class="form-group">
                                    <label>Assignment Time (Kuala Lumpur) *</label>
                                    <input type="time" class="form-control" name="it_assignment_time" value="<?php echo htmlspecialchars($configs['it_support']['cron_time'] ?? '09:00'); ?>" required>
                                    <small class="form-text text-muted">Time for laptop setup assignments (Malaysia timezone)</small>
                                </div>

                                <div class="form-group">
                                    <label>Email Subject *</label>
                                    <input type="text" class="form-control" name="it_subject" value="<?php echo htmlspecialchars($configs['it_support']['email_subject'] ?? 'Laptop Setup Required - New Joiner: {employee_name}'); ?>" required>
                                    <small class="form-text text-muted">Note: {employee_name} will be automatically replaced with the actual employee name</small>
                                </div>

                                <div class="form-group">
                                    <label>Email Template *</label>
                                    <div class="position-relative">
                                        <textarea class="form-control" name="it_template" rows="6" required><?php echo htmlspecialchars($configs['it_support']['email_template'] ?? $default_it_template); ?></textarea>
                                        <button type="button" class="btn btn-outline-primary btn-sm position-absolute" style="top: 8px; right: 8px; z-index: 10;" onclick="previewTemplate('it_template', 'IT Support Email Template')" title="Preview Email">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <button type="submit" name="update_it" class="btn btn-submit btn-sm w-100">
                                        <i class="fas fa-save"></i> Update IT Config
                                    </button>
                                </div>
                        </form>
                    </div>
                </div>

                <!-- HR Admin Email Configuration -->
                <div class="card">
                    <div class="card-body">
                        <div id="section-hr" class="section-header hr">
                            <h5><i class="fas fa-clipboard-check me-2"></i>HR Admin</h5>
                        </div>
                            <form method="POST">
                                <div class="form-group">
                                    <label>HR Email Addresses *</label>
                                    <textarea class="form-control" name="hr_emails" rows="3" placeholder="Enter HR emails, one per line" required><?php echo htmlspecialchars($configs['hr_admin']['email_addresses'] ?? '<EMAIL>'); ?></textarea>
                                    <small class="form-text text-muted">HR team members</small>
                                </div>

                                <div class="form-group">
                                    <label>Email Subject *</label>
                                    <input type="text" class="form-control" name="hr_subject" value="<?php echo htmlspecialchars($configs['hr_admin']['email_subject'] ?? 'HR Checklist Required - New Joiner: {employee_name}'); ?>" required>
                                    <small class="form-text text-muted">Note: {employee_name} will be automatically replaced with the actual employee name</small>
                                </div>

                                <div class="form-group">
                                    <label>Email Template *</label>
                                    <div class="position-relative">
                                        <textarea class="form-control" name="hr_template" rows="8" required><?php echo htmlspecialchars($configs['hr_admin']['email_template'] ?? $default_hr_template); ?></textarea>
                                        <button type="button" class="btn btn-outline-primary btn-sm position-absolute" style="top: 8px; right: 8px; z-index: 10;" onclick="previewTemplate('hr_template', 'HR Admin Email Template')" title="Preview Email">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <button type="submit" name="update_hr" class="btn btn-submit btn-sm w-100">
                                        <i class="fas fa-save"></i> Update HR Config
                                    </button>
                                </div>
                        </form>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>

<!-- Email Template Preview Modal -->
<div class="modal fade" id="templatePreviewModal" tabindex="-1" aria-labelledby="templatePreviewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="templatePreviewModalLabel">
                    <i class="fas fa-eye me-2"></i>Email Template Preview
                </h5>
                <button type="button" class="btn-close" onclick="closeModal()" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <!-- Left side - Edit Email Template -->
                    <div class="col-lg-9 col-md-8">
                        <h6 class="mb-3"><i class="fas fa-edit me-2"></i>Edit Email Template</h6>
                        <div class="form-group mb-3">
                            <label class="form-label">Email Subject</label>
                            <input type="text" class="form-control" id="preview_email_subject" oninput="updatePreview()" onchange="updatePreview()" style="font-size: 14px;">
                        </div>
                        <div class="form-group mb-3">
                            <label class="form-label">Email Template</label>
                            <textarea class="form-control" id="preview_email_template" rows="20" oninput="updatePreview()" onchange="updatePreview()" style="font-family: 'Courier New', monospace; font-size: 14px; line-height: 1.5; min-height: 500px;"></textarea>
                            <div class="mt-2 p-3" style="background: #f8f9fa; border-radius: 8px; border: 1px solid #e2e8f0;">
                                <small class="text-muted">
                                    <strong>Available variables:</strong><br>
                                    <span class="d-inline-block me-2 mb-1"><code>{employee_name}</code></span>
                                    <span class="d-inline-block me-2 mb-1"><code>{employee_id}</code></span>
                                    <span class="d-inline-block me-2 mb-1"><code>{department}</code></span>
                                    <span class="d-inline-block me-2 mb-1"><code>{designation}</code></span>
                                    <span class="d-inline-block me-2 mb-1"><code>{onboarding_date}</code></span>
                                    <span class="d-inline-block me-2 mb-1"><code>{personal_email}</code></span>
                                    <span class="d-inline-block me-2 mb-1"><code>{phone_number}</code></span>
                                    <span class="d-inline-block me-2 mb-1"><code>{reporting_manager}</code></span>
                                    <span class="d-inline-block me-2 mb-1"><code>{office_name}</code></span>
                                    <span class="d-inline-block me-2 mb-1"><code>{setup_link}</code></span>
                                    <span class="d-inline-block me-2 mb-1"><code>{workflow_link}</code></span>
                                </small>
                            </div>
                        </div>
                    </div>

                    <!-- Right side - Live Preview -->
                    <div class="col-lg-3 col-md-4">
                        <h6 class="mb-3"><i class="fas fa-envelope me-2"></i>Live Preview</h6>
                        <div class="email-preview">
                            <div class="email-header mb-3 p-2" style="background: #e3f2fd; border-radius: 8px; border-left: 4px solid #2196f3; font-size: 12px;">
                                <strong>Subject:</strong><br><span id="previewSubject" style="word-break: break-word;"></span>
                            </div>
                            <div class="email-body p-2" id="previewBody" style="background: #f8f9fa; border-radius: 8px; border: 1px solid #e2e8f0; white-space: pre-wrap; font-family: Arial, sans-serif; line-height: 1.4; min-height: 400px; max-height: 500px; overflow-y: auto; font-size: 11px;">
                            </div>
                        </div>
                        <div class="alert alert-info mt-2 p-2">
                            <small><i class="fas fa-info-circle me-1"></i>Preview shows template with sample data. Variables will be replaced with actual employee data when emails are sent.</small>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" onclick="saveTemplateChanges()">
                    <i class="fas fa-save me-2"></i>Save Changes
                </button>
                <button type="button" class="btn btn-secondary" onclick="closeModal()">Cancel</button>
            </div>
        </div>
    </div>
</div>

<!-- Scripts -->
<script src="assets/js/jquery-3.6.0.min.js"></script>
<script src="assets/js/feather.min.js"></script>
<script src="assets/js/jquery.slimscroll.min.js"></script>
<script src="assets/js/jquery.dataTables.min.js"></script>
<script src="assets/js/dataTables.bootstrap4.min.js"></script>
<script src="assets/js/bootstrap.bundle.min.js"></script>
<script src="assets/plugins/select2/js/select2.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalert2.all.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalerts.min.js"></script>
<script>
function copyTag(el){
  const text = el.textContent;
  navigator.clipboard.writeText(text);
  el.classList.add('bg-success','text-white');
  setTimeout(()=>{ el.classList.remove('bg-success','text-white'); },600);
}

let currentTemplateTextarea = null;
let currentSubjectInput = null;

function previewTemplate(textareaId, title) {
    // Get the textarea and subject input
    currentTemplateTextarea = document.getElementById(textareaId) || document.querySelector(`textarea[name="${textareaId}"]`);

    // Get the corresponding subject input
    if (textareaId.includes('cto')) {
        currentSubjectInput = document.querySelector('input[name="cto_subject"]');
    } else if (textareaId.includes('it')) {
        currentSubjectInput = document.querySelector('input[name="it_subject"]');
    } else if (textareaId.includes('hr')) {
        currentSubjectInput = document.querySelector('input[name="hr_subject"]');
    }

    // Update modal title
    document.getElementById('templatePreviewModalLabel').innerHTML = '<i class="fas fa-edit me-2"></i>' + title;

    // Load current template and subject into modal
    document.getElementById('preview_email_template').value = currentTemplateTextarea.value;
    document.getElementById('preview_email_subject').value = currentSubjectInput ? currentSubjectInput.value : '';

    // Update preview
    updatePreview();

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('templatePreviewModal'));
    modal.show();
}

function updatePreview() {
    // Get template and subject from modal inputs
    const templateElement = document.getElementById('preview_email_template');
    const subjectElement = document.getElementById('preview_email_subject');

    if (!templateElement || !subjectElement) {
        console.log('Template or subject element not found');
        return;
    }

    const template = templateElement.value;
    const subject = subjectElement.value;

    // Sample data for preview
    const sampleData = {
        '{employee_name}': 'John Doe',
        '{employee_id}': 'EMP001',
        '{department}': 'Information Technology',
        '{designation}': 'Software Developer',
        '{onboarding_date}': '2024-01-15',
        '{personal_email}': '<EMAIL>',
        '{phone_number}': '+60123456789',
        '{reporting_manager}': 'Jane Smith',
        '{office_name}': 'Kuala Lumpur Office',
        '{setup_link}': 'https://company.com/setup/abc123',
        '{workflow_link}': 'https://company.com/workflow/abc123'
    };

    // Replace template variables with sample data
    let previewContent = template;
    let previewSubject = subject;

    for (const [variable, value] of Object.entries(sampleData)) {
        const regex = new RegExp(variable.replace(/[{}]/g, '\\$&'), 'g');
        previewContent = previewContent.replace(regex, value);
        previewSubject = previewSubject.replace(regex, value);
    }

    // Update preview content
    const previewSubjectElement = document.getElementById('previewSubject');
    const previewBodyElement = document.getElementById('previewBody');

    if (previewSubjectElement) {
        previewSubjectElement.textContent = previewSubject;
    }
    if (previewBodyElement) {
        previewBodyElement.textContent = previewContent;
    }
}

function saveTemplateChanges() {
    // Get the edited template and subject from modal
    const editedTemplate = document.getElementById('preview_email_template').value;
    const editedSubject = document.getElementById('preview_email_subject').value;

    // Update the original form fields
    if (currentTemplateTextarea) {
        currentTemplateTextarea.value = editedTemplate;
    }
    if (currentSubjectInput) {
        currentSubjectInput.value = editedSubject;
    }

    // Close modal
    closeModal();

    // Show success message
    const toast = document.createElement('div');
    toast.className = 'toast-notification';
    toast.innerHTML = '<i class="fas fa-check-circle me-2"></i>Template updated successfully!';
    toast.style.cssText = 'position: fixed; top: 20px; right: 20px; background: #059669; color: white; padding: 12px 20px; border-radius: 8px; z-index: 9999; box-shadow: 0 4px 12px rgba(0,0,0,0.15);';
    document.body.appendChild(toast);

    setTimeout(() => {
        toast.remove();
    }, 3000);
}

function closeModal() {
    const modal = bootstrap.Modal.getInstance(document.getElementById('templatePreviewModal'));
    if (modal) {
        modal.hide();
    } else {
        // Fallback if modal instance not found
        document.getElementById('templatePreviewModal').style.display = 'none';
        document.body.classList.remove('modal-open');
        const backdrop = document.querySelector('.modal-backdrop');
        if (backdrop) {
            backdrop.remove();
        }
    }
}
</script>
<script src="assets/js/script.js"></script>

</body>
</html>
