<?php
require("database.php");

if (isset($_SESSION["user"])) {
    $email = $_SESSION["user"];
    $acc_id = $_SESSION["acc_id"];

    // Fetch user details
    $sql4 = "SELECT * FROM `user-list` WHERE `acc_id` = ?";
    $stmt = mysqli_prepare($conn, $sql4);
    mysqli_stmt_bind_param($stmt, "s", $acc_id);
    mysqli_stmt_execute($stmt);
    $result4 = mysqli_stmt_get_result($stmt);

    if ($row4 = mysqli_fetch_assoc($result4)) {
        $fullname = $row4['name'];
        $email2 = $row4['email'];

        // Use email if full name is empty
        if (is_null($fullname) || trim($fullname) === "") {
            $fullname = $email2;
        }
    } else {
        echo "No record found!";
    }
} else {
    header("location: ./signin.php");
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0">
    <meta name="description" content="CLL Warehouse Inventory System">
    <meta name="author" content="Powered by CLL Systems Sdn Bhd">
    <meta name="robots" content="noindex, nofollow">
    <title>CLLXWARE - Onboarding List</title>

    <link rel='icon' type='image/x-icon' href='https://www.cllsystems.com/wp-content/themes/cllsystems/img/favicon.png'>
    <link rel="stylesheet" href="assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="assets/css/animate.css">
    <link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">
    <link rel="stylesheet" href="assets/css/dataTables.bootstrap4.min.css">
    <link rel="stylesheet" href="assets/plugins/fontawesome/css/fontawesome.min.css">
    <link rel="stylesheet" href="assets/plugins/fontawesome/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
<div id="global-loader">
    <div class="whirly-loader"> </div>
</div>

<div class="main-wrapper">
    <?php
    $role_id = $_SESSION["role_id"];
    $sql20 = "SELECT * FROM `role-list` WHERE `role-id` = ?";
    $stmt20 = mysqli_prepare($conn, $sql20);
    mysqli_stmt_bind_param($stmt20, "s", $role_id);
    mysqli_stmt_execute($stmt20);
    $result20 = mysqli_stmt_get_result($stmt20);

    if ($row20 = mysqli_fetch_assoc($result20)) {
        $all = $row20['onboarding_all'];
        $view = $row20['onboarding_view'];
        if($view != '1' and $all !='1'){
            header("location: ./index.php");
            exit;
        }
    } else {
        echo "<script>alert('Role data not found');</script>";
        exit;
    }

    mysqli_stmt_close($stmt20);
    include("header.php");
    ?>

    <div class="page-wrapper">
        <div class="content">
            <div class="page-header">
                <div class="page-title">
                    <h4>Onboarding List</h4>
                    <h6>Manage your onboarding records</h6>
                </div>
                <div class="page-btn">
                    <?php
                    $role_id = $_SESSION["role_id"];
                    $sql20 = "SELECT * FROM `role-list` WHERE `role-id` = ?";
                    $stmt20 = mysqli_prepare($conn, $sql20);
                    mysqli_stmt_bind_param($stmt20, "s", $role_id);
                    mysqli_stmt_execute($stmt20);
                    $result20 = mysqli_stmt_get_result($stmt20);

                    if ($row20 = mysqli_fetch_assoc($result20)) {
                        $create = $row20['onboarding_create'];
                        $all = $row20['onboarding_all'];
                        if($create == '1' or $all =='1'){
                            echo '<a href="add_onboarding.php" class="btn btn-added"><img src="assets/img/icons/plus.svg" alt="img" class="me-1">Add Onboarding</a>';
                        }
                    }
                    mysqli_stmt_close($stmt20);
                    ?>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <div class="table-top">
                        <div class="search-set">
                            <div class="search-path">
                                <a class="btn btn-filter" id="filter_search">
                                    <img src="assets/img/icons/filter.svg" alt="img">
                                    <span><img src="assets/img/icons/closes.svg" alt="img"></span>
                                </a>
                            </div>
                            <div class="search-input">
                                <a class="btn btn-searchset"><img src="assets/img/icons/search-white.svg" alt="img"></a>
                            </div>
                        </div>
                        <div class="wordset">
                            <ul>
                                <li>
                                    <a data-bs-toggle="tooltip" data-bs-placement="top" title="pdf"><img src="assets/img/icons/pdf.svg" alt="img"></a>
                                </li>
                                <li>
                                    <a data-bs-toggle="tooltip" data-bs-placement="top" title="excel"><img src="assets/img/icons/excel.svg" alt="img"></a>
                                </li>
                                <li>
                                    <a data-bs-toggle="tooltip" data-bs-placement="top" title="print"><img src="assets/img/icons/printer.svg" alt="img"></a>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <div class="card" id="filter_inputs" style="display: none;">
                        <div class="card-body pb-0">
                            <div class="row">
                                <div class="col-lg-2 col-sm-6 col-12">
                                    <div class="form-group">
                                        <select class="select" id="filter_department">
                                            <option value="">Choose Department</option>
                                            <?php
                                            $dept_sql = "SELECT department_name FROM `department_list` ORDER BY `department_name`";
                                            $dept_res = mysqli_query($conn,$dept_sql);
                                            if($dept_res && mysqli_num_rows($dept_res)>0){
                                                while($d = mysqli_fetch_assoc($dept_res)){
                                                    echo "<option value='".htmlspecialchars($d['department_name'])."'>".htmlspecialchars($d['department_name'])."</option>";
                                                }
                                            }
                                            ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-lg-2 col-sm-6 col-12">
                                    <div class="form-group">
                                        <select class="select" id="filter_office">
                                            <option value="">Choose Office</option>
                                            <?php
                                            $office_sql = "SELECT office_name FROM `office_list` ORDER BY `office_name`";
                                            $office_res = mysqli_query($conn,$office_sql);
                                            if($office_res && mysqli_num_rows($office_res)>0){
                                                while($o = mysqli_fetch_assoc($office_res)){
                                                    echo "<option value='".htmlspecialchars($o['office_name'])."'>".htmlspecialchars($o['office_name'])."</option>";
                                                }
                                            }
                                            ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-lg-1 col-sm-6 col-12 ms-auto">
                                    <div class="form-group">
                                        <a class="btn btn-filters ms-auto"><img src="assets/img/icons/search-whites.svg" alt="img"></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table datanew">
                            <thead>
                                <tr>
                                    <th>No</th>
                                    <th>Full Name</th>
                                    <th>Employee ID</th>
                                    <th>Department</th>
                                    <th>Job Title</th>
                                    <th>Personal Email</th>
                                    <th>Phone Number</th>
                                    <th>Onboarding Date</th>
                                    <th>Office Name</th>
                                    <?php
                                    // Single permission check for both header and body
                                    $role_id = $_SESSION["role_id"];
                                    $sql_perm = "SELECT * FROM `role-list` WHERE `role-id` = ?";
                                    $stmt_perm = mysqli_prepare($conn, $sql_perm);
                                    mysqli_stmt_bind_param($stmt_perm, "s", $role_id);
                                    mysqli_stmt_execute($stmt_perm);
                                    $result_perm = mysqli_stmt_get_result($stmt_perm);

                                    $show_actions = false;
                                    if ($row_perm = mysqli_fetch_assoc($result_perm)) {
                                        $edit = $row_perm['onboarding_edit'];
                                        $all = $row_perm['onboarding_all'];
                                        $show_actions = ($edit == '1' or $all == '1');
                                    }
                                    mysqli_stmt_close($stmt_perm);

                                    // Show Action column in header if user has permissions
                                    if ($show_actions) {
                                        echo "<th>Action</th>";
                                    }
                                    ?>
                                </tr>
                            </thead>
                            <tbody>
                            <?php

                            $sql = "SELECT ol.*,
                                           dl.department_name,
                                           ml.manager_name,
                                           ofl.office_name as office_name_actual
                                    FROM `onboarding_list` ol
                                    LEFT JOIN `department_list` dl ON ol.department_id = dl.department_id
                                    LEFT JOIN `manager_list` ml ON ol.manager_id = ml.manager_id
                                    LEFT JOIN `office_list` ofl ON ol.office_id = ofl.office_id
                                    ORDER BY ol.onboarding_id ASC";
                            $result = mysqli_query($conn, $sql);

                            if ($result && mysqli_num_rows($result) > 0) {
                                $counter = 1;
                                while ($row = mysqli_fetch_assoc($result)) {
                                    echo "<tr>";
                                    echo "<td>{$counter}</td>";
                                    echo "<td>" . htmlspecialchars($row['full_name'] ?? '') . "</td>";
                                    echo "<td>" . htmlspecialchars($row['employee_id'] ?? '') . "</td>";
                                    echo "<td>" . htmlspecialchars($row['department_name'] ?? 'Not specified') . "</td>";
                                    echo "<td>" . htmlspecialchars($row['job_title'] ?? '') . "</td>";
                                    echo "<td>" . htmlspecialchars($row['personal_email'] ?? '') . "</td>";
                                    echo "<td>" . htmlspecialchars($row['phone_number'] ?? '') . "</td>";
                                    echo "<td>" . htmlspecialchars($row['onboarding_date'] ?? '') . "</td>";
                                    echo "<td>" . htmlspecialchars($row['office_name_actual'] ?? 'Not specified') . "</td>";

                                    // Add Action column only if user has permissions
                                    if ($show_actions) {
                                        echo "<td>";
                                        echo "<a class='me-3' href='edit_onboarding.php?onboarding_id=" . htmlspecialchars($row['onboarding_id']) . "' data-bs-toggle='tooltip' title='Edit'>";
                                        echo "<img src='assets/img/icons/edit.svg' alt='Edit'>";
                                        echo "</a>";
                                        echo "<a class='me-3' href='onboarding_details.php?onboarding_id=" . htmlspecialchars($row['onboarding_id']) . "' data-bs-toggle='tooltip' title='View'>";
                                        echo "<img src='assets/img/icons/eye.svg' alt='View'>";
                                        echo "</a>";
                                        echo "<a class='me-3' href='onboarding_workflow.php?id=" . htmlspecialchars($row['onboarding_id']) . "' data-bs-toggle='tooltip' title='Workflow'>";
                                        echo "<i class='fas fa-tasks text-primary'></i>";
                                        echo "</a>";
                                        echo "<a href='#' onclick=\"deleteOnboarding('" . htmlspecialchars($row['onboarding_id']) . "')\" data-bs-toggle='tooltip' title='Delete'>";
                                        echo "<img src='assets/img/icons/delete.svg' alt='Delete'>";
                                        echo "</a>";
                                        echo "</td>";
                                    }

                                    echo "</tr>";
                                    $counter++;
                                }
                            } else {
                                // No rows: leave tbody empty and let DataTables show its empty message
                            }
                            ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="assets/js/jquery-3.6.0.min.js"></script>
<script src="assets/js/feather.min.js"></script>
<script src="assets/js/jquery.slimscroll.min.js"></script>
<script src="assets/js/jquery.dataTables.min.js"></script>
<script src="assets/js/dataTables.bootstrap4.min.js"></script>
<script src="assets/js/bootstrap.bundle.min.js"></script>
<script src="assets/plugins/select2/js/select2.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalert2.all.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalerts.min.js"></script>
<script src="assets/js/script.js"></script>

<script>
$(document).ready(function() {
    // Filter functionality
    $('.btn-filters').on('click', function(){
        var dept = $('#filter_department').val();
        var office = $('#filter_office').val();
        var dt = $('.datanew').DataTable();
        // Assuming Department is column index 3 and Office column index 8
        dt.column(3).search(dept || '', true, false);
        dt.column(8).search(office || '', true, false);
        dt.draw();
    });

    // Additional safety check for onboarding table
    if ($('.datanew').length > 0) {
        try {
            // Validate table structure before initializing DataTables
            var table = $('.datanew')[0];
            var headerCells = table.querySelector('thead tr').cells.length;
            var bodyRows = table.querySelectorAll('tbody tr');

            console.log('=== Table Structure Debug ===');
            console.log('Header cells count:', headerCells);
            console.log('Body rows count:', bodyRows.length);

            var isValid = true;
            bodyRows.forEach(function(row, index) {
                console.log('Row ' + index + ' cells count:', row.cells.length);
                if (row.cells.length !== headerCells) {
                    console.error('❌ Row ' + index + ' has ' + row.cells.length + ' cells, but header has ' + headerCells + ' cells');
                    console.log('Row ' + index + ' content:', row.innerHTML);
                    isValid = false;
                } else {
                    console.log('✅ Row ' + index + ' is valid');
                }
            });

            if (isValid) {
                console.log('✅ Table structure is valid. Header: ' + headerCells + ' columns');
            } else {
                console.error('❌ Table structure validation failed');
                // Don't initialize DataTables if structure is invalid
                return;
            }
        } catch (e) {
            console.error('Table validation error:', e);
        }
    }
});
</script>
<script>
function deleteOnboarding(onboardingId){
  if(confirm('Are you sure you want to delete this onboarding record?')){
    var xhr=new XMLHttpRequest();
    xhr.open('POST','delete_onboarding.php',true);
    xhr.setRequestHeader('Content-Type','application/x-www-form-urlencoded');
    xhr.onload=function(){
      if(xhr.status>=200&&xhr.status<300){
        window.location.reload();
      }else{ alert('Error deleting: '+xhr.responseText); }
    };
    xhr.onerror=function(){ alert('Network error.'); };
    xhr.send('onboarding_id='+encodeURIComponent(onboardingId));
  }
}
</script>


</body>
</html>
