<?php
require('database.php');
require_once 'MAILER/vendor/autoload.php';
use <PERSON>HPMailer\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\Exception;

// Check if user is logged in
if (!isset($_SESSION['user'])) {
    header("Location: login.php");
    exit;
}

$onboarding_id = $_GET['id'] ?? '';
if (!$onboarding_id) {
    echo "<script>alert('No onboarding ID provided.'); window.location='onboarding_list.php';</script>";
    exit;
}

// Ensure required columns exist
function ensure_column_exists($conn, $table, $column, $definition) {
    $result = mysqli_query($conn, "SHOW COLUMNS FROM `$table` LIKE '$column'");
    if (mysqli_num_rows($result) == 0) {
        mysqli_query($conn, "ALTER TABLE `$table` ADD COLUMN `$column` $definition");
    }
}

ensure_column_exists($conn, 'onboarding_list', 'company_email_password', 'varchar(255) NULL');
ensure_column_exists($conn, 'onboarding_list', 'company_email_set', 'TINYINT(1) NOT NULL DEFAULT 0');
ensure_column_exists($conn, 'onboarding_list', 'setup_token', 'varchar(255) NULL');
ensure_column_exists($conn, 'onboarding_list', 'laptop_recipients', 'TEXT NULL');
ensure_column_exists($conn, 'onboarding_list', 'laptop_email_sent', 'TINYINT(1) NOT NULL DEFAULT 0');
ensure_column_exists($conn, 'onboarding_list', 'it_primary_member', 'varchar(255) NULL');
ensure_column_exists($conn, 'onboarding_list', 'it_backup_member', 'varchar(255) NULL');
ensure_column_exists($conn, 'onboarding_list', 'it_primary_email', 'varchar(255) NULL');
ensure_column_exists($conn, 'onboarding_list', 'it_backup_email', 'varchar(255) NULL');
ensure_column_exists($conn, 'onboarding_list', 'it_override_token', 'varchar(255) NULL');
ensure_column_exists($conn, 'onboarding_list', 'it_override_email', 'varchar(255) NULL');
ensure_column_exists($conn, 'onboarding_list', 'it_remarks', 'TEXT NULL');

// Create onboarding_checklist table for tracking HR checklist completion
$create_onboarding_checklist = "CREATE TABLE IF NOT EXISTS `onboarding_checklist` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `onboarding_id` varchar(255) NOT NULL,
    `checklist_id` int(11) NOT NULL,
    `completed` TINYINT(1) NOT NULL DEFAULT 0,
    PRIMARY KEY (`id`),
    UNIQUE KEY `onboarding_checklist` (`onboarding_id`, `checklist_id`)
)";
mysqli_query($conn, $create_onboarding_checklist);

// Get onboarding record with related names
$stmt = mysqli_prepare($conn, "
    SELECT ol.*,
           dl.department_name,
           ml.manager_name,
           ofl.office_name as office_name_actual
    FROM `onboarding_list` ol
    LEFT JOIN `department_list` dl ON ol.department_id = dl.department_id
    LEFT JOIN `manager_list` ml ON ol.manager_id = ml.manager_id
    LEFT JOIN `office_list` ofl ON ol.office_id = ofl.office_id
    WHERE ol.onboarding_id = ?
");
mysqli_stmt_bind_param($stmt, 's', $onboarding_id);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);
$record = mysqli_fetch_assoc($result);
mysqli_stmt_close($stmt);

if (!$record) {
    echo "<script>alert('Onboarding record not found.'); window.location='onboarding_list.php';</script>";
    exit;
}

// Handle form submissions
if ($_POST) {
    if (isset($_POST['update_cto'])) {
        $company_email_set = isset($_POST['company_email_set']) ? 1 : 0;
        $stmt = mysqli_prepare($conn, "UPDATE `onboarding_list` SET `company_email_set` = ? WHERE `onboarding_id` = ?");
        mysqli_stmt_bind_param($stmt, 'is', $company_email_set, $onboarding_id);
        mysqli_stmt_execute($stmt);
        mysqli_stmt_close($stmt);
        header("Location: onboarding_workflow.php?id=" . urlencode($onboarding_id));
        exit;
    }
    
    // Handle IT member assignment
    if (isset($_POST['assign_it_members'])) {
        // Handle custom email inputs
        $it_primary_email = $_POST['it_primary_email'] ?? '';
        if ($it_primary_email === 'custom') {
            $it_primary_email = $_POST['it_primary_custom'] ?? '';
        }

        $it_backup_email = $_POST['it_backup_email'] ?? '';
        if ($it_backup_email === 'custom') {
            $it_backup_email = $_POST['it_backup_custom'] ?? '';
        }

        $it_remarks = $_POST['it_remarks'] ?? '';

        $stmt = mysqli_prepare($conn, "UPDATE `onboarding_list` SET `it_primary_email` = ?, `it_backup_email` = ?, `it_remarks` = ? WHERE `onboarding_id` = ?");
        mysqli_stmt_bind_param($stmt, 'ssss', $it_primary_email, $it_backup_email, $it_remarks, $onboarding_id);
        mysqli_stmt_execute($stmt);
        mysqli_stmt_close($stmt);

        // Note: IT assignment emails will be sent automatically by the cron job (it_reminder_cron.php)
        // on the onboarding date at the configured IT support assignment time

        header("Location: onboarding_workflow.php?id=" . urlencode($onboarding_id) . "&success=it_assigned");
        exit;
    }

    // Handle admin override
    if (isset($_POST['admin_override'])) {
        $override_email = $_POST['override_email'] ?? '';

        if (!empty($override_email)) {
            // Generate new token and expire old ones
            $new_token = hash('sha256', $onboarding_id . 'IT_OVERRIDE_' . time());

            // Update the record with new token
            $stmt = mysqli_prepare($conn, "UPDATE `onboarding_list` SET `it_override_token` = ?, `it_override_email` = ? WHERE `onboarding_id` = ?");
            mysqli_stmt_bind_param($stmt, 'sss', $new_token, $override_email, $onboarding_id);
            mysqli_stmt_execute($stmt);
            mysqli_stmt_close($stmt);

            // Send override assignment email
            send_override_assignment_email($onboarding_id, $override_email, $new_token, $conn);

            header("Location: onboarding_workflow.php?id=" . urlencode($onboarding_id) . "&success=override_sent");
            exit;
        }
    }

    if (isset($_POST['update_it'])) {
        $laptop_email_sent = isset($_POST['laptop_email_sent']) ? 1 : 0;
        $it_remarks = $_POST['it_remarks'] ?? '';

        // Check if laptop setup was just completed
        $was_completed_before = !empty($record['laptop_email_sent']);
        $is_completed_now = $laptop_email_sent == 1;

        $stmt = mysqli_prepare($conn, "UPDATE `onboarding_list` SET `laptop_email_sent` = ? WHERE `onboarding_id` = ?");
        mysqli_stmt_bind_param($stmt, 'is', $laptop_email_sent, $onboarding_id);
        mysqli_stmt_execute($stmt);
        mysqli_stmt_close($stmt);

        // Step 4: If laptop setup was just completed, notify HR
        if (!$was_completed_before && $is_completed_now) {
            send_hr_laptop_completion_notification($onboarding_id, $conn);
        }

        header("Location: onboarding_workflow.php?id=" . urlencode($onboarding_id));
        exit;
    }

    // Handle resend IT assignment emails
    if (isset($_POST['resend_it_emails'])) {
        send_it_assignment_reminder_emails($onboarding_id, $conn);
        header("Location: onboarding_workflow.php?id=" . urlencode($onboarding_id) . "&success=emails_resent");
        exit;
    }

    if (isset($_POST['update_hr'])) {
        $current_user = $_SESSION['user'] ?? 'System';

        // Get all checklist items from offboarding_checklist table
        $checklist_result = mysqli_query($conn, "SELECT id FROM `offboarding_checklist`");
        while ($checklist_row = mysqli_fetch_assoc($checklist_result)) {
            $checklist_id = $checklist_row['id'];
            $is_checked = isset($_POST['checklist_' . $checklist_id]) ? 1 : 0;

            if ($is_checked) {
                // Insert or update as completed
                $stmt = mysqli_prepare($conn, "INSERT INTO `onboarding_checklist` (`onboarding_id`, `checklist_id`, `completed`) VALUES (?, ?, 1) ON DUPLICATE KEY UPDATE `completed` = 1");
                mysqli_stmt_bind_param($stmt, 'si', $onboarding_id, $checklist_id);
            } else {
                // Mark as not completed
                $stmt = mysqli_prepare($conn, "INSERT INTO `onboarding_checklist` (`onboarding_id`, `checklist_id`, `completed`) VALUES (?, ?, 0) ON DUPLICATE KEY UPDATE `completed` = 0");
                mysqli_stmt_bind_param($stmt, 'si', $onboarding_id, $checklist_id);
            }
            mysqli_stmt_execute($stmt);
            mysqli_stmt_close($stmt);
        }

        header("Location: onboarding_workflow.php?id=" . urlencode($onboarding_id));
        exit;
    }
}

// Get checklist items for HR section from offboarding_checklist table
$checklist_items = [];
$checklist_result = mysqli_query($conn, "SELECT oc.*, onbc.completed FROM `offboarding_checklist` oc LEFT JOIN `onboarding_checklist` onbc ON oc.id = onbc.checklist_id AND onbc.onboarding_id = '" . mysqli_real_escape_string($conn, $onboarding_id) . "' ORDER BY oc.id");
if ($checklist_result) {
    while ($row = mysqli_fetch_assoc($checklist_result)) {
        $checklist_items[] = $row;
    }
}

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Onboarding Workflow - <?php echo htmlspecialchars($record['full_name']); ?></title>
    <link rel="shortcut icon" type="image/x-icon" href="assets/img/favicon.jpg">
    <link rel="stylesheet" href="assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="assets/css/animate.css">
    <link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">
    <link rel="stylesheet" href="assets/css/dataTables.bootstrap4.min.css">
    <link rel="stylesheet" href="assets/plugins/fontawesome/css/fontawesome.min.css">
    <link rel="stylesheet" href="assets/plugins/fontawesome/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">

    <style>
        .form-group { position: relative; }
        .form-group input,
        .form-group textarea,
        .form-group select { padding-left: 12px; }
        .card { border: 1px solid #f0f0f0; padding: 20px; box-shadow: 0 4px 8px rgba(0,0,0,0.1); margin-bottom: 20px; }
        .btn-primary { background-color: #007bff; border-color: #007bff; }

        /* Table-like layout for workflow sections */
        .workflow-table .col-lg-4 {
            border-right: 2px solid #f0f0f0;
            min-height: 500px;
            padding: 0 15px;
        }
        .workflow-table .col-lg-4:last-child {
            border-right: none;
        }

        .section-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }
        .section-header.cto { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
        .section-header.it { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
        .section-header.hr { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); }

        .password-toggle { cursor: pointer; }


        /* Responsive adjustments */
        @media (max-width: 991px) {
            .workflow-table .col-lg-4 {
                border-right: none;
                border-bottom: 2px solid #f0f0f0;
                margin-bottom: 20px;
                min-height: auto;
            }
            .workflow-table .col-lg-4:last-child {
                border-bottom: none;
            }
        }
    </style>
</head>
<body>

<div class="main-wrapper">
    <?php include("header.php"); ?>

    <div class="page-wrapper">
        <div class="content">
            <div class="page-header">
                <div class="page-title form-header text-start">
                    <h4>Onboarding Workflow</h4>
                    <p>Manage the onboarding process for <?php echo htmlspecialchars($record['full_name']); ?></p>
                </div>
                <div class="page-btn">
                    <a href="onboarding_list.php" class="btn btn-added">
                        <img src="assets/img/icons/back.svg" alt="img" class="me-1">Back to List
                    </a>
                </div>
            </div>

            <!-- Success Messages -->
            <?php if (isset($_GET['success'])): ?>
                <?php if ($_GET['success'] === 'it_assigned'): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle"></i> <strong>Success!</strong> IT members have been assigned and notification emails sent.
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php elseif ($_GET['success'] === 'override_sent'): ?>
                    <div class="alert alert-warning alert-dismissible fade show" role="alert">
                        <i class="fas fa-user-shield"></i> <strong>Override Sent!</strong> New assignment email sent with updated token. Previous tokens have been expired.
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php elseif ($_GET['success'] === 'emails_resent'): ?>
                    <div class="alert alert-info alert-dismissible fade show" role="alert">
                        <i class="fas fa-paper-plane"></i> <strong>Emails Resent!</strong> IT assignment reminder emails have been sent to primary and backup IT members.
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
            <?php endif; ?>

            <!-- Workflow Table Layout -->
            <div class="card">
                <div class="card-body">
                    <div class="row workflow-table">
                        <!-- CTO Request Section - Left -->
                        <div class="col-lg-4 col-md-12">
                            <div class="section-header cto">
                                <h5><i class="fas fa-envelope me-2"></i>CTO Request</h5>
                            </div>
                            <form method="POST">
                                <div class="form-group">
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="company_email_set" name="company_email_set" <?php echo !empty($record['company_email_set']) ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="company_email_set">
                                            <strong>Company email created</strong>
                                        </label>
                                    </div>
                                </div>

                                <?php if (!empty($record['company_email'])): ?>
                                <div class="alert alert-success">
                                    <i class="fas fa-check-circle"></i> Company email has been created successfully.
                                </div>
                                <?php endif; ?>

                                <div class="form-group">
                                    <button type="button" class="btn btn-outline-primary btn-sm mb-2 w-100" id="resend_company_email">
                                        <i class="fas fa-paper-plane"></i> Resend Setup Email
                                    </button>
                                    <button type="submit" name="update_cto" class="btn btn-submit btn-sm w-100">
                                        <i class="fas fa-save"></i> Update CTO Status
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- IT Support Required Section - Center -->
                        <div class="col-lg-4 col-md-12">
                            <div class="section-header it">
                                <h5><i class="fas fa-laptop me-2"></i>IT Support Required</h5>
                            </div>

                            <?php if (empty($record['it_primary_email'])): ?>
                            <!-- IT Assignment Form -->
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle"></i> <strong>IT members not assigned yet</strong>
                            </div>

                            <button type="button" class="btn btn-primary btn-sm w-100 mb-3" id="show_it_assignment">
                                <i class="fas fa-user-plus"></i> Assign IT Members
                            </button>

                            <div id="it_assignment_form" style="display: none;">
                                <form method="POST">
                                    <div class="form-group">
                                        <label>Primary IT Member *</label>
                                        <select class="form-control select" name="it_primary_email" id="primary_it_select" required>
                                            <option value="">Choose Primary IT Member</option>
                                            <?php
                                            // Fetch IT members from database
                                            $it_members_sql = "SELECT email_address, manager_name FROM `manager_list` WHERE `department_id` = (SELECT department_id FROM department_list WHERE department_name = 'IT' LIMIT 1) ORDER BY manager_name ASC";
                                            $it_members_result = mysqli_query($conn, $it_members_sql);
                                            if ($it_members_result && mysqli_num_rows($it_members_result) > 0) {
                                                while ($it_member = mysqli_fetch_assoc($it_members_result)) {
                                                    echo "<option value='" . htmlspecialchars($it_member['email_address']) . "'>" . htmlspecialchars($it_member['manager_name']) . " (" . htmlspecialchars($it_member['email_address']) . ")</option>";
                                                }
                                            }
                                            ?>
                                            <option value="custom">Custom Email...</option>
                                        </select>
                                        <input type="email" class="form-control mt-2" name="it_primary_custom" id="primary_custom_email" placeholder="Enter custom email" style="display: none;">
                                    </div>

                                    <div class="form-group">
                                        <label>Backup IT Member</label>
                                        <select class="form-control select" name="it_backup_email" id="backup_it_select">
                                            <option value="">Choose Backup IT Member (Optional)</option>
                                            <?php
                                            // Reset result pointer and fetch again
                                            $it_members_result = mysqli_query($conn, $it_members_sql);
                                            if ($it_members_result && mysqli_num_rows($it_members_result) > 0) {
                                                while ($it_member = mysqli_fetch_assoc($it_members_result)) {
                                                    echo "<option value='" . htmlspecialchars($it_member['email_address']) . "'>" . htmlspecialchars($it_member['manager_name']) . " (" . htmlspecialchars($it_member['email_address']) . ")</option>";
                                                }
                                            }
                                            ?>
                                            <option value="custom">Custom Email...</option>
                                        </select>
                                        <input type="email" class="form-control mt-2" name="it_backup_custom" id="backup_custom_email" placeholder="Enter custom email" style="display: none;">
                                    </div>

                                    <div class="form-group">
                                        <label>IT Remarks</label>
                                        <textarea class="form-control" name="it_remarks" rows="2" placeholder="Enter remarks"><?php echo htmlspecialchars($record['it_remarks'] ?? ''); ?></textarea>
                                    </div>

                                    <div class="form-group">
                                        <button type="submit" name="assign_it_members" class="btn btn-success btn-sm w-100">
                                            <i class="fas fa-paper-plane"></i> Assign & Send Notifications
                                        </button>
                                    </div>
                                </form>
                            </div>

                            <?php else: ?>
                            <!-- IT Members Already Assigned -->
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle"></i> <strong>IT members assigned</strong>
                            </div>

                            <div class="mb-3">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <div>
                                        <p class="mb-1"><strong>Primary IT Member:</strong></p>
                                        <small class="text-muted"><?php echo htmlspecialchars($record['it_primary_email']); ?></small>
                                    </div>
                                    <form method="POST" style="display: inline;">
                                        <button type="submit" name="resend_it_emails" class="btn btn-info btn-sm">
                                            <i class="fas fa-paper-plane"></i> Resend
                                        </button>
                                    </form>
                                </div>

                                <?php if (!empty($record['it_backup_email'])): ?>
                                <div class="mb-2">
                                    <p class="mb-1"><strong>Backup IT Member:</strong></p>
                                    <small class="text-muted"><?php echo htmlspecialchars($record['it_backup_email']); ?></small>
                                </div>
                                <?php endif; ?>

                                <?php if (!empty($record['it_remarks'])): ?>
                                <div class="mb-2">
                                    <p class="mb-1"><strong>Remarks:</strong></p>
                                    <small class="text-muted"><?php echo htmlspecialchars($record['it_remarks']); ?></small>
                                </div>
                                <?php endif; ?>
                            </div>

                            <form method="POST">
                                <div class="form-group">
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="laptop_email_sent" name="laptop_email_sent" <?php echo !empty($record['laptop_email_sent']) ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="laptop_email_sent">
                                            <strong>Laptop setup completed</strong>
                                        </label>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <button type="submit" name="update_it" class="btn btn-submit btn-sm w-100 mb-2">
                                        <i class="fas fa-save"></i> Update IT Status
                                    </button>
                                </div>

                            </form>

                            <!-- Admin Override Section -->
                            <div class="mt-3">
                                <button type="button" class="btn btn-warning btn-sm w-100" id="show_it_override">
                                    <i class="fas fa-user-shield"></i> Admin Override
                                </button>

                                <div id="it_override_form" style="display: none;" class="mt-2">
                                    <form method="POST">
                                        <div class="form-group">
                                            <label>Override Email</label>
                                            <input type="email" class="form-control" name="override_email" placeholder="Enter email for new assignment" required>
                                            <small class="form-text text-muted">This will generate a new token and expire previous links</small>
                                        </div>
                                        <div class="form-group">
                                            <button type="submit" name="admin_override" class="btn btn-warning btn-sm w-100">
                                                <i class="fas fa-paper-plane"></i> Send Override Assignment
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>

                        <!-- HR Admin Required Section - Right -->
                        <div class="col-lg-4 col-md-12">
                            <div class="section-header hr">
                                <h5><i class="fas fa-clipboard-check me-2"></i>HR Admin Required</h5>
                            </div>
                            <form method="POST">
                                <div class="checklist-container" style="max-height: 400px; overflow-y: auto; background: #f9f9f9; padding: 15px; border-radius: 8px; border: 1px solid #e0e0e0;">
                                    <?php if (empty($checklist_items)): ?>
                                    <div class="alert alert-info text-center">
                                        <i class="fas fa-info-circle"></i> No checklist items found in offboarding_checklist table.
                                    </div>
                                    <?php else: ?>
                                    <?php foreach ($checklist_items as $item): ?>
                                    <div class="checklist-item" style="padding: 8px 0; border-bottom: 1px solid #eee; font-size: 14px;">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="checklist_<?php echo $item['id']; ?>" name="checklist_<?php echo $item['id']; ?>" <?php echo !empty($item['completed']) ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="checklist_<?php echo $item['id']; ?>">
                                                <?php echo htmlspecialchars($item['checklist_name'] ?? 'Checklist Item #' . $item['id']); ?>
                                            </label>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                    <?php endif; ?>
                                </div>

                                <div class="form-group mt-3">
                                    <button type="submit" name="update_hr" class="btn btn-submit btn-sm w-100">
                                        <i class="fas fa-save"></i> Update HR Checklist
                                    </button>
                                </div>
                            </form>
                        </div>

                    </div>
                </div>
            </div>



        </div>
    </div>
</div>

<?php
// Step 4: Notify HR when laptop setup is completed
function send_hr_laptop_completion_notification($onboarding_id, $conn) {

    // Get employee record with related names
    $stmt = mysqli_prepare($conn, "
        SELECT ol.*,
               dl.department_name,
               ml.manager_name,
               ofl.office_name as office_name_actual
        FROM `onboarding_list` ol
        LEFT JOIN `department_list` dl ON ol.department_id = dl.department_id
        LEFT JOIN `manager_list` ml ON ol.manager_id = ml.manager_id
        LEFT JOIN `office_list` ofl ON ol.office_id = ofl.office_id
        WHERE ol.onboarding_id = ?
    ");
    mysqli_stmt_bind_param($stmt, 's', $onboarding_id);
    mysqli_stmt_execute($stmt);
    $res = mysqli_stmt_get_result($stmt);
    $rec = mysqli_fetch_assoc($res);
    mysqli_stmt_close($stmt);

    if (!$rec) return;

    function mailer(){
        $m = new PHPMailer(true);
        $m->isSMTP();
        $m->Host='smtp.office365.com';
        $m->SMTPAuth=true;
        $m->Username='<EMAIL>';
        $m->Password='&[i3F6}0hOw6';
        $m->SMTPSecure='tls';
        $m->Port=587;
        $m->setFrom('<EMAIL>','CLLXWARE');
        return $m;
    }

    try {
        // Get HR email configuration
        $hr_config = mysqli_query($conn, "SELECT * FROM `onboarding_email_config` WHERE `config_type` = 'hr_admin'");
        $hr_data = mysqli_fetch_assoc($hr_config);

        $hr_emails = $hr_data ? array_filter(array_map('trim', explode("\n", $hr_data['email_addresses']))) : ['<EMAIL>'];

        $workflow_link = (isset($_SERVER['HTTP_HOST']) ? ( (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://') . $_SERVER['HTTP_HOST'] . dirname($_SERVER['PHP_SELF']) ) : 'https://cis.cllsystems.com:9443/staging');
        $workflow_link = rtrim($workflow_link,'/')."/onboarding_workflow.php?id=".urlencode($rec['onboarding_id']);

        $email_subject = $hr_data['email_subject'] ?? 'HR Checklist Required - New Joiner: {employee_name}';
        $email_template = $hr_data['email_template'] ?? 'Dear HR Team,

A new employee onboarding checklist requires completion:

Employee Information:
- Name: {employee_name}
- Employee ID: {employee_id}
- Department: {department}
- Designation: {designation}
- Onboarding Date: {onboarding_date}
- Reporting Manager: {reporting_manager}
- Office Location: {office_name}
- Company Email: {company_email}

Please click the link below to complete the HR checklist:
{workflow_link}

Best regards,
HR Team';

        // Replace template variables
        $email_subject = str_replace('{employee_name}', $rec['full_name'], $email_subject);
        $email_body = str_replace([
            '{employee_name}', '{employee_id}', '{department}', '{designation}',
            '{onboarding_date}', '{reporting_manager}', '{office_name}',
            '{company_email}', '{workflow_link}'
        ], [
            $rec['full_name'], $rec['employee_id'], $rec['department_name'] ?? 'Not specified', $rec['job_title'],
            $rec['onboarding_date'], $rec['manager_name'] ?? 'Not assigned', $rec['office_name_actual'] ?? 'Not specified',
            $rec['company_email'] ?? 'Not set', $workflow_link
        ], $email_template);

        $mail = new PHPMailer(true);
        $mail->isSMTP();
        $mail->Host='smtp.office365.com';
        $mail->SMTPAuth=true;
        $mail->Username='<EMAIL>';
        $mail->Password='&[i3F6}0hOw6';
        $mail->SMTPSecure='tls';
        $mail->Port=587;
        $mail->setFrom('<EMAIL>','CLLXWARE');

        foreach($hr_emails as $email) {
            $mail->addAddress($email);
        }
        $mail->isHTML(false);
        $mail->Subject = $email_subject;
        $mail->Body = $email_body;
        $mail->send();

    } catch (Exception $e) {
        // Log error but don't stop the process
        error_log('HR notification error: ' . $e->getMessage());
    }
}

// Function to send IT assignment email
function send_it_assignment_email($onboarding_id, $it_email, $role, $conn) {
    // Get employee record with related names
    $stmt = mysqli_prepare($conn, "
        SELECT ol.*,
               dl.department_name,
               ml.manager_name,
               ofl.office_name as office_name_actual
        FROM `onboarding_list` ol
        LEFT JOIN `department_list` dl ON ol.department_id = dl.department_id
        LEFT JOIN `manager_list` ml ON ol.manager_id = ml.manager_id
        LEFT JOIN `office_list` ofl ON ol.office_id = ofl.office_id
        WHERE ol.onboarding_id = ?
    ");
    mysqli_stmt_bind_param($stmt, 's', $onboarding_id);
    mysqli_stmt_execute($stmt);
    $res = mysqli_stmt_get_result($stmt);
    $rec = mysqli_fetch_assoc($res);
    mysqli_stmt_close($stmt);

    if (!$rec) return;

    try {
        // Create completion link
        $token = hash('sha256', $rec['onboarding_id'] . $it_email . 'IT_COMPLETION_2024');
        $base_url = (isset($_SERVER['HTTP_HOST']) ? ( (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://') . $_SERVER['HTTP_HOST'] . dirname($_SERVER['PHP_SELF']) ) : 'https://cis.cllsystems.com:9443/staging');
        $completion_link = rtrim($base_url,'/')."/it_completion.php?id=".urlencode($rec['onboarding_id'])."&token=".urlencode($token)."&email=".urlencode($it_email);

        $mail = new PHPMailer(true);
        $mail->isSMTP();
        $mail->Host='smtp.office365.com';
        $mail->SMTPAuth=true;
        $mail->Username='<EMAIL>';
        $mail->Password='&[i3F6}0hOw6';
        $mail->SMTPSecure='tls';
        $mail->Port=587;
        $mail->setFrom('<EMAIL>','CLLXWARE');

        $mail->addAddress($it_email);

        // Get IT email configuration
        $it_config = mysqli_query($conn, "SELECT * FROM `onboarding_email_config` WHERE `config_type` = 'it_support'");
        $it_data = mysqli_fetch_assoc($it_config);

        $role_text = ($role === 'primary') ? 'Primary' : 'Backup';
        $email_subject = $it_data['email_subject'] ?? 'Laptop Setup Required - New Joiner: {employee_name}';
        $email_template = $it_data['email_template'] ?? 'Dear IT Support Team,

A new employee requires laptop setup:

Employee Information:
- Name: {employee_name}
- Employee ID: {employee_id}
- Department: {department}
- Designation: {designation}
- Onboarding Date: {onboarding_date}
- Reporting Manager: {reporting_manager}
- Office Location: {office_name}
- Company Email: {company_email}
- Company Email Password: {company_password}

Please click the link below to complete the IT setup and mark as completed:
{completion_link}

Priority: Primary contacts should handle this request. Backup contacts are CC\'d for awareness.

Best regards,
HR Team';

        // Replace template variables
        $email_subject = str_replace('{employee_name}', $rec['full_name'], $email_subject);
        $email_body = str_replace([
            '{employee_name}', '{employee_id}', '{department}', '{designation}',
            '{onboarding_date}', '{reporting_manager}', '{office_name}',
            '{company_email}', '{company_password}', '{completion_link}'
        ], [
            $rec['full_name'], $rec['employee_id'], $rec['department_name'] ?? 'Not specified', $rec['job_title'],
            $rec['onboarding_date'], $rec['manager_name'] ?? 'Not assigned', $rec['office_name_actual'] ?? 'Not specified',
            $rec['company_email'] ?? 'Not set', base64_decode($rec['company_email_password'] ?? ''), $completion_link
        ], $email_template);

        $mail->Subject = $email_subject;
        $mail->Body = $email_body;
        $mail->isHTML(false);
        $mail->send();

    } catch (Exception $e) {
        error_log('IT assignment email error: ' . $e->getMessage());
    }
}

// Function to send override assignment email
function send_override_assignment_email($onboarding_id, $override_email, $token, $conn) {
    // Get employee record with related names
    $stmt = mysqli_prepare($conn, "
        SELECT ol.*,
               dl.department_name,
               ml.manager_name,
               ofl.office_name as office_name_actual
        FROM `onboarding_list` ol
        LEFT JOIN `department_list` dl ON ol.department_id = dl.department_id
        LEFT JOIN `manager_list` ml ON ol.manager_id = ml.manager_id
        LEFT JOIN `office_list` ofl ON ol.office_id = ofl.office_id
        WHERE ol.onboarding_id = ?
    ");
    mysqli_stmt_bind_param($stmt, 's', $onboarding_id);
    mysqli_stmt_execute($stmt);
    $res = mysqli_stmt_get_result($stmt);
    $rec = mysqli_fetch_assoc($res);
    mysqli_stmt_close($stmt);

    if (!$rec) return;

    try {
        // Create completion link with new token
        $base_url = (isset($_SERVER['HTTP_HOST']) ? ( (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://') . $_SERVER['HTTP_HOST'] . dirname($_SERVER['PHP_SELF']) ) : 'https://cis.cllsystems.com:9443/staging');
        $completion_link = rtrim($base_url,'/')."/it_completion.php?id=".urlencode($rec['onboarding_id'])."&token=".urlencode($token)."&email=".urlencode($override_email);

        $mail = new PHPMailer(true);
        $mail->isSMTP();
        $mail->Host='smtp.office365.com';
        $mail->SMTPAuth=true;
        $mail->Username='<EMAIL>';
        $mail->Password='&[i3F6}0hOw6';
        $mail->SMTPSecure='tls';
        $mail->Port=587;
        $mail->setFrom('<EMAIL>','CLLXWARE');

        $mail->addAddress($override_email);

        // Get IT email configuration
        $it_config = mysqli_query($conn, "SELECT * FROM `onboarding_email_config` WHERE `config_type` = 'it_support'");
        $it_data = mysqli_fetch_assoc($it_config);

        $email_subject = $it_data['email_subject'] ?? 'Laptop Setup Required - New Joiner: {employee_name}';
        $email_template = $it_data['email_template'] ?? 'Dear IT Support Team,

A new employee requires laptop setup:

Employee Information:
- Name: {employee_name}
- Employee ID: {employee_id}
- Department: {department}
- Designation: {designation}
- Onboarding Date: {onboarding_date}
- Reporting Manager: {reporting_manager}
- Office Location: {office_name}
- Company Email: {company_email}
- Company Email Password: {company_password}

Please click the link below to complete the IT setup and mark as completed:
{completion_link}

Priority: Primary contacts should handle this request. Backup contacts are CC\'d for awareness.

Best regards,
HR Team';

        // Replace template variables
        $email_subject = str_replace('{employee_name}', $rec['full_name'], 'URGENT: IT Assignment Override - ' . $email_subject);
        $email_body = str_replace([
            '{employee_name}', '{employee_id}', '{department}', '{designation}',
            '{onboarding_date}', '{reporting_manager}', '{office_name}',
            '{company_email}', '{company_password}', '{completion_link}'
        ], [
            $rec['full_name'], $rec['employee_id'], $rec['department_name'] ?? 'Not specified', $rec['job_title'],
            $rec['onboarding_date'], $rec['manager_name'] ?? 'Not assigned', $rec['office_name_actual'] ?? 'Not specified',
            $rec['company_email'] ?? 'Not set', base64_decode($rec['company_email_password'] ?? ''), $completion_link
        ], $email_template);

        $mail->Subject = $email_subject;
        $mail->Body = $email_body . "\n\nIMPORTANT: Previous assignment tokens have been expired. Only this link is now valid.\n\nNote: This link will expire after the setup is marked complete.";
        $mail->isHTML(false);
        $mail->send();

    } catch (Exception $e) {
        error_log('Override assignment email error: ' . $e->getMessage());
    }
}

// Function to resend IT assignment reminder emails
function send_it_assignment_reminder_emails($onboarding_id, $conn) {
    // Get employee record with IT assignments
    $stmt = mysqli_prepare($conn, "
        SELECT ol.*,
               dl.department_name,
               ml.manager_name,
               ofl.office_name as office_name_actual
        FROM `onboarding_list` ol
        LEFT JOIN `department_list` dl ON ol.department_id = dl.department_id
        LEFT JOIN `manager_list` ml ON ol.manager_id = ml.manager_id
        LEFT JOIN `office_list` ofl ON ol.office_id = ofl.office_id
        WHERE ol.onboarding_id = ?
    ");
    mysqli_stmt_bind_param($stmt, 's', $onboarding_id);
    mysqli_stmt_execute($stmt);
    $res = mysqli_stmt_get_result($stmt);
    $rec = mysqli_fetch_assoc($res);
    mysqli_stmt_close($stmt);

    if (!$rec) return;

    // Get time configuration for display
    $time_config = mysqli_query($conn, "SELECT cron_time FROM `onboarding_email_config` WHERE config_type = 'it_support' LIMIT 1");
    $time_data = mysqli_fetch_assoc($time_config);
    $base_time = $time_data ? $time_data['cron_time'] : '09:00';
    $start_time = $base_time . ':00';
    $end_time = date('H:i:s', strtotime($start_time . ' +1 hour'));
    $display_start_time = date('g:i A', strtotime($start_time));
    $display_end_time = date('g:i A', strtotime($end_time));

    try {
        $mail = new PHPMailer(true);
        $mail->isSMTP();
        $mail->Host='smtp.office365.com';
        $mail->SMTPAuth=true;
        $mail->Username='<EMAIL>';
        $mail->Password='&[i3F6}0hOw6';
        $mail->SMTPSecure='tls';
        $mail->Port=587;
        $mail->setFrom('<EMAIL>','CLLXWARE');

        // Get IT email configuration
        $it_config = mysqli_query($conn, "SELECT * FROM `onboarding_email_config` WHERE `config_type` = 'it_support'");
        $it_data = mysqli_fetch_assoc($it_config);

        $email_subject = $it_data['email_subject'] ?? 'Laptop Setup Required - New Joiner: {employee_name}';
        $email_template = $it_data['email_template'] ?? 'Dear IT Support Team,

A new employee requires laptop setup:

Employee Information:
- Name: {employee_name}
- Employee ID: {employee_id}
- Department: {department}
- Designation: {designation}
- Onboarding Date: {onboarding_date}
- Reporting Manager: {reporting_manager}
- Office Location: {office_name}
- Company Email: {company_email}
- Company Email Password: {company_password}

Please click the link below to complete the IT setup and mark as completed:
{completion_link}

Priority: Primary contacts should handle this request. Backup contacts are CC\'d for awareness.

Best regards,
HR Team';

        // Send to primary IT member
        if (!empty($rec['it_primary_email'])) {
            $mail->clearAddresses();
            $mail->addAddress($rec['it_primary_email']);

            $primary_token = hash('sha256', $onboarding_id . $rec['it_primary_email'] . 'IT_COMPLETION_2024');
            $base_url = (isset($_SERVER['HTTP_HOST']) ? ( (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://') . $_SERVER['HTTP_HOST'] . dirname($_SERVER['PHP_SELF']) ) : 'https://cis.cllsystems.com:9443/staging');
            $completion_link = rtrim($base_url,'/')."/it_completion.php?id=".urlencode($onboarding_id)."&email=".urlencode($rec['it_primary_email'])."&token=".urlencode($primary_token);

            // Replace template variables for primary
            $primary_subject = str_replace('{employee_name}', $rec['full_name'], 'REMINDER: ' . $email_subject);
            $primary_body = str_replace([
                '{employee_name}', '{employee_id}', '{department}', '{designation}',
                '{onboarding_date}', '{reporting_manager}', '{office_name}',
                '{company_email}', '{company_password}', '{completion_link}'
            ], [
                $rec['full_name'], $rec['employee_id'], $rec['department_name'] ?? 'Not specified', $rec['job_title'],
                $rec['onboarding_date'], $rec['manager_name'] ?? 'Not assigned', $rec['office_name_actual'] ?? 'Not specified',
                $rec['company_email'] ?? 'Not set', base64_decode($rec['company_email_password'] ?? ''), $completion_link
            ], $email_template);

            $mail->Subject = $primary_subject;
            $mail->isHTML(true);

            $mail->Body = "
            <html>
            <body>
                <h3>IT Assignment Reminder (Primary)</h3>
                " . nl2br(htmlspecialchars($primary_body)) . "

                <div style='background-color: #f8f9fa; padding: 15px; border-left: 4px solid #28a745; margin: 20px 0;'>
                    <h4>Action Required:</h4>
                    <p>Please complete the laptop setup and click the link below to mark it as complete:</p>
                    <p><a href='" . $completion_link . "' style='background-color: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Mark Setup Complete</a></p>
                </div>
            </body>
            </html>";

            $mail->send();
        }

        // Send to backup IT member
        if (!empty($rec['it_backup_email'])) {
            $mail->clearAddresses();
            $mail->addAddress($rec['it_backup_email']);

            $backup_token = hash('sha256', $onboarding_id . $rec['it_backup_email'] . 'IT_COMPLETION_2024');
            $backup_completion_link = rtrim($base_url,'/')."/it_completion.php?id=".urlencode($onboarding_id)."&email=".urlencode($rec['it_backup_email'])."&token=".urlencode($backup_token);

            // Replace template variables for backup
            $backup_subject = str_replace('{employee_name}', $rec['full_name'], 'REMINDER: ' . $email_subject);
            $backup_body = str_replace([
                '{employee_name}', '{employee_id}', '{department}', '{designation}',
                '{onboarding_date}', '{reporting_manager}', '{office_name}',
                '{company_email}', '{company_password}', '{completion_link}'
            ], [
                $rec['full_name'], $rec['employee_id'], $rec['department_name'] ?? 'Not specified', $rec['job_title'],
                $rec['onboarding_date'], $rec['manager_name'] ?? 'Not assigned', $rec['office_name_actual'] ?? 'Not specified',
                $rec['company_email'] ?? 'Not set', base64_decode($rec['company_email_password'] ?? ''), $backup_completion_link
            ], $email_template);

            $mail->Subject = $backup_subject;
            $mail->isHTML(true);

            $mail->Body = "
            <html>
            <body>
                <h3>IT Assignment Reminder (Backup)</h3>
                " . nl2br(htmlspecialchars($backup_body)) . "

                <div style='background-color: #f8f9fa; padding: 15px; border-left: 4px solid #28a745; margin: 20px 0;'>
                    <h4>Action Required:</h4>
                    <p>You are the backup support. If needed, please complete the laptop setup and click the link below:</p>
                    <p><a href='" . $backup_completion_link . "' style='background-color: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Mark Setup Complete</a></p>
                </div>
            </body>
            </html>";

            $mail->send();
        }

    } catch (Exception $e) {
        error_log('IT reminder email error: ' . $e->getMessage());
    }
}
?>

<!-- Scripts -->
<script src="assets/js/jquery-3.6.0.min.js"></script>
<script src="assets/js/feather.min.js"></script>
<script src="assets/js/jquery.slimscroll.min.js"></script>
<script src="assets/js/jquery.dataTables.min.js"></script>
<script src="assets/js/dataTables.bootstrap4.min.js"></script>
<script src="assets/js/bootstrap.bundle.min.js"></script>
<script src="assets/plugins/select2/js/select2.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalert2.all.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalerts.min.js"></script>

<script>
    // Password toggle functionality
    document.getElementById('toggle_password')?.addEventListener('click', function() {
        const passwordField = document.getElementById('company_password');
        const eyeIcon = document.getElementById('eye_icon');

        if (passwordField.type === 'password') {
            passwordField.type = 'text';
            eyeIcon.className = 'fas fa-eye-slash';
        } else {
            passwordField.type = 'password';
            eyeIcon.className = 'fas fa-eye';
        }
    });

    // Resend email functionality
    const obId = '<?php echo htmlspecialchars($onboarding_id); ?>';
    function callResend(action) {
        const xhr = new XMLHttpRequest();
        xhr.open('POST', 'resend_onboarding_email.php', true);
        xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
        xhr.onload = function() {
            if (xhr.status >= 200 && xhr.status < 300) {
                Swal.fire({
                    title: 'Success!',
                    text: 'Email sent successfully!',
                    icon: 'success',
                    confirmButtonText: 'OK'
                });
            } else {
                Swal.fire({
                    title: 'Error!',
                    text: 'Error: ' + xhr.responseText,
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        };
        xhr.send('action=' + encodeURIComponent(action) + '&onboarding_id=' + encodeURIComponent(obId));
    }

    document.getElementById('resend_company_email')?.addEventListener('click', () => callResend('company'));
    document.getElementById('resend_laptop_email')?.addEventListener('click', () => callResend('laptop'));

    // Initialize Select2 for IT assignment dropdowns
    $(document).ready(function() {
        $('.select').select2({
            width: '100%'
        });
    });

    // IT Assignment functionality
    document.getElementById('show_it_assignment')?.addEventListener('click', function() {
        const form = document.getElementById('it_assignment_form');
        if (form.style.display === 'none') {
            form.style.display = 'block';
            this.innerHTML = '<i class="fas fa-user-minus"></i> Cancel Assignment';
            this.classList.remove('btn-primary');
            this.classList.add('btn-secondary');
        } else {
            form.style.display = 'none';
            this.innerHTML = '<i class="fas fa-user-plus"></i> Assign IT Members';
            this.classList.remove('btn-secondary');
            this.classList.add('btn-primary');
        }
    });

    // Admin Override functionality
    document.getElementById('show_it_override')?.addEventListener('click', function() {
        const form = document.getElementById('it_override_form');
        if (form.style.display === 'none') {
            form.style.display = 'block';
            this.innerHTML = '<i class="fas fa-times"></i> Cancel Override';
        } else {
            form.style.display = 'none';
            this.innerHTML = '<i class="fas fa-user-shield"></i> Admin Override';
        }
    });

    // Handle custom email selection for primary IT
    document.getElementById('primary_it_select')?.addEventListener('change', function() {
        const customField = document.getElementById('primary_custom_email');
        if (this.value === 'custom') {
            customField.style.display = 'block';
            customField.setAttribute('required', 'true');
        } else {
            customField.style.display = 'none';
            customField.removeAttribute('required');
        }
    });

    // Handle custom email selection for backup IT
    document.getElementById('backup_it_select')?.addEventListener('change', function() {
        const customField = document.getElementById('backup_custom_email');
        if (this.value === 'custom') {
            customField.style.display = 'block';
        } else {
            customField.style.display = 'none';
        }
    });
</script>

<script src="assets/js/script.js"></script>

</body>
</html>
