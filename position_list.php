<?php
require("database.php");

if (isset($_SESSION["user"])) {
    $email = $_SESSION["user"];
?>
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0">
<meta name="description" content="CLL Ticketing System">
<meta name="author" content="Powered by CLL Systems Sdn Bhd">
<meta name="robots" content="noindex, nofollow">
<title>CLLXWARE - Position List</title>

<link rel='icon' type='image/x-icon' href='https://www.cllsystems.com/wp-content/themes/cllsystems/img/favicon.png'>
<link rel="stylesheet" href="assets/css/bootstrap.min.css">
<link rel="stylesheet" href="assets/css/animate.css">
<link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">
<link rel="stylesheet" href="assets/css/dataTables.bootstrap4.min.css">
<link rel="stylesheet" href="assets/plugins/fontawesome/css/fontawesome.min.css">
<link rel="stylesheet" href="assets/plugins/fontawesome/css/all.min.css">
<link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
<div id="global-loader">
    <div class="whirly-loader"></div>
</div>

<div class="main-wrapper">

<?php 
$role_id = $_SESSION["role_id"];
$sql20 = "SELECT * FROM `role-list` WHERE `role-id` = ?";
$stmt20 = mysqli_prepare($conn, $sql20);
mysqli_stmt_bind_param($stmt20, "s", $role_id);
mysqli_stmt_execute($stmt20);
$result20 = mysqli_stmt_get_result($stmt20);

if ($row20 = mysqli_fetch_assoc($result20)) {
    $all = $row20['offboarding_all'];
    if ($all != '1') {
        header("location: ./index.php");
        exit;
    }
} else {
    echo "<script>alert('Role data not found')</script>";
    exit;
}

mysqli_stmt_close($stmt20);
include("header.php");
?>

</div>

<div class="page-wrapper">
    <div class="content">
        <div class="page-header">
            <div class="page-title">
                <h4>Position List</h4>
                <h6>Manage Positions</h6>
            </div>
            <?php
            if ($row20 && $row20['offboarding_all'] == '1') {
                echo "
                <div class='page-btn'>
                    <a href='add_position.php' class='btn btn-added'><img src='assets/img/icons/plus.svg' class='me-2' alt='img'>Add New Position</a>
                </div>";
            }
            ?>
        </div>

        <div class="card">
            <div class="card-body">
                <div class="table-top">
                    <div class="search-set">
                        <div class="search-input">
                            <a class="btn btn-searchset"><img src="assets/img/icons/search-white.svg" alt="img"></a>
                        </div>

                    </div>
                    <div class="wordset">
                        <ul>
                            <form action='csv_position.php' method='post'>
                                <li>
                                    <a data-bs-toggle="tooltip" data-bs-placement="top" title="CSV" href="#" onclick="submitForm();">
                                        <img src="assets/img/icons/excel.svg" alt="CSV">
                                    </a>
                                </li>
                            </form>
                            <li>
                                <a data-bs-toggle="tooltip" data-bs-placement="top" title="Print"><img src="assets/img/icons/printer.svg" alt="img" id="printButton"></a>
                            </li>
                        </ul>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table datanew">
                        <thead>
                            <tr>
                                <th>No</th>
                                <th>Position Name</th>
                                <th>Department</th>
                                <th>Description</th>
                                <?php 
                                $num_actual_columns = 4; // Base columns
                                if ($row20 && $row20['offboarding_all'] == '1') {
                                    echo "<th>Action</th>";
                                    $num_actual_columns++;
                                }
                                ?>
                            </tr>
                        </thead>
                        <tbody>
                            <?php 
                            $sql = "SELECT p.*, d.department_name 
                                   FROM `position_list` p 
                                   LEFT JOIN `department_list` d ON p.department_id = d.department_id
                                   ORDER BY p.position_name";
                            $result = mysqli_query($conn, $sql);

                            if ($result && mysqli_num_rows($result) > 0) {
                                $counter = 1;
                                while ($row = mysqli_fetch_assoc($result)) {
                                    echo "
                                    <tr> 
                                        <td>{$counter}</td>
                                        <td>" . htmlspecialchars($row['position_name']) . "</td>
                                        <td>" . htmlspecialchars($row['department_name'] ?: 'Not Assigned') . "</td>
                                        <td>" . htmlspecialchars($row['description']) . "</td>"; // End of base columns echo block

                                    if ($row20 && $row20['offboarding_all'] == '1') { // Same condition as header
                                        echo "<td> 
                                            <a class='me-3' href='edit_position.php?position_id=" . htmlspecialchars($row['position_id']) . "'> 
                                                <img src='assets/img/icons/edit.svg' alt='img'> 
                                            </a> 
                                            <a href='#' onclick='deleteItem(\"" . htmlspecialchars($row['position_id']) . "\")'> 
                                                <img src='assets/img/icons/delete.svg' alt='Delete'> 
                                            </a> 
                                        </td>";
                                    }
                                    echo "</tr>";
                                    $counter++;
                                }
                            } // else, if no data, do nothing here, tbody will be empty.
                              // DataTables will use its 'sEmptyTable' or 'sZeroRecords' message.
                            ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

    </div>
</div>
</div>

<script>
function deleteItem(positionId) {
    if (confirm('Are you sure you want to delete this position?')) {
        var xhr = new XMLHttpRequest();
        xhr.open('POST', 'delete_position.php', true);
        xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');

        xhr.onload = function () {
            if (xhr.status >= 200 && xhr.status < 300) {
                window.location.reload();
            } else {
                console.error('Error:', xhr.responseText);
                alert('Error deleting item: ' + xhr.responseText);
            }
        };

        xhr.onerror = function () {
            alert('Network Error. Please try again.');
        };

        xhr.send('position_id=' + encodeURIComponent(positionId));
    }
}

document.getElementById("printButton").addEventListener("click", function() {
    const originalTable = document.querySelector(".datanew");
    if (!originalTable) {
        alert("Error: Table not found.");
        return;
    }

    const printTable = originalTable.cloneNode(true);
    const printContainer = document.createElement("div");
    printContainer.style.display = "none";
    printContainer.appendChild(printTable);
    document.body.appendChild(printContainer);

    printTable.style.width = "100%";
    printTable.style.border = "1px solid black";
    const stylesheet = document.createElement("style");
    stylesheet.textContent = "@page { size: landscape; }";
    printContainer.appendChild(stylesheet);

    window.print();
    document.body.removeChild(printContainer);
});

function submitForm() {
    var form = document.querySelector("form");
    if (form) {
        form.submit();
    } else {
        alert("Error: Form not found.");
    }
}
</script>

<script src="assets/js/jquery-3.6.0.min.js"></script>
<script src="assets/js/feather.min.js"></script>
<script src="assets/js/jquery.slimscroll.min.js"></script>
<script src="assets/js/jquery.dataTables.min.js"></script>
<script src="assets/js/dataTables.bootstrap4.min.js"></script>
<script src="assets/js/bootstrap.bundle.min.js"></script>
<script src="assets/plugins/select2/js/select2.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalert2.all.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalerts.min.js"></script>
<script src="assets/js/script.js"></script>
</body>
</html>
<?php
} else {
    header("location: ./signin.php");
    exit;
}
?>
