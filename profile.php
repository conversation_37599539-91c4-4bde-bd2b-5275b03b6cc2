<?php
require("database.php");
if (isset($_SESSION["user"])) {
    $email = $_SESSION["user"];
    $acc_id = $_SESSION["acc_id"];

    if (isset($_POST["submit"])) {
        $name = $_POST["name"];
        $designation = $_POST["designation"];
        $department = $_POST["department"];
        $profile_image = '';

        // Handle file upload
        if (isset($_FILES['profile_image']) && $_FILES['profile_image']['error'] == 0) {
            // Check file size
            if ($_FILES['profile_image']['size'] > 25 * 1024 * 1024) {
                echo "<script>alert('Error: File size exceeds 25MB.'); window.location='profile.php';</script>";
                exit();
            }

            $target_dir = "profile-image/";

            // Check if the directory exists, if not, create it
            if (!file_exists($target_dir)) {
                mkdir($target_dir, 0777, true);
            }

            $target_file = $target_dir . basename($_FILES["profile_image"]["name"]);
            if (move_uploaded_file($_FILES["profile_image"]["tmp_name"], $target_file)) {
                $profile_image = $target_file;
            } else {
                echo "<script>alert('Error uploading file.');</script>";
            }
        }

        if (!empty($profile_image)) {
            $sql = "UPDATE `user-list` 
                    SET `name` = ?, 
                        `designation` = ?, 
                        `department` = ?, 
                        `profile_image` = ?
                    WHERE `acc_id` = ?";
            $stmt = mysqli_prepare($conn, $sql);
            mysqli_stmt_bind_param($stmt, "sssss", $name, $designation, $department, $profile_image, $acc_id);
        } else {
            $sql = "UPDATE `user-list` 
                    SET `name` = ?, 
                        `designation` = ?, 
                        `department` = ?
                    WHERE `acc_id` = ?";
            $stmt = mysqli_prepare($conn, $sql);
            mysqli_stmt_bind_param($stmt, "ssss", $name, $designation, $department, $acc_id);
        }

        if (mysqli_stmt_execute($stmt)) {
            echo "<script>alert('The profile $name has been modified.'); window.location='profile.php';</script>";
        } else {
            echo "<script>alert('Error: " . mysqli_error($conn) . "'); window.location='profile.php';</script>";
        }
        mysqli_stmt_close($stmt);
    }
?>
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0">
<meta name="description" content="CLL Warehouse Inventory System">
<meta name="author" content="Powered by CLL Systems Sdn Bhd">
<meta name="robots" content="noindex, nofollow">
<title>CLLXWARE-Profile</title>

<link rel='icon' type='image/x-icon' href='https://www.cllsystems.com/wp-content/themes/cllsystems/img/favicon.png'>

<link rel="stylesheet" href="assets/css/bootstrap.min.css">
<link rel="stylesheet" href="assets/css/animate.css">
<link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">
<link rel="stylesheet" href="assets/css/dataTables.bootstrap4.min.css">
<link rel="stylesheet" href="assets/plugins/fontawesome/css/fontawesome.min.css">
<link rel="stylesheet" href="assets/plugins/fontawesome/css/all.min.css">
<link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
<div id="global-loader">
<div class="whirly-loader"> </div>
</div>

<div class="main-wrapper">

<?php include("header.php"); ?>
</div>

<div class="page-wrapper">
<div class="content">
<div class="page-header">
<div class="page-title">
<h4>Profile</h4>
<h6>User Profile</h6>
</div>
</div>

<div class="card">
<div class="card-body">
<form action="profile.php" method="post" enctype="multipart/form-data">
<?php
$acc_id = $_SESSION["acc_id"];
$role_id = $_SESSION["role_id"];
$sql = "SELECT * FROM `user-list` WHERE `acc_id`='$acc_id'";
$result = mysqli_query($conn, $sql);
$row = mysqli_fetch_assoc($result);

$sql2 = "SELECT `role` FROM `role-list` WHERE `role-id`='$role_id'";
$result2 = mysqli_query($conn, $sql2);
$row2 = mysqli_fetch_assoc($result2);

$profile_image = $row['profile_image'] ? $row['profile_image'] : 'assets/img/profiles/profile_pic.png';

echo "
<div class='profile-set'>
<div class='profile-head'>
</div>
<div class='profile-top'>
<div class='profile-content'>
<div class='profile-contentimg'>
<img src='$profile_image' alt='img' id='blah' >
<div class='profileupload'>
<input type='file' name='profile_image' id='imgInp' accept='image/*'>
<img src='assets/img/icons/edit-set.svg' alt='img'>
</div>
</div>
<div class='profile-contentname'>
<h2>" . $row['name'] . " (" . $row2['role'] . ")</h2>
<h4>Updates Your Photo and Personal Details.</h4>
</div>
</div>
</div>
</div>
<div class='row'>
<div class='col-lg-6 col-sm-12'>
<div class='form-group'>
<label>Full Name</label>
<input type='text' placeholder='Your Full Name' name='name' value='" . $row['name'] . "'>
</div>
</div>
<div class='col-lg-6 col-sm-12'>
<div class='form-group'>
<label>Email</label>
<input type='text' placeholder='Your Email' value='" . $row['email'] . "' readonly>
</div>
</div>
<div class='col-lg-6 col-sm-12'>
<div class='form-group'>
<label>Designation</label>
<input type='text' placeholder='Designation' name='designation' value='" . $row['designation'] . "'>
</div>
</div>
<div class='col-lg-6 col-sm-12'>
<div class='form-group'>
<label>Department</label>
<select class='select' name='department'>
<option disabled selected>Choose Department</option>
<option value='Admin Department' "; if($row['department'] == "Admin Department") echo "selected"; echo ">Admin Department</option>
<option value='Finance Department' "; if($row['department'] == "Finance Department") echo "selected"; echo ">Finance Department</option>
<option value='Sales & Marketing' "; if($row['department'] == "Sales & Marketing") echo "selected"; echo ">Sales & Marketing</option>
<option value='Technical Department' "; if($row['department'] == "Technical Department") echo "selected"; echo ">Technical Department</option>
<option value='Software Team' "; if($row['department'] == "Software Team") echo "selected"; echo ">Software Team</option>
<option value='Network & Security Department' "; if($row['department'] == "Network & Security Department") echo "selected"; echo ">Network & Security Department</option>
</select>
</div>
</div>
</div>
<div class='col-12'>
<input type='submit' class='btn btn-submit me-2' name='submit' value='Submit'>
<a href='profile.php' class='btn btn-cancel'>Cancel</a>
</div>
</form>
";
?>

</div>
</div>
</div>

</div>
</div>
</div>

<script src="assets/js/jquery-3.6.0.min.js"></script>
<script src="assets/js/feather.min.js"></script>
<script src="assets/js/jquery.slimscroll.min.js"></script>
<script src="assets/js/jquery.dataTables.min.js"></script>
<script src="assets/js/dataTables.bootstrap4.min.js"></script>
<script src="assets/js/bootstrap.bundle.min.js"></script>
<script src="assets/plugins/select2/js/select2.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalert2.all.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalerts.min.js"></script>
<script src="assets/js/script.js"></script>
<script>
document.getElementById('imgInp').onchange = function (event) {
    const [file] = event.target.files;
    if (file) {
        document.getElementById('blah').src = URL.createObjectURL(file);
    }
};
</script>
</body>
</html>
<?php
} else {
    header("location: ./signin.php");
}
?>
