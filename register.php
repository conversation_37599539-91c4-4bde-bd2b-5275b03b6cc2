<?php
require("database.php");
use <PERSON><PERSON><PERSON>ailer\PHPMailer\PHPMailer;
use PHP<PERSON>ailer\PHPMailer\Exception;

if (isset($_SESSION["user"])) {
    header("location: ./index.php");
    exit();
}

function getAccountId($length, $conn) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $accId = '';
    $maxTries = 10;
    $tryCount = 0;

    do {
        $accId = '';
        for ($i = 0; $i < $length; $i++) {
            $accId .= $characters[rand(0, strlen($characters) - 1)];
        }

        $checkQuery = "SELECT COUNT(*) AS count FROM `useracc` WHERE `acc_id` = ?";
        $checkStmt = mysqli_prepare($conn, $checkQuery);
        mysqli_stmt_bind_param($checkStmt, "s", $accId);
        mysqli_stmt_execute($checkStmt);
        mysqli_stmt_bind_result($checkStmt, $count);
        mysqli_stmt_fetch($checkStmt);
        mysqli_stmt_close($checkStmt);

        $tryCount++;
    } while ($count > 0 && $tryCount < $maxTries);

    return $accId;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = $_POST["name"];
    $email = trim($_POST["email"]); // Trim whitespace

    // Check if the email ends with @cllsystems.com
    if (!preg_match('/@cllsystems\.com$/', $email)) {
        echo "<div class='alert alert-danger alert-dismissible fade show text-center' role='alert'>
                Registration is only allowed for office emails.
              </div>";
        error_log("Invalid email domain: $email"); // Log the entire email for debugging
    } else {
        $acc_id = getAccountId(10, $conn);

        $sql = "SELECT * FROM useracc WHERE email = ?";
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, "s", $email);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);

        if (mysqli_num_rows($result) > 0) {
            echo "<div class='alert alert-danger alert-dismissible fade show text-center' role='alert'>
                    Email already exists. Please use a different email.
                  </div>";
        } else {
            // Get role_id for 'User' role
            $roleQuery = "SELECT `role-id` FROM `role-list` WHERE `role` = 'User'";
            $roleResult = mysqli_query($conn, $roleQuery);
            $roleRow = mysqli_fetch_assoc($roleResult);
            $role_id = $roleRow['role-id'];

            // Generate a verification token
            $token = bin2hex(random_bytes(32));
            $sql = "INSERT INTO useracc (acc_id, email, role_id, verification_token, verified) VALUES (?, ?, ?, ?, 0)";
            $stmt = mysqli_prepare($conn, $sql);
            mysqli_stmt_bind_param($stmt, "ssss", $acc_id, $email, $role_id, $token);

            if (mysqli_stmt_execute($stmt)) {
                // Insert into user-list table
                $userListQuery = "INSERT INTO `user-list` (`acc_id`, `email`, `name`, `designation`, `department`, `description`) VALUES (?, ?, ?, '', '', '')";
                $userListStmt = mysqli_prepare($conn, $userListQuery);
                mysqli_stmt_bind_param($userListStmt, "sss", $acc_id, $email, $name);
                mysqli_stmt_execute($userListStmt);

                // Send verification email
                require 'MAILER/vendor/autoload.php';
                $mail = new PHPMailer(true);
                try {
                    $mail->isSMTP();
                    $mail->SMTPOptions = array(
                        'ssl' => array(
                            'verify_peer' => false,
                            'verify_peer_name' => false,
                            'allow_self_signed' => true
                        ));
                    $mail->Host       = 'smtp.office365.com';
                    $mail->SMTPAuth   = true;
                    $mail->Username   = '<EMAIL>';
                    $mail->Password   = '&[i3F6}0hOw6';
                    $mail->Port       = 587;
                    $mail->setFrom('<EMAIL>', 'CLLXWARE');
                    $mail->addAddress($email, $name);
                    $mail->SMTPSecure = 'tls';
                    $mail->isHTML(true);
                    $mail->Subject = "CLLXWARE - Email Verification";
                    $verificationLink = "http://cis.cllsystems.com:9443/staging/verify.php?email=$email&token=$token";
                    $mail->Body = "
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <style>
                            body {
                                font-family: Arial, sans-serif;
                                background-color: #f4f4f4;
                                margin: 0;
                                padding: 0;
                            }
                            .container {
                                max-width: 600px;
                                margin: 0 auto;
                                padding: 20px;
                                background-color: #ffffff;
                                box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
                                border-radius: 10px;
                            }
                            .header {
                                text-align: center;
                                padding: 10px 0;
                                background-color: #004080;
                                border-radius: 10px 10px 0 0;
                            }
                            .header img {
                                max-width: 150px;
                                height: auto;
                            }
                            .content {
                                padding: 20px;
                                color: #333333;
                            }
                            .content p {
                                margin: 0 0 15px;
                                font-size: 14px;
                                line-height: 1.5;
                            }
                            .content a {
                                display: inline-block;
                                padding: 10px 20px;
                                color: #ffffff;
                                background-color: #004085;
                                text-decoration: none;
                                border-radius: 5px;
                                margin-top: 10px;
                                text-align: center;
                                display: inline-block;
                            }
                            .footer {
                                text-align: center;
                                padding: 10px;
                                background-color: #f4f4f4;
                                color: #888888;
                                font-size: 12px;
                                border-bottom-left-radius: 10px;
                                border-bottom-right-radius: 10px;
                            }
                            .footer a {
                                color: #888888;
                                text-decoration: none;
                            }
                        </style>
                    </head>
                    <body>
                        <div class='container'>
                            <div class='header'>
                                <img src='https://www.cllsystems.com/wp-content/uploads/2016/09/cropped-cll-logo.png' alt='Company Logo'>
                            </div>
                            <div class='content'>
                                <p>Dear $name,</p>
                                <p>Thank you for registering with CLLXWARE. To complete your registration, please verify your email address by clicking the link below:</p>
                                <p><a href='$verificationLink'>Verify Email Address</a></p>
                                <p>Thank you,<br>The CLLXWARE Team</p>
                            </div>
                            <div class='footer'>
                                <p>CLL Systems Sdn Bhd | <a href='https://www.cllsystems.com'>www.cllsystems.com</a></p>
                            </div>
                        </div>
                    </body>
                    </html>";

                    $mail->send();
                    echo "<script>alert('Registration successful. Please check your email for verification link.'); window.location='signin.php';</script>";
                } catch (Exception $e) {
                    echo "Message could not be sent. Mailer Error: {$mail->ErrorInfo}";
                }
            } else {
                echo "<div class='alert alert-danger alert-dismissible fade show text-center' role='alert'>
                        Registration failed. Please try again later.
                      </div>";
            }
            mysqli_stmt_close($stmt);
        }
        mysqli_close($conn);
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0">
<meta name="description" content="CLL Warehouse Inventory System">
<meta name="author" content="Powered by CLL Systems Sdn Bhd">
<meta name="robots" content="noindex, nofollow">
<title>CLLXWARE-Register</title>
<link rel='icon' type='image/x-icon' href='https://www.cllsystems.com/wp-content/themes/cllsystems/img/favicon.png'>
<link rel="stylesheet" href="assets/css/bootstrap.min.css">
<link rel="stylesheet" href="assets/plugins/fontawesome/css/fontawesome.min.css">
<link rel="stylesheet" href="assets/plugins/fontawesome/css/all.min.css">
<link rel="stylesheet" href="assets/css/style.css">
</head>
<body class="account-page">
<div class="main-wrapper">
<div class="account-content">
<div class="login-wrapper">
<div class='login-content'>
<div class='login-userset'>
<div class='login-logo'>
<img src='assets/img/logo2.png' alt='img'>
</div>
<div class='login-userheading'>
<h3>Register</h3>
<p>Create your account</p>
</div>
<form action='' class='login-form' method='POST'>
<div class='form-login'>
<label>Name</label>
<div class='form-addons'>
<input type='text' placeholder='Enter your name' id='name' name='name' required>
<img src='assets/img/icons/users1.svg' alt='img'>
</div>
</div>
<div class='form-login'>
<label>Email</label>
<div class='form-addons'>
<input type='email' placeholder='Enter your email address' id='email' name='email' required>
<img src='assets/img/icons/mail.svg' alt='img'>
</div>
</div>
<div class='form-login'>
<input type='submit' value='Register' class='btn btn-login'>
</div>
</form>
<div class='form-login'>
<p>Already have an account? <a href='signin.php'>Sign In</a></p>
<p>Didn't receive the verification email? <a href='resend_verification.php'>Resend Email</a></p>
</div>
</div>
</div>
<div class="login-img">
<img src="assets/img/inventory.png" alt="img">
</div>
</div>
</div>
</div>
<script src="assets/js/jquery-3.6.0.min.js"></script>
<script src="assets/js/feather.min.js"></script>
<script src="assets/js/bootstrap.bundle.min.js"></script>
<script src="assets/js/script.js"></script>
</body>
</html>
