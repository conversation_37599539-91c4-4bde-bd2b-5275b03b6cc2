﻿<?php
require("database.php");
if(isset($_SESSION["user"])){
    $email=$_SESSION["user"];

    if (isset($_POST["submit"])) {
        $request = $_POST["request"];
        $request_remark = $_POST["request_remark"];
        $inventory = $_POST['inventory'];
        $inventory_remark = $_POST['inventory_remark'];
        $return_date = $_POST['return_date'];
        $collection_date = $_POST['collection_date'];
        $request_id = $_GET['request-id'];

        if($request=='1'){
            $email2=$email;
            $current_time2=$return_date;
            date_default_timezone_set('Asia/Kuala_Lumpur');
            $approved_date = date('Y-m-d H:i:s');
            if($inventory=='2'){
            date_default_timezone_set('Asia/Kuala_Lumpur');
            $current_time = date('Y-m-d H:i:s');
            $current_time2=$return_date;
            }elseif($inventory=='3'){
                $current_time= $collection_date;
                date_default_timezone_set('Asia/Kuala_Lumpur');
                $current_time2 = date('Y-m-d H:i:s');
            }
        } elseif($request=='2'){
            $email2=$email;
            $current_time2=$return_date;
            date_default_timezone_set('Asia/Kuala_Lumpur');
            $approved_date = date('Y-m-d H:i:s');
            if($inventory=='1'){
              echo"<script>alert('You already decline the request, no status will be updated'); window.location='request-details.php?request-id=$request_id';</script>";
              return $inventory=='0';
                }elseif($inventory=='2'){
                    echo"<script>alert('You already decline the request, no status will be updated'); window.location='request-details.php?request-id=$request_id';</script>";
                    return $inventory=='0';
                }elseif($inventory=='3'){
                    echo"<script>alert('You already decline the request, no status will be updated'); window.location='request-details.php?request-id=$request_id';</script>";
                    return $inventory=='0';
                }
        }

        $sql = "UPDATE `request-list` 
                SET `approval_status` = ?, 
                    `approval_remark` = ?,
                    `approval_date` = ?,
                    `inventory_status` = ?,
                    `inventory_remark` = ?,
                    `approval_made_by` = ?,
                    `collection_date` = ?,
                    `return_date` = ?
                WHERE `request-id` = ?";
        
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, "ississsss", $request, $request_remark, $approved_date, $inventory, $inventory_remark, $email2, $current_time, $current_time2, $request_id);
        
        if (mysqli_stmt_execute($stmt)) {
                echo "<script>alert('The request has been updated.');</script>";
        } else {
            echo "<script>alert('Error updating request information: " . mysqli_error($conn) . "');</script>";
        }
        
        // Close statement
        mysqli_stmt_close($stmt);
    }
?>
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0">
<meta name="description" content="CLL Warehouse Inventory System">
<meta name="author" content="Powered by CLL Systems Sdn Bhd">
<meta name="robots" content="noindex, nofollow">
<title>CLLXWARE-Request Details</title>

<link rel='icon' type='image/x-icon' href='https://www.cllsystems.com/wp-content/themes/cllsystems/img/favicon.png'>
<link rel="stylesheet" href="assets/css/bootstrap.min.css">

<link rel="stylesheet" href="assets/css/animate.css">

<link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">

<link rel="stylesheet" href="assets/css/dataTables.bootstrap4.min.css">

<link rel="stylesheet" href="assets/plugins/fontawesome/css/fontawesome.min.css">
<link rel="stylesheet" href="assets/plugins/fontawesome/css/all.min.css">

<link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
<div id="global-loader">
<div class="whirly-loader"> </div>
</div>
<div class="main-wrapper">

<?php 
$role_id = $_SESSION["role_id"];
$sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
$result20 = mysqli_query($conn, $sql20);

if ($result20) {
    $row20 = mysqli_fetch_assoc($result20);
    if ($row20) {
        $edit = $row20['request_approval'];
        $all = $row20['request_all'];
        if($edit != '1' and $all !='1'){
        header("location: ./index.php");
       }
    } else {
        echo "<script>alert('Role data not found')</script>";
    }
} else {
    echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
}

include("header.php");?>
</div>

<div class="page-wrapper">
<div class="content">
<div class="page-header">
<div class="page-title">
<h4>Request Details</h4>
<h6>View request details</h6>
</div>
</div>
<?php
if(isset($_GET['request-id'])) {
    $request_id = $_GET['request-id'];
    $sql4 = "SELECT * FROM `request-list` WHERE `request-id` = '$request_id'";
    $result4 = mysqli_query($conn, $sql4);
    if ($result4 && mysqli_num_rows($result4) > 0) {
        $row4 = mysqli_fetch_assoc($result4);
        $inventory_id = $row4['inventory-id'];
        $acc_id = $row4['acc_id'];

        $sql7 = "SELECT SUM(quantity) AS total_quantity FROM `request-list` WHERE `request-id` = '$request_id'";
        $result7 = mysqli_query($conn, $sql7);
        if ($result7) {
            $row7 = mysqli_fetch_assoc($result7);
            $total_quantity = $row7['total_quantity'];
        } else {
            echo "Error: " . mysqli_error($conn);
        }

        $sql2 = "SELECT * FROM `inventory-list` WHERE `inventory-id` = '$inventory_id'";
        $result2 = mysqli_query($conn, $sql2);
        
        if (!$result2) {
            // Query failed, display error message
            echo "Error: " . mysqli_error($conn);
            // Stop execution or handle the error as needed
        } else {
            // Query successful, proceed with fetching data
            $row2 = mysqli_fetch_assoc($result2);
        }
        $category_id=$row2['category-id'];
    

        $sql5 = "SELECT * FROM `category-list` WHERE `category-id` = '$category_id'";
        $result5 = mysqli_query($conn, $sql5);
        
        if (!$result5) {
            // Query failed, display error message
            echo "Error: " . mysqli_error($conn);
            // Stop execution or handle the error as needed
        } else {
            // Query successful, proceed with fetching data
            $row5 = mysqli_fetch_assoc($result5);
        }

        // Query to fetch data from user-list
        $sql3 = "SELECT * FROM `user-list` WHERE `acc_id` = '$acc_id'";
        $result3 = mysqli_query($conn, $sql3);
        if (!$result2) {
            // Query failed, display error message
            echo "Error fetching user data: " . mysqli_error($conn);
            // Stop execution or handle the error as needed
        } else {
            // Query successful, proceed with fetching data
            $row3 = mysqli_fetch_assoc($result3);
        }
echo"
<div class='card'>
<div class='card-body'>
<div class='card-sales-split'>
<h2>Request Detail : {$row4['request-id']}</h2>

<ul>
<ul><p>{$row4['request_date']}</p><ul>
<li><a data-bs-toggle='tooltip' data-bs-placement='top' title='Print'><img src='assets/img/icons/printer.svg' alt='img' id='printButton'></a>
</li>
</ul>
</div>
<div class='invoice-box table-height' style='max-width: 1600px;width:100%;overflow: auto;margin:15px auto;padding: 0;font-size: 14px;line-height: 24px;color: #555;'>
<table cellpadding='0' cellspacing='0' style='width: 100%;line-height: inherit;text-align: left;'>
<tbody><tr class='top'>
<td colspan='6' style='padding: 5px;vertical-align: top;'>
<table style='width: 100%;line-height: inherit;text-align: left;'>
<tbody><tr>
<td style='padding:5px;vertical-align:top;text-align:left;padding-bottom:20px'>
<font style='vertical-align: inherit;margin-bottom:25px;'><font style='vertical-align: inherit;font-size:14px;color:#7367F0;font-weight:600;line-height: 35px; '>Requester Info</font></font><br>
<font style='vertical-align: inherit;'><font style='vertical-align: inherit;font-size: 14px;color:#000;font-weight: 400;'>{$row3['name']}</font></font><br>
<font style='vertical-align: inherit;'><font style='vertical-align: inherit;font-size: 14px;color:#000;font-weight: 400;'> <a href='mailto:{$row3['email']}' >{$row3['email']}</a></font></font><br>
</td>
<td style='padding:5px;vertical-align:top;text-align:left;padding-bottom:20px'>
<font style='vertical-align: inherit;margin-bottom:25px;'><font style='vertical-align: inherit;font-size:14px;color:#7367F0;font-weight:600;line-height: 35px; '>Company Info</font></font><br>
<font style='vertical-align: inherit;'><font style='vertical-align: inherit;font-size: 14px;color:#000;font-weight: 400;'> {$row4['company_name']}</font></font><br>
<font style='vertical-align: inherit;'><font style='vertical-align: inherit;font-size: 14px;color:#000;font-weight: 400;'> {$row4['company_category']}</font></font><br>
</td>
<td style='padding:5px;vertical-align:top;text-align:left;padding-bottom:20px'>
<font style='vertical-align: inherit;margin-bottom:25px;'><font style='vertical-align: inherit;font-size:14px;color:#7367F0;font-weight:600;line-height: 35px; '>Request Info</font></font><br>
<font style='vertical-align: inherit;'><font style='vertical-align: inherit;font-size: 14px;color:#000;font-weight: 400;'> Reference </font></font><br>
<font style='vertical-align: inherit;'><font style='vertical-align: inherit;font-size: 14px;color:#000;font-weight: 400;'> Request Status</font></font><br>
 <font style='vertical-align: inherit;'><font style='vertical-align: inherit;font-size: 14px;color:#000;font-weight: 400;'> Status</font></font><br>
</td>
<td style='padding:5px;vertical-align:top;text-align:right;padding-bottom:20px'>
<font style='vertical-align: inherit;margin-bottom:25px;'><font style='vertical-align: inherit;font-size:14px;color:#7367F0;font-weight:600;line-height: 35px; '>&nbsp;</font></font><br>
<font style='vertical-align: inherit;'><font style='vertical-align: inherit;font-size: 14px;color:#000;font-weight: 400;'>{$row4['request-id']} </font></font><br>";
$request_status=$row4['request_status'];
$approval_status=$row4['approval_status'];
if($request_status=='0'){
    echo"<font style='vertical-align: inherit;'><font style='vertical-align: inherit;font-size: 14px;color:orange;font-weight: 400;'>Borrowed</font></font><br>";
}else{
    echo"<font style='vertical-align: inherit;'><font style='vertical-align: inherit;font-size: 14px;color:red;font-weight: 400;'>Permanent</font></font><br>";
}

if($approval_status=='0'){
    echo"<font style='vertical-align: inherit;'><font style='vertical-align: inherit;font-size: 14px;color:orange;font-weight: 400;'> Pending for approval</font></font><br>";
}elseif($approval_status=='1'){
    echo"<font style='vertical-align: inherit;'><font style='vertical-align: inherit;font-size: 14px;color:green;font-weight: 400;'> Approval Approved</font></font><br>";
}elseif($approval_status=='2'){
    echo"<font style='vertical-align: inherit;'><font style='vertical-align: inherit;font-size: 14px;color:red;font-weight: 400;'> Approval Declined</font></font><br>";
}
echo"
</td>
</tr>
</tbody></table>
</td>
</tr>";
$sql6 = "SELECT rl.*, il.`product-name`, il.`part-number`, il.`serial-number`, il.`location-id`, b.`brand-name`, c.`category-name` FROM `request-list` rl LEFT JOIN `inventory-list` il ON rl.`inventory-id` = il.`inventory-id` LEFT JOIN `brand-list` b ON il.`brand-id` = b.`brand-id` LEFT JOIN `category-list` c ON il.`category-id` = c.`category-id` WHERE rl.`request-id` = '$request_id'";
$result6 = mysqli_query($conn, $sql6);

echo"
<tr class='heading ' style='background: #F3F2F7;'>
<td style='padding: 5px;vertical-align: middle;font-weight: 600;color: #5E5873;font-size: 14px;padding: 10px; '>
Product Model
</td>
<td style='padding: 5px;vertical-align: middle;font-weight: 600;color: #5E5873;font-size: 14px;padding: 10px; '>
Product Part Number
</td>
<td style='padding: 5px;vertical-align: middle;font-weight: 600;color: #5E5873;font-size: 14px;padding: 10px; '>
Product Serial Number
</td>
<td style='padding: 5px;vertical-align: middle;font-weight: 600;color: #5E5873;font-size: 14px;padding: 10px; '>
QTY
</td>
<td style='padding: 5px;vertical-align: middle;font-weight: 600;color: #5E5873;font-size: 14px;padding: 10px; '>
Brand
</td>
<td style='padding: 5px;vertical-align: middle;font-weight: 600;color: #5E5873;font-size: 14px;padding: 10px; '>
Category
</td>
<td style='padding: 5px;vertical-align: middle;font-weight: 600;color: #5E5873;font-size: 14px;padding: 10px; '>
Location
</td>
<td style='padding: 5px;vertical-align: middle;font-weight: 600;color: #5E5873;font-size: 14px;padding: 10px; '>
Remark
</td>
</tr>";
if($result6){
    while ($row6 = mysqli_fetch_assoc($result6)) {
echo"
<tr class='details' style='border-bottom:1px solid #E9ECEF ;'>
<td style='padding: 10px;vertical-align: top; display: flex;align-items: center;'>
{$row6['product-name']}
</td>
<td style='padding: 10px;vertical-align: top; '>
{$row6['part-number']}
</td>
<td style='padding: 10px;vertical-align: top; '>
{$row6['serial-number']}
</td>
<td style='padding: 10px;vertical-align: top; '>
{$row6['quantity']}
</td>
<td style='padding: 10px;vertical-align: top; '>
{$row6['brand-name']}
</td>
<td style='padding: 10px;vertical-align: top; '>
{$row6['category-name']}
</td>
<td style='padding: 10px;vertical-align: top; '>
Room {$row6['location-id']}
</td>
<td style='padding: 10px;vertical-align: top; '>
{$row6['request_remark']}
</td>
</tr>";
if (isset($_POST["submit"])) {
    $sql7 = "SELECT il.* FROM `inventory-list` il LEFT JOIN `request-list` rl ON il.`inventory-id` = rl.`inventory-id` WHERE rl.`request-id` = '$request_id' AND il.`inventory-id` = '{$row6['inventory-id']}'";
    $result7 = mysqli_query($conn, $sql7);
    if($result7){
        $row7 = mysqli_fetch_assoc($result7);


        $current_qty=$row7['quantity'];
        $reserve_qty=$row7['reserve_qty'];
        $current_inventory_id=$row7['inventory-id'];
        $approval_status2=$row6['approval_status'];
        $request_status2 = $row6['inventory_status'];
        $request_qty = $row6['quantity'];

        if ($approval_status2 == '1' AND $request_status2 == '0') { // approve and available
            if ($request_qty > $current_qty) {
                $latest_qty = $current_qty;
                $new_reserve_qty = $reserve_qty;
                $request_status2 = '0'; // Set status as available
        
                // Update approval_status to '2' (Declined)
                $sql = "UPDATE `request-list` SET `approval_status` = '2' WHERE `request-id` = ?";
                $stmt = mysqli_prepare($conn, $sql);
                mysqli_stmt_bind_param($stmt, "s", $request_id); 
        
                if (mysqli_stmt_execute($stmt)) {
                    echo "<script>alert('Insufficient stock quantity for {$row6['product-name']}. Request has been declined.'); window.location='request-details.php?request-id=$request_id';</script>";
                } else {
                    echo "<script>alert('Error updating approval status: " . mysqli_error($conn) . "');</script>";
                }
        
                mysqli_stmt_close($stmt); 
                break;
            } else {
                $latest_qty = $current_qty - $request_qty;
                $new_reserve_qty = $reserve_qty + $request_qty;
                $request_status2 = '1'; // Set status as pending collection
            }
        } 
        elseif ($approval_status2 == '1' && $request_status2 == '1') { // approved and pending collection
            $latest_qty = $current_qty - $request_qty;
            $new_reserve_qty = $reserve_qty + $request_qty;
        
            // Check if request quantity is greater than current quantity
            if($request_qty > $current_qty){
                $latest_qty = $current_qty;
                $new_reserve_qty = $reserve_qty;
                $request_status2 = '0'; // Set status as insufficient stock
        
                // Update approval status to '2' (Declined)
                $sql = "UPDATE `request-list` SET `approval_status` = '2' WHERE `request-id` = ?";
                $stmt = mysqli_prepare($conn, $sql);
                mysqli_stmt_bind_param($stmt, "s", $request_id); 
        
                if (mysqli_stmt_execute($stmt)) {
                    // Update inventory status to 0
                    $update_inventory_sql = "UPDATE `request-list` SET `inventory_status` = '0' WHERE `request-id` = ?";
                    $stmt_inventory = mysqli_prepare($conn, $update_inventory_sql);
                    mysqli_stmt_bind_param($stmt_inventory, "s", $request_id); 
        
                    if (mysqli_stmt_execute($stmt_inventory)) {
                        echo "<script>alert('Insufficient stock quantity for {$row6['product-name']}. Request has been declined.'); window.location='request-details.php?request-id=$request_id';</script>";
                    } else {
                        echo "<script>alert('Error updating inventory status: " . mysqli_error($conn) . "');</script>";
                    }
        
                    mysqli_stmt_close($stmt_inventory);
                } else {
                    echo "<script>alert('Error updating approval status: " . mysqli_error($conn) . "');</script>";
                }
        
                mysqli_stmt_close($stmt); 
                break; // Exit the loop or return, depending on the context
            } else {
                $request_status2 = '1'; // Set status as pending collection
            }
        }
        
        
        elseif ($approval_status2 == '1' && $request_status2 == '2') { // approved and taken
            $latest_qty = $current_qty;
            $new_reserve_qty = $reserve_qty - $request_qty;
            $request_status2 = '2'; // Set status as taken
        } 
        elseif($approval_status2 == '1' AND $request_status2 == '3'){ // approve and return
            $latest_qty = $current_qty + $request_qty;
            $new_reserve_qty = $reserve_qty;
        } 
        elseif($approval_status2 == '2' AND $request_status2 == '0'){ // decline and available
            $latest_qty = $current_qty;
            $new_reserve_qty = $reserve_qty;
            $request_status2 = '0';
        } 
        elseif($approval_status2 == '2' AND $request_status2 == '1'){ // decline and pending collection
            $latest_qty = $current_qty + $reserve_qty;
            $new_reserve_qty = 0;
            $request_status2 = '0';
        } 
        elseif($approval_status2 == '2' AND $request_status2 == '2'){ // decline and taken
            $latest_qty = $current_qty + $reserve_qty;
            $new_reserve_qty = 0;
            $request_status2 = '0';
        } 
        elseif($approval_status2 == '2' AND $request_status2 == '3'){ // decline and return
            $latest_qty = $current_qty + $request_qty;
            $new_reserve_qty = 0;
        } 
        else{
            echo "Invalid approval or request status.";
        }
        $sql = "UPDATE `inventory-list` SET `quantity` = ?, `reserve_qty` = ? WHERE `inventory-id` = ?";

        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, "iis", $latest_qty, $new_reserve_qty, $current_inventory_id);

        if (mysqli_stmt_execute($stmt)) {
        $update_role_sql = "UPDATE `request-list` SET `inventory_status` = ? WHERE `request-id` = ?";
            $stmt_role = mysqli_prepare($conn, $update_role_sql);
            mysqli_stmt_bind_param($stmt_role, "is", $request_status2, $request_id);
            if (mysqli_stmt_execute($stmt_role)) {
       
            echo "<script>window.location='request-details.php?request-id=$request_id';</script>";
            }else {
                echo "<script>alert('Error updating request: " . mysqli_error($conn) . "'); window.location='requestlist.php';</script>";
            }
            
            mysqli_stmt_close($stmt_role);
        } else {
            echo "Error updating inventory: " . mysqli_error($conn);
        }
        // Close statement
        mysqli_stmt_close($stmt);




} else {
    echo "<tr><td colspan='8'>Error fetching product details: " . mysqli_error($conn) . "</td></tr>";
}
}
}
} else {
    echo "<tr><td colspan='8'>Error fetching product details: " . mysqli_error($conn) . "</td></tr>";
}
echo"

</tbody></table>
</div>
<form action='#' method='post' id='myForm'>
<div class='row'>
<div class='col-lg-3 col-sm-6 col-12'>
<div class='form-group'>
<label>Request Status</label>
<select class='select' name='request' required>
<option value='' selected disabled>Choose Status</option>
<option value='1' "; if($row4['approval_status'] == "1") echo "selected"; echo">Approved</option>
<option value='2' "; if($row4['approval_status'] == "2") echo "selected"; echo">Declined</option>
</select>
</div>
</div>
<div class='col-lg-3 col-sm-6 col-12'>
<div class='form-group'>
<label>Remark</label>
<input type='text' name='request_remark' value='{$row4['approval_remark']}'>
<input type='hidden' name='return_date' value='{$row4['return_date']}'>
<input type='hidden' name='collection_date' value='{$row4['collection_date']}'>
</div>
</div>
<div class='row'>
<div class='col-lg-3 col-sm-6 col-12'>
<div class='form-group'>
<label>Inventory Status</label>";?>
<select class='select' name='inventory' id="inventoryStatus" required>
  <option value='' <?php if($row4['inventory_status'] == "") echo "selected"; ?> disabled>Choose Status</option>
  <option value='0' <?php if($row4['inventory_status'] == "0") echo "selected"; ?> <?php if($row4['inventory_status'] != "0" && $row4['inventory_status'] != "") echo "disabled"; ?>>Available</option>
  <option value='1' <?php if($row4['inventory_status'] == "1") echo "selected"; ?> <?php if($row4['inventory_status'] != "1" && $row4['inventory_status'] != "0") echo "disabled"; ?>>Pending collection</option>
  <option value='2' <?php if($row4['inventory_status'] == "2") echo "selected"; ?> <?php if($row4['inventory_status'] != "2" && $row4['inventory_status'] != "1") echo "disabled"; ?>>Taken</option>
  <option value='3' <?php if($row4['inventory_status'] == "3") echo "selected"; ?> <?php if($row4['inventory_status'] != "3" && $row4['inventory_status'] != "2") echo "disabled"; ?>>Returned</option>
</select>
<script>
    function checkInventoryStatus() {
        // Get the current selection
        var currentSelection = document.getElementById('inventoryStatus').value; // Assuming 'inventoryStatus' is the ID of your select input

        // Simulated database retrieval mechanism
        var databaseInventoryStatus = '<?php echo $row4['inventory_status']; ?>';

        if (databaseInventoryStatus === '0') {
            // Allow form submission if the inventory status is '1'
            return true;
        } else if (currentSelection === databaseInventoryStatus) {
            // Prevent form submission
            alert('Cannot submit form. Inventory status must choose the next selection.');
            return false; // Stop form submission
        } else {
            // Allow form submission
            return true;
        }
    }

    // Attach this function to the form's submit event
    document.getElementById('myForm').addEventListener('submit', function(event) {
        // Check the inventory status before submitting the form
        if (!checkInventoryStatus()) {
            event.preventDefault(); // Prevent form submission
        }
    });
</script>






<?php
echo"
</div>
</div>
<div class='col-lg-3 col-sm-6 col-12'>
<div class='form-group'>
<label>Remark</label>
<input type='text' name='inventory_remark' value='{$row4['inventory_remark']}'>
</div>
</div>
<div class='row'>
<div class='col-lg-6 '>
<div class='total-order w-100 max-widthauto m-auto mb-4'>
<ul>
<li>
<h4>Total Inventory Quantity Taken</h4>
<h5>$total_quantity</h5>
</li>
<li>
<h4>Approval Made By</h4>
<h5>{$row4['approval_made_by']}</h5>
</li>
</ul>
</div>
</div>
<div class='col-lg-6 '>
<div class='total-order w-100 max-widthauto m-auto mb-4'>
<ul>
<li class='total'>
<h4>Collection Date</h4>
<p>&nbsp;&nbsp;{$row4['collection_date']}</p>
</li>
<li class='total'>
<h4>Returning Date</h4>
<p>&nbsp;&nbsp;{$row4['return_date']}</p>
</li>
</ul>
</div>
</div>
</div>
<div class='col-lg-12'>
<input type='submit'  class='btn btn-submit me-2' name='submit' value='Submit'>
<a href='requestlist.php' class='btn btn-cancel'>Cancel</a>
</div>
</form>
";
} else {
    // Handle case where no inventory item with the provided inventory-id is found
    echo "<p>No request found with the provided ID.</p>";
  }

} else {
    // Handle case where inventory-id is not provided in the URL
    echo "<p>Request ID is not provided in the URL.</p>";
    }
?>
</div>
</div>
</div>
</div>
</div>
</div>



<script>
document.getElementById("printButton").addEventListener("click", function() {
    // Create a temporary copy of the content
    const originalContent = document.querySelector(".invoice-box");
    const printContent = originalContent.cloneNode(true);

    // Create a temporary printable container
    const printContainer = document.createElement("div");
    printContainer.style.display = "none";
    printContainer.appendChild(printContent);
    document.body.appendChild(printContainer);

    // Trigger print and clean up
    window.print();
    document.body.removeChild(printContainer);
});

</script>

<script data-cfasync="false" src="../../cdn-cgi/scripts/5c5dd728/cloudflare-static/email-decode.min.js"></script><script src="assets/js/jquery-3.6.0.min.js"></script>

<script src="assets/js/feather.min.js"></script>

<script src="assets/js/jquery.slimscroll.min.js"></script>

<script src="assets/js/jquery.dataTables.min.js"></script>
<script src="assets/js/dataTables.bootstrap4.min.js"></script>

<script src="assets/js/bootstrap.bundle.min.js"></script>

<script src="assets/plugins/select2/js/select2.min.js"></script>

<script src="assets/plugins/sweetalert/sweetalert2.all.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalerts.min.js"></script>

<script src="assets/js/script.js"></script>
</body>
</html>
<?php
}else{
    header("location: ./signin.php");
}
?>