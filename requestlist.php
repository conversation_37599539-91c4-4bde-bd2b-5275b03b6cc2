<?php
require("database.php");
if(isset($_SESSION["user"])){
    $email=$_SESSION["user"];
?>
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0">
<meta name="description" content="CLL Warehouse Inventory System">
<meta name="author" content="Powered by CLL Systems Sdn Bhd">
<meta name="robots" content="noindex, nofollow">
<title>CLLXWARE-Request List</title>
<link rel='icon' type='image/x-icon' href='https://www.cllsystems.com/wp-content/themes/cllsystems/img/favicon.png'>

<link rel="stylesheet" href="assets/css/bootstrap.min.css">

<link rel="stylesheet" href="assets/css/animate.css">

<link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">

<link rel="stylesheet" href="assets/css/bootstrap-datetimepicker.min.css">

<link rel="stylesheet" href="assets/css/dataTables.bootstrap4.min.css">

<link rel="stylesheet" href="assets/plugins/fontawesome/css/fontawesome.min.css">
<link rel="stylesheet" href="assets/plugins/fontawesome/css/all.min.css">

<link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
<div id="global-loader">
<div class="whirly-loader"> </div>
</div>

<div class="main-wrapper">
<?php 
$role_id = $_SESSION["role_id"];
$sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
$result20 = mysqli_query($conn, $sql20);

if ($result20) {
    $row20 = mysqli_fetch_assoc($result20);
    if ($row20) {
       $view = $row20['request_view'];
       $all = $row20['request_all'];
       if($view != '1' and $all !='1'){
        header("location: ./index.php");
       }
    } else {
        echo "<script>alert('Role data not found')</script>";
    }
} else {
    echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
}
include("header.php");?>
</div>

<div class="page-wrapper">
<div class="content">
<div class="page-header">
<div class="page-title">
<h4>Request List</h4>
<h6>Manage requests</h6>
</div>
<?php
$role_id = $_SESSION["role_id"];
$sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
$result20 = mysqli_query($conn, $sql20);

if ($result20) {
    $row20 = mysqli_fetch_assoc($result20);
    if ($row20) {
       $create = $row20['request_create'];
       $all = $row20['request_all'];
       if($create != '1' and $all !='1'){
       echo"";
       }else{
        echo"
        <div class='page-btn'>
    <a href='add-request.php' class='btn btn-added'><img src='assets/img/icons/plus.svg' alt='img' class='me-1'>Add Request</a>
    </div>
        ";
       }
    } else {
        echo "<script>alert('Role data not found')</script>";
    }
} else {
    echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
}

?>

</div>

<div class="card">
<div class="card-body">
<div class="table-top">
<div class="search-set">
<div class="search-input">
<a class="btn btn-searchset"><img src="assets/img/icons/search-white.svg" alt="img"></a>
</div>
</div>
<div class="wordset">
<ul>
<form action='csv_request.php' method='post'>
<li>
<a data-bs-toggle="tooltip" data-bs-placement="top" title="CSV" href="#" onclick="submitForm();">
                <img src="assets/img/icons/excel.svg" alt="CSV">
            </a>
</li>
</form>
<li>
<a data-bs-toggle="tooltip" data-bs-placement="top" title="Print"><img src="assets/img/icons/printer.svg" alt="img" id="printButton"></a>
</li>
</ul>
</div>
</div>


<div class="table-responsive">
<table class="table  datanew">
<thead>
<tr>
<th>
No
</th>
<th>Date</th>
<th>Quantity</th>
<th>Request Status</th>
<th>Approval Status</th>
<th>Customer</th>
<th>Created By</th>
<?php
            // Check user permissions once for the entire table
            $role_id = $_SESSION["role_id"];
            $sql_perm = "SELECT * FROM `role-list` WHERE `role-id` = ?";
            $stmt_perm = mysqli_prepare($conn, $sql_perm);
            mysqli_stmt_bind_param($stmt_perm, "s", $role_id);
            mysqli_stmt_execute($stmt_perm);
            $result_perm = mysqli_stmt_get_result($stmt_perm);

            $show_actions = false;
            if ($row_perm = mysqli_fetch_assoc($result_perm)) {
                $edit = $row_perm['request_approval'];
                $all = $row_perm['request_all'];
                $show_actions = ($edit == '1' || $all == '1');
            }
            mysqli_stmt_close($stmt_perm);

            // Only show Action column if user has permissions
            if ($show_actions) {
                echo "<th class='text-center'>Action</th>";
            }
            ?>
</tr>
</thead>
<tbody>
<?php
$sql = "SELECT * FROM `request-list` WHERE `inventory_status` != '3' GROUP BY `request-id`, `acc_id` ORDER BY `id` DESC";
$result = mysqli_query($conn, $sql);

if ($result && mysqli_num_rows($result) > 0) {
    $counter = 1;
    while ($row = mysqli_fetch_assoc($result)) {
        $inventory_id = $row['inventory-id'];
        $acc_id = $row['acc_id'];
        
        $sql2 = "SELECT * FROM `inventory-list` WHERE `inventory-id` = '$inventory_id'";
        $result2 = mysqli_query($conn, $sql2);
        
        if (!$result2) {
            // Query failed, display error message
            echo "Error: " . mysqli_error($conn);
            // Stop execution or handle the error as needed
        } else {
            // Query successful, proceed with fetching data
            $row2 = mysqli_fetch_assoc($result2);
        }
        

        // Query to fetch data from user-list
        $sql3 = "SELECT * FROM `user-list` WHERE `acc_id` = '$acc_id'";
        $result3 = mysqli_query($conn, $sql3);
        if ($result3 && mysqli_num_rows($result3) > 0) {
            $row3 = mysqli_fetch_assoc($result3);
        } else {
            // Handle error if query fails or returns no rows
            echo "Error fetching user data: " . mysqli_error($conn);
            continue; // Skip to next iteration of loop
        }
        $request_id=$row['request-id'];
        $sql7 = "SELECT SUM(quantity) AS total_quantity FROM `request-list` WHERE `request-id` = '$request_id'";
        $result7 = mysqli_query($conn, $sql7);
        if ($result7) {
            $row7 = mysqli_fetch_assoc($result7);
            $total_quantity = $row7['total_quantity'];
        } else {
            echo "Error: " . mysqli_error($conn);
        }

        // Output table row
        echo "
        <tr>
            <td>$counter</td>
            <td>" . $row['request_date'] . "</td>
            <td>$total_quantity</td>";

        // Output request status
        $request_status = $row['request_status'];
        if ($request_status == '0') {
            echo "<td><span class='badges bg-lightyellow'>Borrowed</span></td>";
        } else {
            echo "<td><span class='badges bg-lightred'>Permanent</span></td>";
        }

        // Output approval status
        $approval_status = $row['approval_status'];
        if ($approval_status == '0') {
            echo "<td><span class='badges bg-lightyellow'>Pending</span></td>";
        } elseif ($approval_status == '1') {
            echo "<td><span class='badges bg-lightgreen'>Approved</span></td>";
        } else {
            echo "<td><span class='badges bg-lightred'>Declined</span></td>";
        }

        // Output remaining columns
        echo "
            <td>" . htmlspecialchars($row['company_name']) . "</td>
            <td>" . htmlspecialchars($row3['name']) . "</td>";

            // Use the same $show_actions variable from header
            if ($show_actions) {
                echo "
                <td class='text-center'>
                    <a class='action-set' href='javascript:void(0);' data-bs-toggle='dropdown' aria-expanded='true'>
                        <i class='fa fa-ellipsis-v' aria-hidden='true'></i>
                    </a>
                    <ul class='dropdown-menu'>
                        <li>
                            <a href='request-details.php?request-id=" . htmlspecialchars($row['request-id']) . "' class='dropdown-item'>
                                <img src='assets/img/icons/eye1.svg' class='me-2' alt='img'>Request Detail
                            </a>
                        </li>
                    </ul>
                </td>";
            }
            
            echo"
        </tr>";

        $counter++;
    }
} else {
    // Add fallback row when no data is found
    $colspan = $show_actions ? 8 : 7; // 7 base columns + 1 optional Action column
    echo "<tr><td colspan='{$colspan}' class='text-center'>No requests found.</td></tr>";
}
?>


</tbody>
</table>
</div>
</div>
</div>

</div>
</div>
</div>

<script>
  document.getElementById("printButton").addEventListener("click", function() {
    // Create a temporary copy of the table
    const originalTable = document.querySelector(".datanew");
    const printTable = originalTable.cloneNode(true);

    // Create a temporary printable container
    const printContainer = document.createElement("div");
    printContainer.style.display = "none";
    printContainer.appendChild(printTable);
    document.body.appendChild(printContainer);

    // Set print styles for table and landscape orientation
    printTable.style.width = "100%";
    printTable.style.border = "1px solid black"; // Optional for visual clarity
    const stylesheet = document.createElement("style");
    stylesheet.textContent = "@page { size: landscape; }";
    printContainer.appendChild(stylesheet);

    // Trigger print and clean up
    window.print();
    document.body.removeChild(printContainer);
  });
</script>

<script>
    function submitForm() {
        // Programmatically submit the form when the link is clicked
        document.querySelector("form").submit();
    }
</script>

<script src="assets/js/jquery-3.6.0.min.js"></script>

<script src="assets/js/feather.min.js"></script>

<script src="assets/js/jquery.slimscroll.min.js"></script>

<script src="assets/js/jquery.dataTables.min.js"></script>
<script src="assets/js/dataTables.bootstrap4.min.js"></script>

<script src="assets/js/bootstrap.bundle.min.js"></script>

<script src="assets/plugins/select2/js/select2.min.js"></script>

<script src="assets/js/moment.min.js"></script>
<script src="assets/js/bootstrap-datetimepicker.min.js"></script>

<script src="assets/plugins/sweetalert/sweetalert2.all.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalerts.min.js"></script>

<script src="assets/js/script.js"></script>

<script>
$(document).ready(function() {
    // Additional safety check for request table
    if ($('.datanew').length > 0) {
        try {
            // Validate table structure before initializing DataTables
            var table = $('.datanew')[0];
            var headerCells = table.querySelector('thead tr').cells.length;
            var bodyRows = table.querySelectorAll('tbody tr');

            var isValid = true;
            bodyRows.forEach(function(row, index) {
                if (row.cells.length !== headerCells) {
                    console.error('Row ' + index + ' has ' + row.cells.length + ' cells, but header has ' + headerCells + ' cells');
                    isValid = false;
                }
            });

            if (isValid) {
                console.log('Request table structure is valid. Header: ' + headerCells + ' columns');
            } else {
                console.error('Request table structure validation failed');
            }
        } catch (e) {
            console.error('Request table validation error:', e);
        }
    }
});
</script>

</body>
</html>
<?php
}else{
    header("location: ./signin.php");
}
?>