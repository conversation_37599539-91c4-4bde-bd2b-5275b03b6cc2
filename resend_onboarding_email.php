<?php
require('database.php');
require_once 'MAILER/vendor/autoload.php';
use PHPMailer\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\Exception;

if (!isset($_SESSION['user'])) { http_response_code(401); echo 'Unauthorized'; exit; }

$action = $_POST['action'] ?? '';
$onboarding_id = $_POST['onboarding_id'] ?? '';

if ($action==='' || $onboarding_id===''){ http_response_code(400); echo 'Bad request'; exit; }

// Load record with related names
$stmt = mysqli_prepare($conn, "
    SELECT ol.*,
           dl.department_name,
           ml.manager_name,
           ofl.office_name as office_name_actual
    FROM `onboarding_list` ol
    LEFT JOIN `department_list` dl ON ol.department_id = dl.department_id
    LEFT JOIN `manager_list` ml ON ol.manager_id = ml.manager_id
    LEFT JOIN `office_list` ofl ON ol.office_id = ofl.office_id
    WHERE ol.onboarding_id = ?
");
mysqli_stmt_bind_param($stmt, 's', $onboarding_id);
mysqli_stmt_execute($stmt);
$res = mysqli_stmt_get_result($stmt);
$rec = mysqli_fetch_assoc($res);
mysqli_stmt_close($stmt);
if(!$rec){ http_response_code(404); echo 'Not found'; exit; }

function mailer(){
  $m = new PHPMailer(true);
  $m->isSMTP();
  $m->Host='smtp.office365.com';
  $m->SMTPAuth=true;
  $m->Username='<EMAIL>';
  $m->Password='&[i3F6}0hOw6';
  $m->SMTPSecure='tls';
  $m->Port=587;
  $m->setFrom('<EMAIL>','CLLXWARE');
  return $m;
}

if ($action === 'company') {
  try {
    // Ensure setup_token column exists
    $result = mysqli_query($conn, "SHOW COLUMNS FROM `onboarding_list` LIKE 'setup_token'");
    if (mysqli_num_rows($result) == 0) {
        mysqli_query($conn, "ALTER TABLE `onboarding_list` ADD COLUMN `setup_token` varchar(255) NULL");
    }

    // Get CTO email configuration
    $cto_config = mysqli_query($conn, "SELECT * FROM `onboarding_email_config` WHERE `config_type` = 'cto_request'");
    $cto_data = mysqli_fetch_assoc($cto_config);

    $cto_emails = $cto_data ? array_filter(array_map('trim', explode("\n", $cto_data['email_addresses']))) : ['<EMAIL>'];
    $email_subject = $cto_data['email_subject'] ?? 'New Employee Email Setup Required';
    $email_subject = str_replace('{employee_name}', $rec['full_name'], $email_subject);
    $email_template = $cto_data['email_template'] ?? 'Please use the link below to set company email and password for: {employee_name}\n\n{setup_link}';

    // regenerate token and send link
    $token = bin2hex(random_bytes(16));
    $upd = mysqli_prepare($conn, "UPDATE `onboarding_list` SET `setup_token`=? WHERE `onboarding_id`=?");
    mysqli_stmt_bind_param($upd, 'ss', $token, $onboarding_id);
    if (!mysqli_stmt_execute($upd)) {
      throw new Exception('Failed to update token: ' . mysqli_error($conn));
    }
    mysqli_stmt_close($upd);

    $link = (isset($_SERVER['HTTP_HOST']) ? ( (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://') . $_SERVER['HTTP_HOST'] . dirname($_SERVER['PHP_SELF']) ) : 'https://cis.cllsystems.com:9443/staging');
    $link = rtrim($link,'/')."/setup_company_account.php?onboarding_id=".urlencode($onboarding_id)."&token=".urlencode($token);

    // Replace template variables with actual names from related tables
    $email_body = str_replace([
      '{employee_name}', '{employee_id}', '{department}', '{job_title}',
      '{onboarding_date}', '{personal_email}', '{phone_number}',
      '{reporting_manager}', '{office_name}', '{setup_link}'
    ], [
      $rec['full_name'], $rec['employee_id'], $rec['department_name'] ?? '', $rec['job_title'],
      $rec['onboarding_date'], $rec['personal_email'], $rec['phone_number'],
      $rec['manager_name'] ?? '', $rec['office_name_actual'] ?? '', $link
    ], $email_template);

    $mail = mailer();
    foreach($cto_emails as $email) {
      $mail->addAddress($email);
    }
    $mail->isHTML(true);
    $mail->Subject = $email_subject;
    $mail->Body = nl2br(htmlspecialchars($email_body));
    $mail->send();
    echo 'OK';
  } catch (Exception $e) {
    http_response_code(500);
    echo 'Email error: ' . $e->getMessage();
  }
  exit;
}

if ($action === 'laptop') {
  try {
    // Get IT email configuration
    $it_primary_config = mysqli_query($conn, "SELECT * FROM `onboarding_email_config` WHERE `config_type` = 'it_primary'");
    $it_backup_config = mysqli_query($conn, "SELECT * FROM `onboarding_email_config` WHERE `config_type` = 'it_backup'");

    $primary_data = mysqli_fetch_assoc($it_primary_config);
    $backup_data = mysqli_fetch_assoc($it_backup_config);

    $primary_emails = $primary_data ? array_filter(array_map('trim', explode("\n", $primary_data['email_addresses']))) : ['<EMAIL>'];
    $backup_emails = $backup_data ? array_filter(array_map('trim', explode("\n", $backup_data['email_addresses']))) : [];
    $email_subject = $primary_data['email_subject'] ?? 'Laptop Setup Required for New Employee';
    $email_subject = str_replace('{employee_name}', $rec['full_name'], $email_subject);
    $email_template = $primary_data['email_template'] ?? 'A new employee requires laptop setup:\n\nEmployee: {employee_name}\nEmployee ID: {employee_id}\nDepartment: {department}\n\nWorkflow Link: {workflow_link}';

    if(empty($primary_emails)){ echo 'No primary recipients configured'; exit; }

    $workflow_link = (isset($_SERVER['HTTP_HOST']) ? ( (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://') . $_SERVER['HTTP_HOST'] . dirname($_SERVER['PHP_SELF']) ) : 'https://cis.cllsystems.com:9443/staging');
    $workflow_link = rtrim($workflow_link,'/')."/onboarding_workflow.php?id=".urlencode($rec['onboarding_id']);

    // Replace template variables with actual names from related tables
    $email_body = str_replace([
      '{employee_name}', '{employee_id}', '{department}', '{job_title}',
      '{onboarding_date}', '{personal_email}', '{phone_number}',
      '{reporting_manager}', '{office_name}', '{workflow_link}'
    ], [
      $rec['full_name'], $rec['employee_id'], $rec['department_name'] ?? '', $rec['job_title'],
      $rec['onboarding_date'], $rec['personal_email'], $rec['phone_number'],
      $rec['manager_name'] ?? '', $rec['office_name_actual'] ?? '', $workflow_link
    ], $email_template);

    $mail = mailer();

    // Add primary recipients
    foreach($primary_emails as $email){
      $mail->addAddress($email);
    }

    // Add backup recipients as CC
    foreach($backup_emails as $email){
      $mail->addCC($email);
    }

    $mail->isHTML(true);
    $mail->Subject = $email_subject;
    $mail->Body = nl2br(htmlspecialchars($email_body));

    $mail->send();
    echo 'OK';
  } catch (Exception $e) {
    http_response_code(500);
    echo 'Email error: ' . $e->getMessage();
  }
  exit;
}

echo 'Unknown action';
?>

