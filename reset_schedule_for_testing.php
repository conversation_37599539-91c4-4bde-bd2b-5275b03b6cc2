<?php
/**
 * Reset Email Schedule for Testing
 * This script updates the email schedule times to current time for immediate testing
 */

require_once __DIR__ . '/database.php';

// Set timezone
date_default_timezone_set('Asia/Kuala_Lumpur');

// Set to next minute for immediate testing
$next_minute = date('H:i', strtotime('+1 minute'));
$current_time = $next_minute;
$current_time_with_seconds = $next_minute . ':00';

echo "=== Reset Email Schedule for Testing ===\n";
echo "Current Time: " . date('Y-m-d H:i:s') . " (Malaysia Time)\n\n";

// Update onboarding schedule to current time
$update_onboarding = "UPDATE `onboarding_cron_config` SET `cron_time` = ? WHERE `config_type` = 'cron_reminder'";
$stmt1 = mysqli_prepare($conn, $update_onboarding);
mysqli_stmt_bind_param($stmt1, 's', $current_time);
$result1 = mysqli_stmt_execute($stmt1);

if ($result1) {
    echo "✅ Updated onboarding schedule to: $current_time\n";
} else {
    echo "❌ Failed to update onboarding schedule\n";
}
mysqli_stmt_close($stmt1);

// Update IT support schedule to current time
$update_it_support = "UPDATE `onboarding_email_config` SET `cron_time` = ? WHERE `config_type` = 'it_support'";
$stmt2 = mysqli_prepare($conn, $update_it_support);
mysqli_stmt_bind_param($stmt2, 's', $current_time_with_seconds);
$result2 = mysqli_stmt_execute($stmt2);

if ($result2) {
    echo "✅ Updated IT support schedule to: $current_time_with_seconds\n";
} else {
    echo "❌ Failed to update IT support schedule\n";
}
mysqli_stmt_close($stmt2);

echo "\n=== Current Configuration ===\n";

// Show current configuration
$check_onboarding = mysqli_query($conn, "SELECT cron_time, is_enabled FROM `onboarding_cron_config` WHERE `config_type` = 'cron_reminder'");
if ($row = mysqli_fetch_assoc($check_onboarding)) {
    $status = $row['is_enabled'] ? 'Enabled' : 'Disabled';
    echo "Onboarding: {$row['cron_time']} ($status)\n";
}

$check_it = mysqli_query($conn, "SELECT cron_time FROM `onboarding_email_config` WHERE `config_type` = 'it_support'");
if ($row = mysqli_fetch_assoc($check_it)) {
    echo "IT Support: {$row['cron_time']}\n";
}

echo "\n=== Next Steps ===\n";
echo "1. Start the scheduler: C:\\xampp\\php\\php.exe start_scheduler.php\n";
echo "2. Watch for emails to be sent at the current time\n";
echo "3. Check status: C:\\xampp\\php\\php.exe check_scheduler.php\n";

mysqli_close($conn);
?>
