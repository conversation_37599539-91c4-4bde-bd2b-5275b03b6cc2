<?php
// search-product.php
if (isset($_GET['search_term']) && !empty($_GET['search_term'])) {
  $search_term = $_GET['search_term'];

  // Connect to the database
  $con = mysqli_connect('localhost', 'root', '', 'cll-inventory');

  // Prepare the SQL statement
  $sql = "SELECT `inventory-id`, `product-name`, `part-number`, `serial-number` 
          FROM `inventory-list` 
          WHERE `product-name` LIKE '%$search_term%' 
          OR `part-number` LIKE '%$search_term%' 
          OR `serial-number` LIKE '%$search_term%'";

  // Execute the SQL statement
  $result = mysqli_query($con, $sql);

  // Check if any results were found
  if (mysqli_num_rows($result) > 0) {
    echo '<ul>';
    while ($row = mysqli_fetch_assoc($result)) {
      echo '<li data-inventory-id="' . $row['inventory-id'] . '">' . $row['product-name'] . ' - ' . $row['part-number'] . ' - ' . $row['serial-number'] . '</li>';
    }
    echo '</ul>';
  } else {
    echo '<p>No results found.</p>';
  }

  // Close the database connection
  mysqli_close($con);
}
?>
