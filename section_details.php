<?php
require("database.php");

if (!isset($_SESSION["user"])) {
    header("location: ./signin.php");
    exit();
}

$role_id = $_SESSION["role_id"];
$acc_id = $_SESSION["acc_id"];
$is_admin = false;
$is_hr = false;
$is_finance = false;
$is_it_support = false;
$is_anonymous = false;

// Fetch role permissions
$sql_role = "SELECT * FROM `role-list` WHERE `role-id` = ?";
$stmt_role = mysqli_prepare($conn, $sql_role);
mysqli_stmt_bind_param($stmt_role, "s", $role_id);
mysqli_stmt_execute($stmt_role);
$result_role = mysqli_stmt_get_result($stmt_role);

if ($row_role = mysqli_fetch_assoc($result_role)) {
    $is_admin = ($row_role['section_all'] == '1');
    $is_hr = ($row_role['hr'] == '1');
    $is_finance = ($row_role['finance'] == '1');
    $is_it_support = ($row_role['it_support'] == '1');
    $is_anonymous = ($row_role['anonymous'] == '1');
} else {
    echo "<script>alert('Role data not found')</script>";
    exit();
}

mysqli_stmt_close($stmt_role);

// Retrieve the token from the cookie if it exists
$anon_token = $_COOKIE["anon_token_$acc_id"] ?? null;

$inq_id = $_GET['inq_id'] ?? null;
if (!$inq_id) {
    echo "No inquiry ID provided!";
    exit();
}

$sql = "SELECT * FROM `sections` WHERE `inq_id` = ?";
$stmt = mysqli_prepare($conn, $sql);
mysqli_stmt_bind_param($stmt, "s", $inq_id);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

if ($row = mysqli_fetch_assoc($result)) {
    $section_name = $row['section_name'];
    $status = $row['status'];
    $priority_level = $row['priority_level']; // Fetch priority_level
    $created_by_id = $row['acc_id'];
    $token = $row['acc_id']; // For anonymous inquiries, acc_id stores the token
    $created_by = ($section_name == 'Anonymous Inquiry') ? 'Anonymous' : fetchUserName($created_by_id);
    $comments = nl2br(htmlspecialchars($row['comments']));
    $involved_hr = $row['involved_hr'];
    $involved_finance = $row['involved_finance'];
    $involved_it_support = $row['involved_it_support'];
    $involved_anonymous = $row['involved_anonymous'];

    // Role-based access control: Only the owner or involved roles can access the records
    if (
        $created_by_id != $acc_id && $token != $anon_token && !(
            $is_admin ||
            ($is_hr && $involved_hr) ||
            ($is_finance && $involved_finance) ||
            ($is_it_support && $involved_it_support) ||
            ($is_anonymous && $involved_anonymous)
        )
    ) {
        echo "<script>alert('Access denied.'); window.location='index.php';</script>";
        exit();
    }

    // Continue fetching report details based on section_name
    switch ($section_name) {
        case 'Assets Faulty':
            $sql = "SELECT * FROM `assets_faulty_list` WHERE `inq_id` = ?";
            break;
        case 'Finance':
            $sql = "SELECT * FROM `finance_list` WHERE `inq_id` = ?";
            break;
        case 'Procurement':
            $sql = "SELECT * FROM `procurement_list` WHERE `inq_id` = ?";
            break;
        case 'IT Support':
            $sql = "SELECT * FROM `itsupport_list` WHERE `inq_id` = ?";
            break;
        case 'Anonymous Inquiry':
            $sql = "SELECT * FROM `anonymous_inquiry_list` WHERE `inq_id` = ?";
            break;
        case 'HR Inquiry': // Add this case for HR Inquiry
            $sql = "SELECT * FROM `hr_list` WHERE `inq_id` = ?";
            break;
        default:
            echo "<script>alert('Invalid section name!'); window.location='index.php';</script>";
            exit();
    }

    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, "s", $inq_id);
    mysqli_stmt_execute($stmt);
    $report_result = mysqli_stmt_get_result($stmt);

    if ($report_details = mysqli_fetch_assoc($report_result)) {
        // Data fetched successfully
    } else {
        echo "No report details found!";
        exit();
    }
} else {
    echo "No record found!";
    exit();
}

mysqli_stmt_close($stmt);

function fetchFieldName($table, $id_column, $id_value, $name_column) {
    global $conn;
    $sql = "SELECT `$name_column` FROM `$table` WHERE `$id_column` = ?";
    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, "s", $id_value);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);

    if ($result && $row = mysqli_fetch_assoc($result)) {
        return $row[$name_column];
    }
    return null;
}

function fetchUserName($acc_id) {
    global $conn;
    $sql = "SELECT `name`, `email` FROM `user-list` WHERE `acc_id` = ?";
    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, "s", $acc_id);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);

    if ($result && $row = mysqli_fetch_assoc($result)) {
        return $row['name'] ?: $row['email'];
    }
    return 'Unknown';
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0">
    <meta name="description" content="CLL Ticketing System">
    <meta name="author" content="Powered by CLL Systems Sdn Bhd">
    <meta name="robots" content="noindex, nofollow">
    <title>My Requests Details</title>

    <link rel="shortcut icon" type="image/x-icon" href="https://www.cllsystems.com/wp-content/themes/cllsystems/img/favicon.png">
    <link rel="stylesheet" href="assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="assets/css/animate.css">
    <link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">
    <link rel="stylesheet" href="assets/css/dataTables.bootstrap4.min.css">
    <link rel="stylesheet" href="assets/plugins/fontawesome/css/fontawesome.min.css">
    <link rel="stylesheet" href="assets/plugins/fontawesome/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .images-grid {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        .images-grid a {
            display: inline-block;
            text-align: center;
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 5px;
            background-color: #f9f9f9;
            max-width: 150px;
            word-wrap: break-word;
        }
        .images-grid a:hover {
            background-color: #e9e9e9;
        }
        .btn-danger{
            min-width:120px;
            background:#dc3545;
            color:#fff;
            font-size:14px;
            font-weight:700;
            padding:14px 10px;

        }
        .btn-cancel{
            min-width:120px;
            background:#637381;
            color:#fff;
            font-size:14px;
            font-weight:700;
            padding:14px 10px;
        }
    </style>
    <script src="assets/js/jquery-3.6.0.min.js"></script>
</head>
<body>
    <div id="global-loader">
        <div class="whirly-loader"></div>
    </div>
    <div class="main-wrapper">
        <?php include("header.php"); ?>
        <div class="page-wrapper">
            <div class="content">
                <div class="page-header">
                    <div class="page-title">
                        <h4>My Requests Details</h4>
                        <h6>View details of the selected request</h6>
                    </div>
                </div>
                <div class='card'>
                    <div class='card-body'>
                        <div class='productdetails'>
                            <ul class='product-bar'>
                                <li>
                                    <h4>Status</h4>
                                    <h6><?php echo htmlspecialchars($status); ?></h6>
                                </li>
                                <li>
                                    <h4>Section Name</h4>
                                    <h6><?php echo htmlspecialchars($section_name); ?></h6>
                                </li>
                                <li>
                                    <h4>Priority Level</h4>
                                    <h6><?php echo htmlspecialchars($priority_level); ?></h6>
                                </li>
                                <?php if ($section_name == 'Assets Faulty') { ?>
                                <li>
                                    <h4>Office</h4>
                                    <h6><?php echo fetchFieldName('office_list', 'office_id', $report_details['office_id'], 'office_name'); ?></h6>
                                </li>
                                <li>
                                    <h4>Office Area</h4>
                                    <h6><?php echo fetchFieldName('office_area_list', 'office_area_id', $report_details['office_area_id'], 'office_area'); ?></h6>
                                </li>
                                <li>
                                    <h4>Category</h4>
                                    <h6><?php echo fetchFieldName('office_category_list', 'office_category_id', $report_details['office_category_id'], 'office_category'); ?></h6>
                                </li>
                                <li>
                                    <h4>Fault Category</h4>
                                    <h6><?php echo fetchFieldName('fault_category_list', 'fault_category_id', $report_details['fault_category_id'], 'fault_category'); ?></h6>
                                </li>
                                <li>
                                    <h4>Remarks</h4>
                                    <h6><?php echo htmlspecialchars($report_details['remarks']); ?></h6>
                                </li>
                                <?php } elseif ($section_name == 'Finance') { ?>
                                <li>
                                    <h4>Finance Category</h4>
                                    <h6><?php echo fetchFieldName('financeinq_list', 'finance_inq_id', $report_details['finance_inq_id'], 'finance_inq'); ?></h6>
                                </li>
                                <li>
                                    <h4>Description</h4>
                                    <h6><?php echo htmlspecialchars($report_details['des']); ?></h6>
                                </li>
                                <li>
                                    <h4>Remarks</h4>
                                    <h6><?php echo htmlspecialchars($report_details['remarks']); ?></h6>
                                </li>
                                <?php } elseif ($section_name == 'Procurement') { ?>
                                <li>
                                    <h4>Procurement Inquiry</h4>
                                    <h6><?php echo htmlspecialchars($report_details['inquiry']); ?></h6>
                                </li>
                                <li>
                                    <h4>Remarks</h4>
                                    <h6><?php echo htmlspecialchars($report_details['remarks']); ?></h6>
                                </li>
                                <?php } elseif ($section_name == 'IT Support') { ?>
                                <li>
                                    <h4>IT Support Inquiry</h4>
                                    <h6><?php echo fetchFieldName('itsupportinq_list', 'itsupport_inq_id', $report_details['itsupport_inq_id'], 'itsupport_inq'); ?></h6>
                                </li>
                                <li>
                                    <h4>Category</h4>
                                    <h6><?php echo fetchFieldName('itsupport_category_list', 'itsupport_category_id', $report_details['itsupport_category_id'], 'itsupport_category'); ?></h6>
                                </li>
                                <li>
                                    <h4>Brand Category</h4>
                                    <h6><?php
                                        if (isset($report_details['brand_id'])) {
                                            echo fetchFieldName('brand-list', 'brand-id', $report_details['brand_id'], 'brand-name');
                                        } else {
                                            echo 'No brand category assigned';
                                        }
                                        ?></h6>
                                </li>
                                <li>
                                    <h4>Description</h4>
                                    <h6><?php echo htmlspecialchars($report_details['des']); ?></h6>
                                </li>
                                <li>
                                    <h4>Remarks</h4>
                                    <h6><?php echo htmlspecialchars($report_details['remarks']); ?></h6>
                                </li>
                                <?php } elseif ($section_name == 'Anonymous Inquiry') { ?>
                                <li>
                                    <h4>Anonymous Inquiry</h4>
                                    <h6><?php echo htmlspecialchars($report_details['inquiry']); ?></h6>
                                </li>
                                <li>
                                    <h4>Remarks</h4>
                                    <h6><?php echo htmlspecialchars($report_details['remarks']); ?></h6>
                                </li>
                                <?php } elseif ($section_name == 'HR Inquiry') { ?> <!-- Add this section for HR Inquiry -->
                                <li>
                                    <h4>HR Inquiry</h4>
                                    <h6><?php echo fetchFieldName('hrinq_list', 'hr_inq_id', $report_details['hr_inq_id'], 'hr_inq'); ?></h6>
                                </li>
                                <li>
                                    <h4>Description</h4>
                                    <h6><?php echo htmlspecialchars($report_details['des']); ?></h6>
                                </li>
                                <li>
                                    <h4>Remarks</h4>
                                    <h6><?php echo htmlspecialchars($report_details['remarks']); ?></h6>
                                </li>
                                <?php } ?>
                                <li>
                                    <h4>Images</h4>
                                    <h6>
                                        <div class="images-grid">
                                            <?php
                                            if (!empty($report_details['image'])) {
                                                $images = explode(',', $report_details['image']);
                                                foreach ($images as $image) {
                                                    echo "<a href='$image' download>$image</a>";
                                                }
                                            } else {
                                                echo "No images uploaded.";
                                            }
                                            ?>
                                        </div>
                                    </h6>
                                </li>
                                <li>
                                    <h4>Created by</h4>
                                    <h6><?php echo htmlspecialchars($created_by); ?></h6>
                                </li>
                                <li>
                                    <h4>Comments</h4>
                                    <h6><?php echo $comments; ?></h6>
                                </li>
                            </ul>
                            <br>
                            <div class="col-lg-12">
                                <button class="btn btn-cancel" onclick="goBack()">Back to List</button>
                                <?php if (($status !== "Withdrawn" && $status !== "Completed") && 
                                    ($created_by_id == $acc_id || ($section_name == 'Anonymous Inquiry' && $token == $anon_token))) { ?>
                                    <button class="btn btn-danger" onclick="withdrawInquiry()">Withdrawn</button>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function goBack() {
    window.history.back();
}

function withdrawInquiry() {
    if (confirm("Are you sure you want to withdraw this inquiry?")) {
        const inqId = "<?php echo $inq_id; ?>"; // Pass the inquiry ID to the JS function
        $.ajax({
            url: 'withdraw_inquiry.php',
            type: 'POST',
            data: { inq_id: inqId },
            success: function(response) {
                alert(response);
                if (response.includes("successfully withdrawn")) {
                    window.location.href = 'section_list_user.php';
                }
            },
            error: function(xhr, status, error) {
                alert("An error occurred: " + xhr.responseText);
            }
        });
    }
}
</script>


<script src="assets/js/feather.min.js"></script>
<script src="assets/js/jquery.slimscroll.min.js"></script>
<script src="assets/js/bootstrap.bundle.min.js"></script>
<script src="assets/plugins/select2/js/select2.min.js"></script>
<script src="assets/js/script.js"></script>
</body>
</html>
