<?php
require("database.php");

if (!isset($_SESSION["user"])) {
    header("location: ./signin.php");
    exit();
}

$email = $_SESSION["user"];
$acc_id = $_SESSION["acc_id"];
$fullname = '';

// Generate and store a unique token in a cookie if not already present
if (!isset($_COOKIE["anon_token_$acc_id"])) {
    $anon_token = substr(md5(uniqid($acc_id, true)), 0, 10);
    setcookie("anon_token_$acc_id", $anon_token, time() + (86400 * 30), "/"); // Cookie expires in 30 days
} else {
    $anon_token = $_COOKIE["anon_token_$acc_id"];
}

$sql4 = "SELECT * FROM `user-list` WHERE `acc_id` = ?";
$stmt = mysqli_prepare($conn, $sql4);
mysqli_stmt_bind_param($stmt, "s", $acc_id);
mysqli_stmt_execute($stmt);
$result4 = mysqli_stmt_get_result($stmt);

if ($row4 = mysqli_fetch_assoc($result4)) {
    $fullname = !empty($row4['name']) ? $row4['name'] : $row4['email'];
} else {
    echo "No record found!";
    exit();
}
mysqli_stmt_close($stmt);

function getStatusClass($status) {
    switch ($status) {
        case 'New Tickets':
            return 'badge bg-lightyellow';
        case 'In Progress':
            return 'badge bg-lightorange';
        case 'Rejected':
            return 'badge bg-lightred';
        case 'Completed':
            return 'badge bg-lightgreen';
        default:
            return '';
    }
}

$role_id = $_SESSION["role_id"];
$is_admin = false;

// Fetch role permissions
$sql_role = "SELECT * FROM `role-list` WHERE `role-id` = ?";
$stmt_role = mysqli_prepare($conn, $sql_role);
mysqli_stmt_bind_param($stmt_role, "s", $role_id);
mysqli_stmt_execute($stmt_role);
$result_role = mysqli_stmt_get_result($stmt_role);

if ($row_role = mysqli_fetch_assoc($result_role)) {
    $all = $row_role['section_all'];
    $user= $row_role['user'] || $row_role['hr'] || $row_role['finance'] || $row_role['it_support'] || $row_role['anonymous'];
    $edit = $row_role['hr'] || $row_role['finance'] || $row_role['it_support'] || $row_role['anonymous'];
    $hr = $row_role['hr'];
    $finance = $row_role['finance'];
    $it_support = $row_role['it_support'];
    $anonymous = $row_role['anonymous'];

    if ($user != '1' && $all != '1') {
        header("location: ./index.php");
        exit();
    }

    $is_admin = ($all == '1');
} else {
    echo "<script>alert('Role data not found')</script>";
    exit();
}

$where_clause = "";
if (!$is_admin) {
    $conditions = ["(`sections`.`acc_id` = '$acc_id' OR `anonymous_inquiry_list`.`token` = '$anon_token')"];
    if ($hr) {
        $conditions[] = "`sections`.`involved_hr` = 1";
    }
    if ($finance) {
        $conditions[] = "`sections`.`involved_finance` = 1";
    }
    if ($it_support) {
        $conditions[] = "`sections`.`involved_it_support` = 1";
    }
    if ($anonymous) {
        $conditions[] = "`sections`.`involved_anonymous` = 1";
    }
    $where_clause = "WHERE " . implode(" OR ", $conditions);
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0">
    <meta name="description" content="CLL Ticketing List">
    <meta name="author" content="Powered by CLL Systems Sdn Bhd">
    <meta name="robots" content="noindex, nofollow">
    <title>CLLXWARE-Ticketing List</title>

    <link rel="shortcut icon" type="image/x-icon" href="https://www.cllsystems.com/wp-content/themes/cllsystems/img/favicon.png">
    <link rel="stylesheet" href="assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="assets/css/animate.css">
    <link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">
    <link rel="stylesheet" href="assets/css/dataTables.bootstrap4.min.css">
    <link rel="stylesheet" href="assets/plugins/fontawesome/css/fontawesome.min.css">
    <link rel="stylesheet" href="assets/plugins/fontawesome/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .badge {
            font-size: 12px;
            font-weight: 500;
            padding: 5px 10px;
            width: 100px;
            display: inline-block;
            text-align: center;
            color: #fff;
        }
        .bg-lightgreen {
            background-color: #28c76f;
        }
        .bg-lightorange {
            background-color: #f90;
        }
        .bg-lightred {
            background-color: #ea5455;
        }
        .bg-lightyellow {
            background-color: #ffe107;
        }
        .truncate {
            display: inline-block;
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    </style>
    <script src="assets/js/jquery-3.6.0.min.js"></script>
</head>
<body>
    <div id="global-loader">
        <div class="whirly-loader"></div>
    </div>
    <div class="main-wrapper">
        <?php include("header.php"); ?>
        <div class="page-wrapper">
            <div class="content">
                <div class="page-header">
                    <div class="page-title">
                        <h4>Ticketing List</h4>
                    </div>
                    <?php if ($user == '1' || $is_admin) { ?>
                        <div class='page-btn'> 
                            <a href='add_section.php' class='btn btn-added'> 
                                <img src='assets/img/icons/plus.svg' class='me-2' alt='img'>Add New Ticket 
                            </a> 
                        </div>
                    <?php } ?>
                </div>
                <div class="card">
                    <div class="card-body">
                        <div class="table-top">
                            <div class="search-set">
                                <div class="search-input">
                                    <a class="btn btn-searchset"><img src="assets/img/icons/search-white.svg" alt="img"></a>
                                </div>
                            </div>
                            <div class="wordset">
                                <ul>
                                    <li>
                                        <a data-bs-toggle="tooltip" data-bs-placement="top" title="CSV" href="csv_ticket.php">
                                            <img src="assets/img/icons/excel.svg" alt="CSV">
                                        </a>
                                    </li>
                                    <li>
                                        <a data-bs-toggle="tooltip" data-bs-placement="top" title="print"><img src="assets/img/icons/printer.svg" alt="img" id="printButton"></a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table datanew">
                                <thead>
                                    <tr>
                                        <th>No</th>
                                        <th>Section Name</th>
                                        <th>Status</th>
                                        <th>Created by</th>
                                        <th>Date Submitted</th>
                                        <th>Comments</th>
                                        <th>Action</th> 
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $sql = "SELECT sections.*, anonymous_inquiry_list.token FROM `sections`
                                            LEFT JOIN `anonymous_inquiry_list` ON `sections`.`inq_id` = `anonymous_inquiry_list`.`inq_id`
                                            $where_clause
                                            ORDER BY `sections`.`date_submitted` ASC";
                                    $result = mysqli_query($conn, $sql);

                                    if ($result && mysqli_num_rows($result) > 0) {
                                        $counter = 1;
                                        while ($row = mysqli_fetch_assoc($result)) {
                                            $statusClass = getStatusClass($row['status']);

                                            // Query to fetch data from user-list
                                            $sql3 = "SELECT * FROM `user-list` WHERE `acc_id` = '" . $row['acc_id'] . "'";
                                            $result3 = mysqli_query($conn, $sql3);
                                            if ($result3 && mysqli_num_rows($result3) > 0) {
                                                $row3 = mysqli_fetch_assoc($result3);
                                                $created_by = !empty($row3['name']) ? $row3['name'] : $row3['email'];
                                            } else {
                                                $created_by = 'Anonymous';
                                            }
                                            
                                            echo "<tr>
                                                <td>$counter</td>
                                                <td>" . htmlspecialchars($row['section_name']) . "</td>
                                                <td><span class='$statusClass'>" . htmlspecialchars($row['status']) . "</span></td>
                                                <td>" . htmlspecialchars($created_by) . "</td>
                                                <td>" . htmlspecialchars($row['date_submitted']) . "</td>
                                                <td>";
                                                    $comments = $row['comments'];
                                                    if ($comments != "") {
                                                        echo "<a href='#' data-bs-toggle='modal' data-bs-target='#commentModal' data-comment='" . htmlspecialchars($comments) . "'>Read more</a>";
                                                    } else {
                                                        echo "-";
                                                    }
                                                    echo "</td>
                                                <td>
                                                    <a class='me-3' href='section_details.php?inq_id=" . htmlspecialchars($row['inq_id']) . "'>
                                                        <img src='assets/img/icons/eye.svg' alt='view'>
                                                    </a>";

                                            if ($edit == '1' || $is_admin) {
                                                echo "<a href='edit_section.php?inq_id=" . htmlspecialchars($row['inq_id']) . "' class='me-2'>
                                                    <img src='assets/img/icons/edit.svg' alt='edit'>
                                                </a>";
                                            }

                                            echo "</td></tr>";
                                            $counter++;
                                        }
                                    }
                                    ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Structure -->
    <div class="modal fade" id="commentModal" tabindex="-1" aria-labelledby="commentModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="commentModalLabel">Comment Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p id="modal-comment"></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.getElementById("printButton").addEventListener("click", function() {
            const originalTable = document.querySelector(".datanew");
            const printTable = originalTable.cloneNode(true);
            const printContainer = document.createElement("div");
            printContainer.style.display = "none";
            printContainer.appendChild(printTable);
            document.body.appendChild(printContainer);
            printTable.style.width = "100%";
            printTable.style.border = "1px solid black";
            const stylesheet = document.createElement("style");
            stylesheet.textContent = "@page { size: landscape; }";
            printContainer.appendChild(stylesheet);
            window.print();
            document.body.removeChild(printContainer);
        });

        function submitForm() {
            document.querySelector("form").submit();
        }

        document.querySelectorAll('a[data-bs-target="#commentModal"]').forEach(function(element) {
            element.addEventListener('click', function(event) {
                var comment = event.currentTarget.getAttribute('data-comment');
                var formattedComment = comment.replace(/\n/g, '<br>');
                document.getElementById('modal-comment').innerHTML = formattedComment;
            });
        });
    </script>

    <script src="assets/js/feather.min.js"></script>
    <script src="assets/js/jquery.slimscroll.min.js"></script>
    <script src="assets/js/jquery.dataTables.min.js"></script>
    <script src="assets/js/dataTables.bootstrap4.min.js"></script>
    <script src="assets/js/bootstrap.bundle.min.js"></script>
    <script src="assets/plugins/select2/js/select2.min.js"></script>
    <script src="assets/js/moment.min.js"></script>
    <script src="assets/js/bootstrap-datetimepicker.min.js"></script>
    <script src="assets/plugins/sweetalert/sweetalert2.all.min.js"></script>
    <script src="assets/plugins/sweetalert/sweetalerts.min.js"></script>
    <script src="assets/js/script.js"></script>
</body>
</html>
