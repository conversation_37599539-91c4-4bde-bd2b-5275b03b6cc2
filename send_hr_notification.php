<?php
require('database.php');
require_once 'MAILER/vendor/autoload.php';
use PHPMailer\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\Exception;

if (!isset($_SESSION['user'])) { 
    http_response_code(401); 
    echo 'Unauthorized'; 
    exit; 
}

$onboarding_id = $_POST['onboarding_id'] ?? '';

if ($onboarding_id === '') { 
    http_response_code(400); 
    echo 'Bad request'; 
    exit; 
}

// Load record with related names
$stmt = mysqli_prepare($conn, "
    SELECT ol.*,
           dl.department_name,
           ml.manager_name,
           ofl.office_name as office_name_actual
    FROM `onboarding_list` ol
    LEFT JOIN `department_list` dl ON ol.department_id = dl.department_id
    LEFT JOIN `manager_list` ml ON ol.manager_id = ml.manager_id
    LEFT JOIN `office_list` ofl ON ol.office_id = ofl.office_id
    WHERE ol.onboarding_id = ?
");
mysqli_stmt_bind_param($stmt, 's', $onboarding_id);
mysqli_stmt_execute($stmt);
$res = mysqli_stmt_get_result($stmt);
$rec = mysqli_fetch_assoc($res);
mysqli_stmt_close($stmt);

if(!$rec){ 
    http_response_code(404); 
    echo 'Not found'; 
    exit; 
}

function mailer(){
    $m = new PHPMailer(true);
    $m->isSMTP();
    $m->Host='smtp.office365.com';
    $m->SMTPAuth=true;
    $m->Username='<EMAIL>';
    $m->Password='&[i3F6}0hOw6';
    $m->SMTPSecure='tls';
    $m->Port=587;
    $m->setFrom('<EMAIL>','CLLXWARE');
    return $m;
}

try {
    // Get HR email configuration
    $hr_config = mysqli_query($conn, "SELECT * FROM `onboarding_email_config` WHERE `config_type` = 'hr_admin'");
    $hr_data = mysqli_fetch_assoc($hr_config);
    
    $hr_emails = $hr_data ? array_filter(array_map('trim', explode("\n", $hr_data['email_addresses']))) : ['<EMAIL>'];
    $email_subject = $hr_data['email_subject'] ?? 'HR Checklist Required for New Employee';
    $email_subject = str_replace('{employee_name}', $rec['full_name'], $email_subject);
    $email_template = $hr_data['email_template'] ?? 'A new employee requires HR checklist completion:\n\nEmployee: {employee_name}\nEmployee ID: {employee_id}\nDepartment: {department}\n\nWorkflow Link: {workflow_link}';
    
    if(empty($hr_emails)){ 
        echo 'No HR recipients configured'; 
        exit; 
    }
    
    $workflow_link = (isset($_SERVER['HTTP_HOST']) ? ( (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://') . $_SERVER['HTTP_HOST'] . dirname($_SERVER['PHP_SELF']) ) : 'https://cis.cllsystems.com:9443/staging');
    $workflow_link = rtrim($workflow_link,'/')."/onboarding_workflow.php?id=".urlencode($rec['onboarding_id']);
    
    // Replace template variables with actual names from related tables
    $email_body = str_replace([
        '{employee_name}', '{employee_id}', '{department}', '{job_title}',
        '{onboarding_date}', '{personal_email}', '{phone_number}',
        '{reporting_manager}', '{office_name}', '{workflow_link}'
    ], [
        $rec['full_name'], $rec['employee_id'], $rec['department_name'] ?? '', $rec['job_title'],
        $rec['onboarding_date'], $rec['personal_email'], $rec['phone_number'],
        $rec['manager_name'] ?? '', $rec['office_name_actual'] ?? '', $workflow_link
    ], $email_template);
    
    $mail = mailer();
    
    // Add HR recipients
    foreach($hr_emails as $email){ 
        $mail->addAddress($email); 
    }
    
    $mail->isHTML(true);
    $mail->Subject = $email_subject;
    $mail->Body = nl2br(htmlspecialchars($email_body));
    
    $mail->send();
    echo 'OK';
} catch (Exception $e) {
    http_response_code(500);
    echo 'Email error: ' . $e->getMessage();
}
?>
