<?php
require('database.php');
require_once 'MAILER/vendor/autoload.php';
use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\PHPMailer\Exception;

// Ensure required columns exist
function ensure_column_exists($conn, $table, $column, $definition) {
    $result = mysqli_query($conn, "SHOW COLUMNS FROM `$table` LIKE '$column'");
    if (mysqli_num_rows($result) == 0) {
        mysqli_query($conn, "ALTER TABLE `$table` ADD COLUMN `$column` $definition");
    }
}

ensure_column_exists($conn, 'onboarding_list', 'company_email_password', 'varchar(255) NULL');
ensure_column_exists($conn, 'onboarding_list', 'company_email_set', 'TINYINT(1) NOT NULL DEFAULT 0');
ensure_column_exists($conn, 'onboarding_list', 'setup_token', 'varchar(255) NULL');

// This page is public (no login). Validate token and onboarding_id.
$onboarding_id = isset($_GET['onboarding_id']) ? $_GET['onboarding_id'] : '';
$token = isset($_GET['token']) ? $_GET['token'] : '';

$valid = false;
$record = null;
if ($onboarding_id && $token) {
    $stmt = mysqli_prepare($conn, "SELECT onboarding_id, full_name, employee_id, company_email, personal_email FROM `onboarding_list` WHERE onboarding_id = ? AND setup_token = ? AND (company_email_set = 0 OR company_email_set IS NULL)");
    mysqli_stmt_bind_param($stmt, 'ss', $onboarding_id, $token);
    mysqli_stmt_execute($stmt);
    $res = mysqli_stmt_get_result($stmt);
    if ($res && $row = mysqli_fetch_assoc($res)) { $valid = true; $record = $row; }
    mysqli_stmt_close($stmt);
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $onboarding_id = $_POST['onboarding_id'] ?? '';
    $token = $_POST['token'] ?? '';
    $company_email = trim($_POST['company_email'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirm = $_POST['confirm_password'] ?? '';

    if ($company_email === '' || $password === '' || $confirm === '') {
        $error = 'All fields are required.';
    } elseif ($password !== $confirm) {
        $error = 'Passwords do not match.';
    } else {
        // Verify token again and update
        $stmt = mysqli_prepare($conn, "SELECT id FROM `onboarding_list` WHERE onboarding_id = ? AND setup_token = ? AND (company_email_set = 0 OR company_email_set IS NULL)");
        mysqli_stmt_bind_param($stmt, 'ss', $onboarding_id, $token);
        mysqli_stmt_execute($stmt);
        $res = mysqli_stmt_get_result($stmt);
        if ($res && $row = mysqli_fetch_assoc($res)) {
            mysqli_stmt_close($stmt);

            // Encrypt password before storing
            $encrypted_password = base64_encode($password);

            $upd = mysqli_prepare($conn, "UPDATE `onboarding_list` SET company_email = ?, company_email_password = ?, company_email_set = 1, setup_token = NULL WHERE onboarding_id = ?");
            mysqli_stmt_bind_param($upd, 'sss', $company_email, $encrypted_password, $onboarding_id);
            if (mysqli_stmt_execute($upd)) {
                $success = true;

                // Step 2: Send notifications after CTO completes setup
                send_cto_completion_notifications($onboarding_id, $conn);
            } else {
                $error = 'Failed to save. ' . mysqli_error($conn);
            }
            mysqli_stmt_close($upd);
        } else {
            $error = 'Invalid or expired link.';
            mysqli_stmt_close($stmt);
        }
    }
}

// Step 2: Send notifications after CTO completes company email setup
function send_cto_completion_notifications($onboarding_id, $conn) {

    // Get employee record with related names
    $stmt = mysqli_prepare($conn, "
        SELECT ol.*,
               dl.department_name,
               ml.manager_name,
               ofl.office_name as office_name_actual
        FROM `onboarding_list` ol
        LEFT JOIN `department_list` dl ON ol.department_id = dl.department_id
        LEFT JOIN `manager_list` ml ON ol.manager_id = ml.manager_id
        LEFT JOIN `office_list` ofl ON ol.office_id = ofl.office_id
        WHERE ol.onboarding_id = ?
    ");
    mysqli_stmt_bind_param($stmt, 's', $onboarding_id);
    mysqli_stmt_execute($stmt);
    $res = mysqli_stmt_get_result($stmt);
    $rec = mysqli_fetch_assoc($res);
    mysqli_stmt_close($stmt);

    if (!$rec) return;

    function mailer(){
        $m = new PHPMailer(true);
        $m->isSMTP();
        $m->Host='smtp.office365.com';
        $m->SMTPAuth=true;
        $m->Username='<EMAIL>';
        $m->Password='&[i3F6}0hOw6';
        $m->SMTPSecure='tls';
        $m->Port=587;
        $m->setFrom('<EMAIL>','CLLXWARE');
        return $m;
    }

    try {
        // 2a. Notify HR that company email is created
        send_hr_company_email_notification($rec, $conn);

        // 2b. Notify IT Support for laptop setup (with boss CC)
        send_it_laptop_setup_notification($rec, $conn);

    } catch (Exception $e) {
        // Log error but don't stop the process
        error_log('Notification error: ' . $e->getMessage());
    }
}

// 2a. Notify HR that company email is created
function send_hr_company_email_notification($rec, $conn) {
    // Get HR email configuration
    $hr_config = mysqli_query($conn, "SELECT * FROM `onboarding_email_config` WHERE `config_type` = 'hr_admin'");
    $hr_data = mysqli_fetch_assoc($hr_config);

    $hr_emails = $hr_data ? array_filter(array_map('trim', explode("\n", $hr_data['email_addresses']))) : ['<EMAIL>'];

    $workflow_link = (isset($_SERVER['HTTP_HOST']) ? ( (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://') . $_SERVER['HTTP_HOST'] . dirname($_SERVER['PHP_SELF']) ) : 'https://cis.cllsystems.com:9443/staging');
    $workflow_link = rtrim($workflow_link,'/')."/onboarding_workflow.php?id=".urlencode($rec['onboarding_id']);

    $email_subject = 'Company Email Created - ' . $rec['full_name'];
    $email_body = "Dear HR Team,\n\n";
    $email_body .= "The company email has been successfully created for:\n\n";
    $email_body .= "Employee: " . $rec['full_name'] . "\n";
    $email_body .= "Employee ID: " . $rec['employee_id'] . "\n";
    $email_body .= "Department: " . ($rec['department_name'] ?? 'Not specified') . "\n";
    $email_body .= "Company Email: " . $rec['company_email'] . "\n\n";
    $email_body .= "Next Step: IT Support will be notified for laptop setup.\n\n";
    $email_body .= "View Workflow: " . $workflow_link . "\n\n";
    $email_body .= "Best regards,\nSystem Administrator";

    $mail = new PHPMailer(true);
    $mail->isSMTP();
    $mail->Host='smtp.office365.com';
    $mail->SMTPAuth=true;
    $mail->Username='<EMAIL>';
    $mail->Password='&[i3F6}0hOw6';
    $mail->SMTPSecure='tls';
    $mail->Port=587;
    $mail->setFrom('<EMAIL>','CLLXWARE');
    foreach($hr_emails as $email) {
        $mail->addAddress($email);
    }
    $mail->isHTML(false);
    $mail->Subject = $email_subject;
    $mail->Body = $email_body;
    $mail->send();
    $mail->clearAddresses();
}

// 2b. Notify IT Support for laptop setup (with boss CC)
function send_it_laptop_setup_notification($rec, $conn) {
    // Get IT email configuration
    $it_primary_config = mysqli_query($conn, "SELECT * FROM `onboarding_email_config` WHERE `config_type` = 'it_support'");
    $it_backup_config = mysqli_query($conn, "SELECT * FROM `onboarding_email_config` WHERE `config_type` = 'it_backup'");

    $primary_data = mysqli_fetch_assoc($it_primary_config);
    $backup_data = mysqli_fetch_assoc($it_backup_config);

    $primary_emails = $primary_data ? array_filter(array_map('trim', explode("\n", $primary_data['email_addresses']))) : ['<EMAIL>'];
    $backup_emails = $backup_data ? array_filter(array_map('trim', explode("\n", $backup_data['email_addresses']))) : [];

    // Get boss emails from configuration
    $boss_config = mysqli_query($conn, "SELECT * FROM `onboarding_email_config` WHERE `config_type` = 'boss_cc'");
    $boss_data = mysqli_fetch_assoc($boss_config);
    $boss_emails = $boss_data ? array_filter(array_map('trim', explode("\n", $boss_data['email_addresses']))) : ['<EMAIL>'];

    // Create secure IT assignment link
    $base_url = (isset($_SERVER['HTTP_HOST']) ? ( (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://') . $_SERVER['HTTP_HOST'] . dirname($_SERVER['PHP_SELF']) ) : 'https://cis.cllsystems.com:9443/staging');
    $access_token = hash('sha256', $rec['onboarding_id'] . 'IT_LAPTOP_SETUP_2024');
    $it_assignment_link = rtrim($base_url,'/')."/it_laptop_assignment.php?id=".urlencode($rec['onboarding_id'])."&token=".urlencode($access_token);

    $email_subject = 'ACTION REQUIRED: Assign IT Members for Laptop Setup - ' . $rec['full_name'];
    $email_body = "Dear IT Support Team,\n\n";
    $email_body .= "A new employee requires laptop setup and IT member assignment:\n\n";
    $email_body .= "Employee Information:\n";
    $email_body .= "- Name: " . $rec['full_name'] . "\n";
    $email_body .= "- Employee ID: " . $rec['employee_id'] . "\n";
    $email_body .= "- Department: " . ($rec['department_name'] ?? 'Not specified') . "\n";
    $email_body .= "- Designation: " . $rec['job_title'] . "\n";
    $email_body .= "- Onboarding Date: " . $rec['onboarding_date'] . "\n";
    $email_body .= "- Company Email: " . $rec['company_email'] . "\n\n";
    $email_body .= "REQUIRED ACTION:\n";
    $email_body .= "The company email account has been created. You need to assign IT members who will handle this employee's laptop setup.\n\n";
    $email_body .= "Please click the secure link below to assign team members:\n";
    $email_body .= $it_assignment_link . "\n\n";
    $email_body .= "After assignment, the selected IT members will receive:\n";
    $email_body .= "- Email notification with employee details and credentials\n";
    $email_body .= "- Calendar invite for the onboarding session\n";
    $email_body .= "- Completion link to mark setup as done\n\n";
    $email_body .= "Note: Company email credentials will be provided to assigned IT members only.\n\n";
    $email_body .= "Best regards,\nHR Team";

    $mail = new PHPMailer(true);
    $mail->isSMTP();
    $mail->Host='smtp.office365.com';
    $mail->SMTPAuth=true;
    $mail->Username='<EMAIL>';
    $mail->Password='&[i3F6}0hOw6';
    $mail->SMTPSecure='tls';
    $mail->Port=587;
    $mail->setFrom('<EMAIL>','CLLXWARE');

    // Add primary recipients
    foreach($primary_emails as $email) {
        $mail->addAddress($email);
    }

    // Add backup recipients as CC
    foreach($backup_emails as $email) {
        $mail->addCC($email);
    }

    // Add boss emails as CC
    foreach($boss_emails as $email) {
        $mail->addCC($email);
    }

    $mail->isHTML(false);
    $mail->Subject = $email_subject;
    $mail->Body = $email_body;
    $mail->send();
}


?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Setup Company Account</title>
  <link rel="stylesheet" href="assets/css/bootstrap.min.css">
  <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
<div class="container mt-5" style="max-width:600px;">
  <div class="card">
    <div class="card-body">
      <h4 class="mb-3">Setup Company Email & Password</h4>
      <?php if(isset($success) && $success): ?>
        <div class="alert alert-success">Saved successfully. You can close this page now.</div>
      <?php else: ?>
        <?php if(!$valid && !isset($error)): ?>
          <div class="alert alert-danger">Invalid or expired link.</div>
        <?php endif; ?>
        <?php if(isset($error)): ?>
          <div class="alert alert-danger"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>
        <?php if($valid || $_SERVER['REQUEST_METHOD']==='POST'): ?>
        <form method="post">
          <input type="hidden" name="onboarding_id" value="<?php echo htmlspecialchars($onboarding_id); ?>">
          <input type="hidden" name="token" value="<?php echo htmlspecialchars($token); ?>">
          <div class="mb-3">
            <label class="form-label">Company Email</label>
            <input type="email" class="form-control" name="company_email" required value="<?php echo htmlspecialchars($record['company_email'] ?? ''); ?>">
          </div>
          <div class="mb-3">
            <label class="form-label">Password</label>
            <input type="password" class="form-control" name="password" required>
          </div>
          <div class="mb-3">
            <label class="form-label">Confirm Password</label>
            <input type="password" class="form-control" name="confirm_password" required>
          </div>
          <button type="submit" class="btn btn-primary">Save</button>
        </form>
        <?php endif; ?>
      <?php endif; ?>
    </div>
  </div>
</div>
</body>
</html>

