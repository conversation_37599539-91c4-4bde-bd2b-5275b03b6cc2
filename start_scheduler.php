<?php
/**
 * Start Email Scheduler
 * This script starts the email scheduler in the background
 */

// Set timezone
date_default_timezone_set('Asia/Kuala_Lumpur');

function log_message($message) {
    $timestamp = date('Y-m-d H:i:s');
    echo "[$timestamp] $message" . PHP_EOL;
}

// Check if scheduler is already running
function is_scheduler_running() {
    if (!file_exists(__DIR__ . '/scheduler.pid')) {
        return false;
    }
    
    $pid = trim(file_get_contents(__DIR__ . '/scheduler.pid'));
    
    // On Windows, check if process exists
    if (PHP_OS_FAMILY === 'Windows') {
        $output = shell_exec("tasklist /FI \"PID eq $pid\" 2>NUL");
        return strpos($output, $pid) !== false;
    } else {
        // On Unix-like systems
        return file_exists("/proc/$pid");
    }
}

// Remove stop signal file if it exists
if (file_exists(__DIR__ . '/stop_scheduler.txt')) {
    unlink(__DIR__ . '/stop_scheduler.txt');
    log_message("Removed stop signal file");
}

// Check if already running
if (is_scheduler_running()) {
    log_message("Email scheduler is already running!");
    $pid = trim(file_get_contents(__DIR__ . '/scheduler.pid'));
    log_message("Current PID: $pid");
    exit(1);
}

log_message("Starting Email Scheduler...");

// Start the scheduler in background
if (PHP_OS_FAMILY === 'Windows') {
    // Windows command - create a batch file and execute it
    $php_path = 'C:\xampp\php\php.exe';
    $scheduler_path = __DIR__ . '\email_scheduler.php';
    $log_path = __DIR__ . '\logs\scheduler_output.log';

    // Create a temporary batch file
    $batch_content = "@echo off\n\"$php_path\" \"$scheduler_path\" > \"$log_path\" 2>&1";
    $batch_file = __DIR__ . '\start_scheduler_temp.bat';
    file_put_contents($batch_file, $batch_content);

    // Execute the batch file in background
    $command = "start /B \"\" \"$batch_file\"";
    log_message("Executing command: $command");
    pclose(popen($command, 'r'));

    // Clean up batch file after a moment
    sleep(1);
    if (file_exists($batch_file)) {
        unlink($batch_file);
    }
} else {
    // Unix-like systems
    $command = 'php "' . __DIR__ . '/email_scheduler.php" > "' . __DIR__ . '/logs/scheduler_output.log" 2>&1 &';
    exec($command);
}

// Wait a moment for the scheduler to start
sleep(2);

// Check if it started successfully
if (is_scheduler_running()) {
    $pid = trim(file_get_contents(__DIR__ . '/scheduler.pid'));
    log_message("Email Scheduler started successfully!");
    log_message("PID: $pid");
    log_message("Check logs/email_scheduler.log for detailed logs");
    log_message("To stop: run 'php stop_scheduler.php' or create 'stop_scheduler.txt' file");
} else {
    log_message("Failed to start Email Scheduler. Check logs/scheduler_output.log for errors");
}

?>
