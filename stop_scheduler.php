<?php
/**
 * Stop Email Scheduler
 * This script stops the running email scheduler
 */

// Set timezone
date_default_timezone_set('Asia/Kuala_Lumpur');

function log_message($message) {
    $timestamp = date('Y-m-d H:i:s');
    echo "[$timestamp] $message" . PHP_EOL;
}

// Check if scheduler is running
function is_scheduler_running() {
    if (!file_exists(__DIR__ . '/scheduler.pid')) {
        return false;
    }
    
    $pid = trim(file_get_contents(__DIR__ . '/scheduler.pid'));
    
    // On Windows, check if process exists
    if (PHP_OS_FAMILY === 'Windows') {
        $output = shell_exec("tasklist /FI \"PID eq $pid\" 2>NUL");
        return strpos($output, $pid) !== false;
    } else {
        // On Unix-like systems
        return file_exists("/proc/$pid");
    }
}

log_message("Stopping Email Scheduler...");

if (!is_scheduler_running()) {
    log_message("Email scheduler is not running.");
    
    // Clean up PID file if it exists
    if (file_exists(__DIR__ . '/scheduler.pid')) {
        unlink(__DIR__ . '/scheduler.pid');
        log_message("Cleaned up stale PID file.");
    }
    
    exit(0);
}

$pid = trim(file_get_contents(__DIR__ . '/scheduler.pid'));
log_message("Found running scheduler with PID: $pid");

// Create stop signal file
file_put_contents(__DIR__ . '/stop_scheduler.txt', 'STOP');
log_message("Created stop signal file");

// Wait for graceful shutdown
$max_wait = 30; // 30 seconds
$waited = 0;

while (is_scheduler_running() && $waited < $max_wait) {
    sleep(1);
    $waited++;
    if ($waited % 5 == 0) {
        log_message("Waiting for graceful shutdown... ($waited/$max_wait seconds)");
    }
}

if (is_scheduler_running()) {
    log_message("Graceful shutdown timeout. Attempting to force kill...");
    
    // Force kill the process
    if (PHP_OS_FAMILY === 'Windows') {
        exec("taskkill /F /PID $pid 2>NUL");
    } else {
        exec("kill -9 $pid 2>/dev/null");
    }
    
    sleep(2);
    
    if (is_scheduler_running()) {
        log_message("Failed to stop the scheduler process. You may need to stop it manually.");
        log_message("Process ID: $pid");
    } else {
        log_message("Scheduler stopped forcefully.");
    }
} else {
    log_message("Scheduler stopped gracefully.");
}

// Clean up files
if (file_exists(__DIR__ . '/scheduler.pid')) {
    unlink(__DIR__ . '/scheduler.pid');
}

if (file_exists(__DIR__ . '/stop_scheduler.txt')) {
    unlink(__DIR__ . '/stop_scheduler.txt');
}

log_message("Cleanup completed.");

?>
