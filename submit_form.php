<?php
require("database.php");

use <PERSON><PERSON><PERSON>ailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

require 'MAILER/vendor/autoload.php';

if (!isset($_SESSION["user"])) {
    header("location: ./signin.php");
    exit();
}

$email = $_SESSION["user"];
$acc_id = $_SESSION["acc_id"];
$fullname = '';

// Generate and store a unique token in a cookie if not already present
if (!isset($_COOKIE["anon_token_$acc_id"])) {
    $anon_token = substr(md5(uniqid($acc_id, true)), 0, 10);
    setcookie("anon_token_$acc_id", $anon_token, time() + (86400 * 30), "/"); // <PERSON>ie expires in 30 days
} else {
    $anon_token = $_COOKIE["anon_token_$acc_id"];
}

$sql4 = "SELECT * FROM `user-list` WHERE `acc_id` = ?";
$stmt = mysqli_prepare($conn, $sql4);
mysqli_stmt_bind_param($stmt, "s", $acc_id);
mysqli_stmt_execute($stmt);
$result4 = mysqli_stmt_get_result($stmt);

if ($row4 = mysqli_fetch_assoc($result4)) {
    $fullname = !empty($row4['name']) ? $row4['name'] : $row4['email'];
} else {
    echo "No record found!";
    exit();
}
mysqli_stmt_close($stmt);

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST["submit_assetsfaulty"])) {
        handleAssetsFaulty($conn, $acc_id, 'Assets Faulty', $fullname);
    } elseif (isset($_POST["submit_finance"])) {
        handleFinance($conn, $acc_id, 'Finance', $fullname);
    } elseif (isset($_POST["submit_procurement"])) {
        handleProcurement($conn, $acc_id, 'Procurement', $fullname);
    } elseif (isset($_POST["submit_itsupport"])) {
        handleITSupport($conn, $acc_id, 'IT Support', $fullname);
    } elseif (isset($_POST["submit_anonymousinquiry"])) {
        handleAnonymousInquiry($conn, $acc_id, 'Anonymous Inquiry', $anon_token, $fullname);
    } elseif (isset($_POST["submit_hrinquiry"])) {
        handleHRInquiry($conn, $acc_id, 'HR Inquiry', $fullname);
    }
}

function getInqId($n, $conn) {
    $char = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    do {
        $inq_id = '';
        for ($i = 0; $i < $n; $i++) {
            $inq_id .= $char[rand(0, strlen($char) - 1)];
        }
        $sql = "SELECT COUNT(*) AS count FROM `sections` WHERE `inq_id` = ?";
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, "s", $inq_id);
        mysqli_stmt_execute($stmt);
        mysqli_stmt_bind_result($stmt, $count);
        mysqli_stmt_fetch($stmt);
        mysqli_stmt_close($stmt);
    } while ($count > 0);
    return $inq_id;
}

function handleAssetsFaulty($conn, $acc_id, $section_name, $fullname) {
    if (!isset($_POST["office"], $_POST["office_area"], $_POST["office_category"], $_POST["fault_category"], $_POST["remarks"], $_POST["priority_level"])) {
        echo "<script>alert('Missing required fields.'); window.location='add_section.php';</script>";
        return;
    }

    $office = $_POST["office"];
    $office_area = $_POST["office_area"];
    $office_category = $_POST["office_category"];
    $fault_category = $_POST["fault_category"];
    $remarks = $_POST["remarks"];
    $priority_level = $_POST["priority_level"];
    $image_paths = handleImageUpload();

    $inq_id = getInqId(10, $conn);

    $sql = "INSERT INTO assets_faulty_list (inq_id, office_id, office_area_id, office_category_id, fault_category_id, remarks, image, acc_id, date_submitted) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())";
    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, "ssssssss", $inq_id, $office, $office_area, $office_category, $fault_category, $remarks, $image_paths, $acc_id);

    executeFormSubmission($stmt, $conn, $inq_id, $acc_id, $section_name, 1, 0, 0, 0, $_SESSION["user"], $fullname, $priority_level, false);
}

function handleFinance($conn, $acc_id, $section_name, $fullname) {
    if (!isset($_POST["financecategory"], $_POST["des"], $_POST["remarks"], $_POST["priority_level"])) {
        echo "<script>alert('Missing required fields.'); window.location='section_list_user.php';</script>";
        return;
    }

    $category = $_POST["financecategory"];
    $des = $_POST["des"];
    $remarks = $_POST["remarks"];
    $priority_level = $_POST["priority_level"];
    $image_paths = handleImageUpload();

    $inq_id = getInqId(10, $conn);

    $sql = "INSERT INTO finance_list (inq_id, finance_inq_id, des, remarks, image, acc_id, date_submitted) VALUES (?, ?, ?, ?, ?, ?, NOW())";
    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, "ssssss", $inq_id, $category, $des, $remarks, $image_paths, $acc_id);

    executeFormSubmission($stmt, $conn, $inq_id, $acc_id, $section_name, 0, 1, 0, 0, $_SESSION["user"], $fullname, $priority_level, false);
}

function handleProcurement($conn, $acc_id, $section_name, $fullname) {
    if (!isset($_POST["procurementinquiry"], $_POST["remarks"], $_POST["priority_level"])) {
        echo "<script>alert('Missing required fields.'); window.location='add_section.php';</script>";
        return;
    }

    $inquiry = $_POST["procurementinquiry"];
    $remarks = $_POST["remarks"];
    $priority_level = $_POST["priority_level"];
    $image_paths = handleImageUpload();

    $inq_id = getInqId(10, $conn);

    $sql = "INSERT INTO procurement_list (inq_id, inquiry, remarks, image, acc_id, date_submitted) VALUES (?, ?, ?, ?, ?, NOW())";
    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, "sssss", $inq_id, $inquiry, $remarks, $image_paths, $acc_id);

    executeFormSubmission($stmt, $conn, $inq_id, $acc_id, $section_name, 0, 1, 0, 0, $_SESSION["user"], $fullname, $priority_level, false);
}

function handleITSupport($conn, $acc_id, $section_name, $fullname) {
    if (!isset($_POST["itsupportinquiry"], $_POST["itsupportcategory"], $_POST["brandcategory"], $_POST["des"], $_POST["remarks"], $_POST["priority_level"])) {
        echo "<script>alert('Missing required fields.'); window.location='section_list_user.php';</script>";
        return;
    }

    $inquiry = $_POST["itsupportinquiry"];
    $category = $_POST["itsupportcategory"];
    $brand = $_POST["brandcategory"];
    $des = $_POST["des"];
    $remarks = $_POST["remarks"];
    $priority_level = $_POST["priority_level"];
    $image_paths = handleImageUpload();

    $inq_id = getInqId(10, $conn);

    $sql = "INSERT INTO itsupport_list (inq_id, itsupport_inq_id, itsupport_category_id, brand_id, des, remarks, image, acc_id, date_submitted) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())";
    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, "ssssssss", $inq_id, $inquiry, $category, $brand, $des, $remarks, $image_paths, $acc_id);

    executeFormSubmission($stmt, $conn, $inq_id, $acc_id, $section_name, 0, 0, 1, 0, $_SESSION["user"], $fullname, $priority_level, false);
}

function handleAnonymousInquiry($conn, $acc_id, $section_name, $anon_token, $fullname) {
    if (!isset($_POST["anonymousinquiry"], $_POST["remarks"])) {
        echo "<script>alert('Missing required fields.'); window.location='section_list_user.php';</script>";
        return;
    }

    $inquiry = $_POST["anonymousinquiry"];
    $remarks = $_POST["remarks"];
    $image_paths = handleImageUpload();

    $inq_id = getInqId(10, $conn);

    $sql = "INSERT INTO anonymous_inquiry_list (inq_id, inquiry, remarks, image, acc_id, token, date_submitted) VALUES (?, ?, ?, ?, ?, ?, NOW())";
    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, "ssssss", $inq_id, $inquiry, $remarks, $image_paths, $anon_token, $anon_token);

    executeFormSubmission($stmt, $conn, $inq_id, $anon_token, $section_name, 0, 0, 0, 1, $_SESSION["user"], "Anonymous", '-', true);
}

function handleHRInquiry($conn, $acc_id, $section_name, $fullname) {
    if (!isset($_POST["hrinquiry"], $_POST["des"], $_POST["remarks"], $_POST["priority_level"])) {
        echo "<script>alert('Missing required fields.'); window.location='section_list_user.php';</script>";
        return;
    }

    $hr_inquiry = $_POST["hrinquiry"];
    $des = $_POST["des"];
    $remarks = $_POST["remarks"];
    $priority_level = $_POST["priority_level"];
    $image_paths = handleImageUpload();

    $inq_id = getInqId(10, $conn);

    $sql = "INSERT INTO hr_list (inq_id, hr_inq_id, des, remarks, image, acc_id, date_submitted) VALUES (?, ?, ?, ?, ?, ?, NOW())";
    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, "ssssss", $inq_id, $hr_inquiry, $des, $remarks, $image_paths, $acc_id);

    executeFormSubmission($stmt, $conn, $inq_id, $acc_id, $section_name, 1, 0, 0, 0, $_SESSION["user"], $fullname, $priority_level, false);
}

function handleImageUpload() {
    $image_paths = null;
    $maxTotalSize = 20 * 1024 * 1024; // 20 MB in bytes
    $totalSize = 0;

    if (isset($_FILES["image"]["name"]) && $_FILES["image"]["name"][0] !== "") {
        $target_dir = "uploads/";
        if (!file_exists($target_dir)) {
            mkdir($target_dir, 0777, true);
        }
        if (!is_writable($target_dir)) {
            echo "<script>alert('Error: Upload directory is not writable.');</script>";
            return;
        }
        foreach ($_FILES["image"]["tmp_name"] as $key => $tmp_name) {
            $totalSize += $_FILES["image"]["size"][$key];
            if ($totalSize > $maxTotalSize) {
                echo "<script>alert('Error: Total size of selected files exceeds the 20MB limit.'); window.location='add_section.php';</script>";
                return;
            }
        }

        $uploaded_images = [];
        foreach ($_FILES["image"]["tmp_name"] as $key => $tmp_name) {
            $unique_file_name = uniqid() . '.' . pathinfo($_FILES["image"]["name"][$key], PATHINFO_EXTENSION);
            $target_file = $target_dir . $unique_file_name;

            if (move_uploaded_file($tmp_name, $target_file)) {
                $uploaded_images[] = $target_file;
            } else {
                echo "<script>alert('Error: Failed to upload image.'); window.location='add_section.php';</script>";
                return;
            }
        }
        $image_paths = implode(',', $uploaded_images);
    }
    return $image_paths;
}

function executeFormSubmission($stmt, $conn, $inq_id, $acc_id, $section_name, $involved_hr, $involved_finance, $involved_it_support, $involved_anonymous, $email, $fullname, $priority_level, $is_anonymous) {
    if (mysqli_stmt_execute($stmt)) {
        $sql2 = "INSERT INTO sections (inq_id, section_name, acc_id, status, date_submitted, involved_hr, involved_finance, involved_it_support, involved_anonymous, priority_level) VALUES (?, ?, ?, 'New Ticket', NOW(), ?, ?, ?, ?, ?)";
        $stmt2 = mysqli_prepare($conn, $sql2);
        mysqli_stmt_bind_param($stmt2, "sssiiiis", $inq_id, $section_name, $acc_id, $involved_hr, $involved_finance, $involved_it_support, $involved_anonymous, $priority_level);
        mysqli_stmt_execute($stmt2);
        mysqli_stmt_close($stmt2);

        // Only send notification email if the submission is not anonymous
        if (!$is_anonymous) {
            sendEmailNotification($email, $fullname, $section_name, $priority_level);
        }

        // Fetch and send email to involved departments
        if ($involved_hr) {
            $hr_emails = fetchEmailsByRole($conn, 'hr');
            foreach ($hr_emails as $hr_email) {
                sendEmailNotification($hr_email, "HR Department", $section_name, $priority_level, $fullname, true);
            }
        }

        if ($involved_finance) {
            $finance_emails = fetchEmailsByRole($conn, 'finance');
            foreach ($finance_emails as $finance_email) {
                sendEmailNotification($finance_email, "Finance Department", $section_name, $priority_level, $fullname, true);
            }
        }

        if ($involved_it_support) {
            $it_support_emails = fetchEmailsByRole($conn, 'it_support');
            foreach ($it_support_emails as $it_support_email) {
                sendEmailNotification($it_support_email, "IT Support Department", $section_name, $priority_level, $fullname, true);
            }
        }

        if ($involved_anonymous) {
            $hr_emails = fetchEmailsByRole($conn, 'hr');
            foreach ($hr_emails as $hr_email) {
                sendEmailNotification($hr_email, "HR Department", $section_name, $priority_level, $fullname, true);
            }
        }
        echo "<script>alert('Form submitted successfully'); window.location='section_list_user.php';</script>";
    } else {
        error_log("Error: " . mysqli_error($conn));
        echo "<script>alert('Error: Failed to submit the form'); window.location='add_section.php';</script>";
    }
    mysqli_stmt_close($stmt);
}


function fetchEmailsByRole($conn, $role_column) {
    $sql = "SELECT u.email 
            FROM useracc u
            JOIN `role-list` r ON u.role_id = r.`role-id`
            WHERE r.$role_column = 1";
    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);

    $emails = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $emails[] = $row['email'];
    }

    mysqli_stmt_close($stmt);
    return $emails;
}

function sendEmailNotification($email, $fullname, $section_name, $priority_level, $submitter_name = null, $isInvolved = false) {
    $mail = new PHPMailer(true);
    try {
        // Server settings
        $mail->isSMTP();
        $mail->Host       = 'smtp.office365.com';
        $mail->SMTPAuth   = true;
        $mail->Username   = '<EMAIL>';
        $mail->Password   = '&[i3F6}0hOw6';
        $mail->SMTPSecure = 'tls';
        $mail->Port       = 587;

        // Recipients
        $mail->setFrom('<EMAIL>', 'CLLXWARE');
        $mail->addAddress($email, $fullname);

        // Content
        $mail->isHTML(true);
        $mail->Subject = 'CLLXWARE - Form Submission Notification';
        $priority_display = ($priority_level === null) ? '-' : htmlspecialchars($priority_level);
        $mail->Body = "
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    background-color: #f4f4f4;
                    margin: 0;
                    padding: 0;
                }
                .container {
                    max-width: 600px;
                    margin: 0 auto;
                    padding: 20px;
                    background-color: #ffffff;
                    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
                    border-radius: 10px;
                }
                .header {
                    text-align: center;
                    padding: 20px;
                    background-color: #004085;
                    color: white;
                    border-top-left-radius: 10px;
                    border-top-right-radius: 10px;
                }
                .header img {
                    max-width: 150px;
                    height: auto;
                }
                .content {
                    padding: 20px;
                    color: #333333;
                }
                .content p {
                    margin: 0 0 15px;
                    font-size: 14px;
                    line-height: 1.5;
                }
                .content strong {
                    color: #007bff;
                }
                .content a {
                    display: inline-block;
                    padding: 10px 20px;
                    color: #ffffff;
                    background-color: #004085;
                    text-decoration: none;
                    border-radius: 5px;
                    margin-top: 10px;
                    text-align: center;
                    display: inline-block;
                }
                .footer {
                    text-align: center;
                    padding: 10px;
                    background-color: #f4f4f4;
                    color: #888888;
                    font-size: 12px;
                    border-bottom-left-radius: 10px;
                    border-bottom-right-radius: 10px;
                }
                .footer a {
                    color: #888888;
                    text-decoration: none;
                }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <img src='https://www.cllsystems.com/wp-content/uploads/2016/09/cropped-cll-logo.png' alt='Company Logo'>
                </div>
                <div class='content'>
                    <h2>Form Submission Notification</h2>
                    <p>Dear $fullname,</p>";

        if ($submitter_name) {
            $mail->Body .= "
                    <p><strong>$submitter_name</strong> has submitted the <strong>$section_name</strong> ticket form.</p>
                    <p>Priority Level: <strong>$priority_display</strong></p>
                    <p>You have been involved in this process. Please review the submission at your earliest convenience.</p>
                    <div class='button-container'>
                        <a href='https://cis.cllsystems.com:9443/staging/section_list_admin.php' class='button'>Visit Our Website</a>
                    </div>";
        } else {
            $mail->Body .= "
                    <p>Your submission for <strong>$section_name</strong> ticket has been successfully received.</p>
                    <p>Priority Level: <strong>$priority_display</strong></p>
                    <p>Thank you for your input. We will process it as soon as possible.</p>
                    <div class='button-container'>
                        <a href='https://cis.cllsystems.com:9443/staging/section_list_user.php' class='button'>Visit Our Website</a>
                    </div>";
        }
        
        $mail->Body .= "
                </div>
                <div class='footer'>
                    <p>CLLXWARE | Powered by CLL Systems Sdn Bhd</p>
                    <p>For support, please contact <a href='mailto:<EMAIL>'><EMAIL></a></p>
                </div>
            </div>
        </body>
        </html>";
        
        $mail->send();
    } catch (Exception $e) {
        error_log("Message could not be sent. Mailer Error: {$mail->ErrorInfo}");
    }
}
?>

