<?php
require("database.php");
if(isset($_SESSION["user"])){
?>
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0">
<meta name="description" content="POS - Bootstrap Admin Template">
<meta name="keywords" content="admin, estimates, bootstrap, business, corporate, creative, invoice, html5, responsive, Projects">
<meta name="author" content="Dreamguys - Bootstrap Admin Template">
<meta name="robots" content="noindex, nofollow">
<title>CLLXWARE-Latest Update</title>

<link rel="shortcut icon" type="image/x-icon" href="assets/img/favicon.png">

<link rel="stylesheet" href="assets/css/bootstrap.min.css">

<link rel="stylesheet" href="assets/css/animate.css">

<link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">

<link rel="stylesheet" href="assets/css/dataTables.bootstrap4.min.css">

<link rel="stylesheet" href="assets/plugins/fontawesome/css/fontawesome.min.css">
<link rel="stylesheet" href="assets/plugins/fontawesome/css/all.min.css">

<link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
<div id="global-loader">
<div class="whirly-loader"> </div>
</div>

<div class="main-wrapper">

<?php include("header.php");?>
</div>

<div class="page-wrapper">
<div class="content">
<div class="page-header">
<div class="page-title">
<h4>Update patches</h4>
<h6>Latest Version 1.1 - Latest Update: 26 March 2024 5.30pm</h6>
</div>
</div>

<div class="activity">
<div class="activity-box">
<ul class="activity-list">

<li>
<div class="activity-user">
<a href="" title="" data-toggle="tooltip" data-original-title="Lesley Grauer">
<img alt="Lesley Grauer" src="https://cdn-icons-png.freepik.com/256/190/190411.png" class=" img-fluid">
</a>
</div>
<div class="activity-content">
<div class="timeline-content">
<a href="" class="name">AlexAng: </a>The database daily backup has been created, the backup will saved in server(local) and copy to email, to perform 321 rules.
<span class="time">5.30pm, 26 March 2024</span>
</div>
</div>
</li>

<li>
<div class="activity-user">
<a href="" title="" data-toggle="tooltip" data-original-title="Lesley Grauer">
<img alt="Lesley Grauer" src="https://cdn-icons-png.flaticon.com/512/7704/7704445.png" class=" img-fluid">
</a>
</div>
<div class="activity-content">
<div class="timeline-content">
<a href="" class="name">AlanAng: </a>Suggest to create a auto database daily backup
<span class="time">5.30pm, 25 March 2024</span>
</div>
</div>
</li>

<li>
<div class="activity-user">
<a href="" title="" data-toggle="tooltip" data-original-title="Lesley Grauer">
<img alt="Lesley Grauer" src="https://cdn-icons-png.freepik.com/256/190/190411.png" class=" img-fluid">
</a>
</div>
<div class="activity-content">
<div class="timeline-content">
<a href="" class="name">AlexAng: </a>The enhancement of email notification and assigning email recipients based on role management has been added.
<span class="time">5.15pm, 14 March 2024</span>
</div>
</div>
</li>

<li>
<div class="activity-user">
<a href="" title="" data-toggle="tooltip" data-original-title="Lesley Grauer">
<img alt="Lesley Grauer" src="https://cdn-icons-png.flaticon.com/512/7704/7704445.png" class=" img-fluid">
</a>
</div>
<div class="activity-content">
<div class="timeline-content">
<a href="" class="name">Firdaus: </a>Suggest creating an email notification. When a user requests an item, the specific role assigned will receive an email notification.
<span class="time">3.30pm, 13 March 2024</span>
</div>
</div>
</li>

<li>
<div class="activity-user">
<a href="" title="" data-toggle="tooltip" data-original-title="Lesley Grauer">
<img alt="Lesley Grauer" src="https://cdn-icons-png.freepik.com/256/190/190411.png" class=" img-fluid">
</a>
</div>
<div class="activity-content">
<div class="timeline-content">
<a href="" class="name">AlexAng: </a>The bug was caused by the account ID being declared as an integer instead of a string. The problem has now been resolved.
<span class="time">5.45pm, 13 March 2024</span>
</div>
</div>
</li>

<li>
<div class="activity-user">
<a href="" title="" data-toggle="tooltip" data-original-title="Lesley Grauer">
<img alt="Lesley Grauer" src="https://static.vecteezy.com/system/resources/previews/012/042/292/original/warning-sign-icon-transparent-background-png.png" class=" img-fluid">
</a>
</div>
<div class="activity-content">
<div class="timeline-content">
<a href="" class="name">Wan: </a>A bug was found when adding a new inventory, category, brand, or location: the name 'Alex Ang' appears in the 'created by' row instead of the user's name.
<span class="time">3.30pm, 13 March 2024</span>
</div>
</div>
</li>

<li>
<div class="activity-user">
<a href="" title="" data-toggle="tooltip" data-original-title="Lesley Grauer">
<img alt="Lesley Grauer" src="https://cdn-icons-png.freepik.com/256/190/190411.png" class=" img-fluid">
</a>
</div>
<div class="activity-content">
<div class="timeline-content">
<a href="" class="name">AlexAng: </a>Add an "Update Patches" page in the settings for further tracking purposes.
<span class="time">1.45am, 13 March 2024</span>
</div>
</div>
</li>

<li>
<div class="activity-user">
<a href="" title="" data-toggle="tooltip" data-original-title="Lesley Grauer">
<img alt="Lesley Grauer" src="https://cdn-icons-png.freepik.com/256/190/190411.png" class=" img-fluid">
</a>
</div>
<div class="activity-content">
<div class="timeline-content">
<a href="" class="name">AlexAng: </a>On the "add request" page, due to duplicate inventory names, when searching for a product name and selecting it, the table was not updating. The issue has now been fixed.
<span class="time">1.39am, 13 March 2024</span>
</div>
</div>
</li>

<li>
<div class="activity-user">
<a href="" title="" data-toggle="tooltip" data-original-title="Lesley Grauer">
<img alt="Lesley Grauer" src="https://cdn-icons-png.freepik.com/256/190/190411.png" class=" img-fluid">
</a>
</div>
<div class="activity-content">
<div class="timeline-content">
<a href="" class="name">AlexAng: </a>When using '|' will cause a special character space, which differs from a whitespace character. To resolve this issue, the format of the location column in the CSV has been changed to 'room,rack' [Example: Vespa Room,01].
<span class="time">12.49am, 13 March 2024</span>
</div>
</div>
</li>

<li>
<div class="activity-user">
<a href="" title="" data-toggle="tooltip" data-original-title="Lesley Grauer">
<img alt="Lesley Grauer" src="https://cdn-icons-png.freepik.com/256/190/190411.png" class=" img-fluid">
</a>
</div>
<div class="activity-content">
<div class="timeline-content">
<a href="" class="name">AlexAng: </a>The bug related to the multiple upload of data (CSV file upload) with location has been resolved. The location format in the CSV column is now 'room|rack' [Example: Vespa Room|01].
<span class="time">5.11pm, 12 March 2024</span>
</div>
</div>
</li>

<li>
<div class="activity-user">
<a href="" title="" data-toggle="tooltip" data-original-title="Lesley Grauer">
<img alt="Lesley Grauer" src="https://static.vecteezy.com/system/resources/previews/012/042/292/original/warning-sign-icon-transparent-background-png.png" class=" img-fluid">
</a>
</div>
<div class="activity-content">
<div class="timeline-content">
<a href="" class="name">Wan: </a>Reported that multiple uploads of data (CSV file uploads) failed due to an invalid location.
<span class="time">3.47pm, 12 March 2024</span>
</div>
</div>
</li>

</ul>
</div>
</div>

</div>
</div>
</div>


<script src="assets/js/jquery-3.6.0.min.js"></script>

<script src="assets/js/feather.min.js"></script>

<script src="assets/js/jquery.slimscroll.min.js"></script>

<script src="assets/js/jquery.dataTables.min.js"></script>
<script src="assets/js/dataTables.bootstrap4.min.js"></script>

<script src="assets/js/bootstrap.bundle.min.js"></script>

<script src="assets/plugins/select2/js/select2.min.js"></script>

<script src="assets/plugins/sweetalert/sweetalert2.all.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalerts.min.js"></script>

<script src="assets/js/script.js"></script>
</body>
</html>
<?php
}else{
    header("location: ./signin.php");
}
?>