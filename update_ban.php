<?php
// Include your database connection file
include "database.php";

// Check if the request method is POST
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Sanitize the input to prevent SQL injection
    $acc_id = mysqli_real_escape_string($conn, $_POST["acc_id"]);
    $ban = mysqli_real_escape_string($conn, $_POST["ban"]);

    // Construct the SQL update query
    $sql = "UPDATE `useracc` SET `ban` = '$ban' WHERE `acc_id` = '$acc_id'";
    
    // Execute the update query
    if (mysqli_query($conn, $sql)) {
        // If update is successful, send a success response
        http_response_code(200);
        echo "Ban status updated successfully.";
    } else {
        // If update fails, send an error response
        http_response_code(500);
        echo "Error updating ban status: " . mysqli_error($conn);
    }
    
    // Close the database connection
    mysqli_close($conn);
} else {
    // If the request method is not POST, send a method not allowed response
    http_response_code(405);
    echo "Error: Method not allowed.";
}
?>
