<?php
// Include your database connection file
include "database.php";

// Check if the request method is POST
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Sanitize the input to prevent SQL injection
    $category_id = mysqli_real_escape_string($conn, $_POST["category_id"]);
    $hide = mysqli_real_escape_string($conn, $_POST["hide"]);

    // Construct the SQL update query
    $sql = "UPDATE `category-list` SET `hide` = '$hide' WHERE `category-id` = '$category_id'";
    
    // Execute the update query
    if (mysqli_query($conn, $sql)) {
        // If update is successful, send a success response
        http_response_code(200);
        echo "Hide status updated successfully.";
    } else {
        // If update fails, send an error response
        http_response_code(500);
        echo "Error updating hide status: " . mysqli_error($conn);
    }
    
    // Close the database connection
    mysqli_close($conn);
} else {
    // If the request method is not POST, send a method not allowed response
    http_response_code(405);
    echo "Error: Method not allowed.";
}
?>
