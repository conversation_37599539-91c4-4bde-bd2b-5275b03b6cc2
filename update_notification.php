<?php
require("database.php");
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST["mark-as-read"])) {
        $sql = "UPDATE `notification` SET `read_msg` = 1";
    
        $stmt = mysqli_prepare($conn, $sql);
    
        if (mysqli_stmt_execute($stmt)) {
            echo "Success"; // Return a success message
        } else {
            http_response_code(500); // Internal Server Error
            echo "Error: " . mysqli_error($conn);
        }
        mysqli_stmt_close($stmt);

    } else {
        http_response_code(400); // Bad Request
        echo "Error: Mark as read parameter is missing.";
    }
} else {
    http_response_code(405); // Method Not Allowed
}
?>
