<?php
session_start();

function getInventoryId($n, $conn) {
    $char = '0123456789abc<PERSON>f<PERSON>ijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $is_unique = false;

    while (!$is_unique) {
        // Generate a random inventory ID
        $inventory_id = '';
        for ($i = 0; $i < $n; $i++) {
            $index = rand(0, strlen($char) - 1);
            $inventory_id .= $char[$index];
        }

        // Check if the generated ID already exists in the inventory-list table
        $sql = "SELECT COUNT(*) AS count FROM `inventory-list` WHERE `inventory-id` = ?";
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, "s", $inventory_id);
        mysqli_stmt_execute($stmt);
        mysqli_stmt_bind_result($stmt, $count);
        mysqli_stmt_fetch($stmt);
        mysqli_stmt_close($stmt);

        if ($count === 0) {
            $is_unique = true;
        }
    }

    return $inventory_id;
}

if (isset($_FILES['csvFile']) && $_FILES['csvFile']['error'] === UPLOAD_ERR_OK) {
    $csvFile = $_FILES['csvFile']['tmp_name'];
    $handle = fopen($csvFile, 'r');
    $header = fgetcsv($handle); // Get the header row to determine column indexes

    // Check file extension (assuming the file name ends with .csv)
    if (strtolower(pathinfo($_FILES['csvFile']['name'], PATHINFO_EXTENSION)) !== 'csv') {
        echo "<script>alert('Only CSV files are allowed.');window.location='importproduct.php';</script>";
        exit;
    }

    // Database connection
    $host = 'localhost';
    $username = 'root';
    $password = '';
    $database = 'cll-inventory';
    $conn = new mysqli($host, $username, $password, $database);

    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }

    $allDataValid = true; // Flag to track if all data is valid

    // Loop through each row of the CSV file
    while (($data = fgetcsv($handle, 1000, ',')) !== FALSE) {
        // Extract data from CSV columns
        $name = $data[array_search('Product Name *', $header)];
        $brand = $data[array_search('Brand *', $header)];
        $category = $data[array_search('Category *', $header)];
        $location = $data[array_search('Location *', $header)];
        $part_number = $data[array_search('Product Part Number *', $header)];
        $serial_number = $data[array_search('Product Serial Number *', $header)];
        $qty = $data[array_search('Quantity *', $header)];
        $ownership = $data[array_search('Ownership *', $header)];
        $des = $data[array_search('Description', $header)];
        $status = $data[array_search('Status * (Available / Taken)', $header)];
        $remark = $data[array_search('Remark', $header)];
        
        $acc_id = $_SESSION["acc_id"];
        $sql4 = "SELECT * FROM `user-list` WHERE `acc_id` = ?";
        $stmt = mysqli_prepare($conn, $sql4);
        if (!$stmt) {
            die("Error: " . mysqli_error($conn));
        }

        // Bind parameters and execute the query
        mysqli_stmt_bind_param($stmt, "s", $acc_id);
        mysqli_stmt_execute($stmt);

        // Get the result set
        $result4 = mysqli_stmt_get_result($stmt);

        // Check if any rows are returned
        if ($row4 = mysqli_fetch_assoc($result4)) {
            $fullname = $row4['name'];
            $email2 = $row4['email'];
            // Check if fullname is null or empty
            if (is_null($fullname) || trim($fullname) === "") {
                $fullname = $email2;
            }
        } else {
            // Handle no records found
            echo "No record found!";
        }

        // Close statement
        mysqli_stmt_close($stmt);
        $location = trim($location);
        if (!preg_match('/^[^,]+,[^,]+$/', $location)) {
            $allDataValid = false; // Mark the data as invalid
            continue; // Skip to the next iteration
        }
        // Removed unnecessary if statement here

        list($room, $rack) = explode(',', $location);
        $room = trim($room);
        $rack = trim($rack);

        // Format location ID as room | rack
        $formatted_location = "$room, $rack";
        $location_id = "$room | $rack";

        // Check if location exists in location-list and fetch location IDs
        $stmt = $conn->prepare("SELECT `id` FROM `location-list` WHERE `room` = ? AND `rack` = ?");
        $stmt->bind_param("ss", $room, $rack);
        $stmt->execute();
        $stmt->store_result();

        if ($stmt->num_rows > 0) {
            $stmt->bind_result($existing_location_id);
            $stmt->fetch();
            $location_id = "$room | $rack"; // Use the concatenated room | rack as location ID
        } else {
            // Create new location in location-list
            $stmt = $conn->prepare("INSERT INTO `location-list` (`room`, `rack`, `des`, `created`) VALUES (?, ?, 'CSV import added this Location ID', ?)");
            $stmt->bind_param("sss", $room, $rack, $fullname);
            $stmt->execute();
            // Use the concatenated room | rack as location ID
            $location_id = "$room | $rack";
        }
        $stmt->close();

        // Check if brand exists and get brand ID
        $stmt = $conn->prepare("SELECT `brand-id` FROM `brand-list` WHERE `brand-name` = ?");
        $stmt->bind_param("s", $brand);
        $stmt->execute();
        $stmt->store_result();

        if ($stmt->num_rows > 0) {
            $stmt->bind_result($brand_id);
            $stmt->fetch();
        } else {
            $new_brand_id = str_pad(mt_rand(1, 9999999999), 10, '0', STR_PAD_LEFT);
            $stmt = $conn->prepare("INSERT INTO `brand-list` (`brand-id`, `brand-name`, `des`, `created`) VALUES (?, ?, 'CSV import added this Brand ID', ?)");
            $stmt->bind_param("sss", $new_brand_id, $brand, $fullname);
            $stmt->execute();
            $brand_id = $new_brand_id;
        }
        $stmt->close();

        // Check if category exists and get category ID
        $stmt = $conn->prepare("SELECT `category-id` FROM `category-list` WHERE `category-name` = ?");
        $stmt->bind_param("s", $category);
        $stmt->execute();
        $stmt->store_result();

        if ($stmt->num_rows > 0) {
            $stmt->bind_result($category_id);
            $stmt->fetch();
        } else {
            $new_category_id = str_pad(mt_rand(1, 9999999999), 10, '0', STR_PAD_LEFT);
            $stmt = $conn->prepare("INSERT INTO `category-list` (`category-id`, `category-name`, `des`, `created`) VALUES (?, ?, 'CSV import added this Category ID', ?)");
            $stmt->bind_param("sss", $new_category_id, $category, $fullname);
            $stmt->execute();
            $category_id = $new_category_id;
        }
        $stmt->close();
        
        // Determine status ID
        $status_id = ($status === "Available") ? 1 : 0;

        $inventory_id = getInventoryId(10, $conn);
        // Insert data into inventory-list table
        $insertStmt = $conn->prepare("INSERT INTO `inventory-list` (`product-name`, `brand-id`, `category-id`, `location-id`, `part-number`, `serial-number`, `quantity`, `ownership`, `des`, `status`, `remark`, `created`, `inventory-id`) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        if (!$insertStmt) {
            echo "Error: " . $conn->error;
            exit;
        }
        $insertStmt->bind_param("sssssssssisss", $name, $brand_id, $category_id, $location_id, $part_number, $serial_number, $qty, $ownership, $des, $status_id, $remark, $fullname, $inventory_id);
        $insertStmt->execute();
        $insertStmt->close();
    }

    // Close file handle and database connection
    fclose($handle);
    $conn->close();
    
    if ($allDataValid) {
        // Redirect back to the form with a success message
        echo "<script>alert('The inventory data has been uploaded to the database.');window.location='inventorylist.php';</script>";
        exit;
    } else {
        // Redirect back to the form with an error message
        echo "<script>alert('Invalid data format. Please check your CSV file and try again.');window.location='importproduct.php';</script>";
        exit;
    }
} else {
    echo "<script>alert('There was an error uploading the file.');window.location='importproduct.php';</script>";
    exit;
}
?>
