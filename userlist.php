﻿<?php
require("database.php");
if(isset($_SESSION["user"])){
    $email=$_SESSION["user"];
    $current_acc_id=$_SESSION["acc_id"];
?>
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0">
<meta name="description" content="CLL Warehouse Inventory System">
<meta name="author" content="Powered by CLL Systems Sdn Bhd">
<meta name="robots" content="noindex, nofollow">
<title>CLLXWARE-User List</title>

<link rel='icon' type='image/x-icon' href='https://www.cllsystems.com/wp-content/themes/cllsystems/img/favicon.png'>

<link rel="stylesheet" href="assets/css/bootstrap.min.css">

<link rel="stylesheet" href="assets/css/animate.css">

<link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">

<link rel="stylesheet" href="assets/css/bootstrap-datetimepicker.min.css">

<link rel="stylesheet" href="assets/css/dataTables.bootstrap4.min.css">

<link rel="stylesheet" href="assets/plugins/fontawesome/css/fontawesome.min.css">
<link rel="stylesheet" href="assets/plugins/fontawesome/css/all.min.css">

<link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
<div id="global-loader">
<div class="whirly-loader"> </div>
</div>

<div class="main-wrapper">

<?php 
$role_id = $_SESSION["role_id"];
$sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
$result20 = mysqli_query($conn, $sql20);

if ($result20) {
    $row20 = mysqli_fetch_assoc($result20);
    if ($row20) {
       $view = $row20['user_view'];
       $all = $row20['user_all'];
       if($view != '1' and $all !='1'){
        header("location: ./index.php");
       }
    } else {
        echo "<script>alert('Role data not found')</script>";
    }
} else {
    echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
}
include("header.php");?>
</div>

<div class="page-wrapper">
<div class="content">
<div class="page-header">
<div class="page-title">
<h4>User List</h4>
<h6>Manage your User</h6>
</div>
<?php
$role_id = $_SESSION["role_id"];
$sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
$result20 = mysqli_query($conn, $sql20);

if ($result20) {
    $row20 = mysqli_fetch_assoc($result20);
    if ($row20) {
       $create = $row20['user_create'];
       $all = $row20['user_all'];
       if($create != '1' and $all !='1'){
       echo"";
       }else{
        echo"
        <div class='page-btn'>
        <a href='adduser.php' class='btn btn-added'><img src='assets/img/icons/plus.svg' alt='img' class='me-2'>Add User</a>
        </div>
        ";
       }
    } else {
        echo "<script>alert('Role data not found')</script>";
    }
} else {
    echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
}

?>

</div>

<div class="card">
<div class="card-body">
<div class="table-top">
<div class="search-set">
<div class="search-input">
<a class="btn btn-searchset">
<img src="assets/img/icons/search-white.svg" alt="img">
</a>
</div>
</div>
<div class="wordset">
<ul>
<form action='csv_user.php' method='post'>
<li>
<a data-bs-toggle="tooltip" data-bs-placement="top" title="CSV" href="#" onclick="submitForm();">
                <img src="assets/img/icons/excel.svg" alt="CSV">
            </a>
</li>
</form>
</ul>
</div>
</div>


<div class="table-responsive">
<table class="table  datanew">
<thead>
<tr>
<th>
No
</th>
<th>Profile ID</th>
<th>Full Name </th>
<th>Email </th>
<th>Designation </th>
<th>Department</th>
<th>Role</th>
<th>Account Status</th>
<?php 
            $role_id = $_SESSION["role_id"];
            $sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
            $result20 = mysqli_query($conn, $sql20);
            
            if ($result20) {
                $row20 = mysqli_fetch_assoc($result20);
                if ($row20) {
                    $delete = $row20['user_ban'];
                    $all = $row20['user_all'];
                    if($delete != '1' and $all !='1'){
                    echo"";
                   }else{
                   echo"<th>Ban Status</th>";
                   }
                } else {
                    echo "<script>alert('Role data not found')</script>";
                }
            } else {
                echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
            }?>
<?php 
            $role_id = $_SESSION["role_id"];
            $sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
            $result20 = mysqli_query($conn, $sql20);
            
            if ($result20) {
                $row20 = mysqli_fetch_assoc($result20);
                if ($row20) {
                    $edit = $row20['user_edit'];
                    $all = $row20['user_all'];
                    if($edit != '1' and $all !='1'){
                    echo"";
                   }else{
                    echo"
                    <th>Action</th>
                    ";
                   }
                } else {
                    echo "<script>alert('Role data not found')</script>";
                }
            } else {
                echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
            }?>
</tr>
</thead>
<tbody>
<?php
$sql = "SELECT ul.*, ua.ban, ua.role_id 
FROM `user-list` ul
LEFT JOIN `useracc` ua ON ul.acc_id = ua.acc_id";

$result = mysqli_query($conn, $sql);
if ($result && mysqli_num_rows($result) > 0) {
    $counter = 1;
    while ($row = mysqli_fetch_assoc($result)) {
      $ban_status=$row['ban']; 
      $role_id=$row['role_id']; 

      $sql2 = "SELECT * FROM `role-list` WHERE `role-id`='$role_id'";
      $result2 = mysqli_query($conn, $sql2);
      $row2 = mysqli_fetch_assoc($result2);
echo"
<tr>
<td>$counter</td>
<td>" . $row['acc_id'] . "</td>
<td>" . $row['name'] . "</td>
<td><a href='mailto:" . $row['email'] . "'>" . $row['email'] . "</a> </td>
<td>" . $row['designation'] . " </td>
<td>" . $row['department'] . " </td>";?>
<td><?php echo $row2['role'] ?? "Unknown Role"; ?></td>

<?php
if($ban_status=='0'){
    echo"<td><span class='bg-lightgreen badges'>Active</span></td>";
}else{
echo"<td><span class='bg-lightred badges'>Restricted</span></td>";
}
$current_acc_id = $_SESSION["acc_id"];

$role_id = $_SESSION["role_id"];
$sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
$result20 = mysqli_query($conn, $sql20);

if ($result20) {
    $row20 = mysqli_fetch_assoc($result20);
    if ($row20) {
        $delete = $row20['user_ban'];
        $all = $row20['user_all'];
        if($delete != '1' and $all !='1'){
        echo"";
       }else{
        if ($current_acc_id != $row['acc_id']) {
            echo "
            <td>
                <div class='status-toggle d-flex justify-content-between align-items-center'>
                    <input type='checkbox' id='user_";?><?php echo $row['acc_id']; ?><?php echo"' class='check' data-accid='";?><?php echo $row['acc_id']; ?><?php echo"' ";?><?php echo ($row['ban'] == 1) ? 'checked' : ''; ?><?php echo">
                    <label for='user_";?><?php echo $row['acc_id']; ?><?php echo"' class='checktoggle'>";?><?php echo ($row['ban'] == 1) ? 'Uncheck' : 'Check'; ?><?php echo"</label>
                </div>
            </td>";
        } else {
            // If it's the current user's own account, show a message or alternative content
            echo "<td></td>";
        }
       }
    } else {
        echo "<script>alert('Role data not found')</script>";
    }
} else {
    echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
}

?>
<?php 
            $role_id = $_SESSION["role_id"];
            $sql20 = "SELECT * FROM `role-list` WHERE `role-id` = '$role_id'";
            $result20 = mysqli_query($conn, $sql20);
            
            if ($result20) {
                $row20 = mysqli_fetch_assoc($result20);
                if ($row20) {
                    $edit = $row20['user_edit'];
                    $all = $row20['user_all'];
                    if($edit != '1' and $all !='1'){
                    echo"";
                   }else{
                    echo"
                    <td>
                    <a class='me-3' href='edituser.php?acc_id=" . $row['acc_id'] . "'>
                    <img src='assets/img/icons/edit.svg' alt='img'>
                    </a></td>
                    ";
                   }
                } else {
                    echo "<script>alert('Role data not found')</script>";
                }
            } else {
                echo "<script>alert('Error: " . mysqli_error($conn) . "')</script>";
            }?>
<?php
// Assuming $current_acc_id is already set to the current user's acc_id
//$current_acc_id = $_SESSION["acc_id"];

// Check if the current user is the owner of the displayed account
//$is_owner = ($current_acc_id === $row['acc_id']);

// Conditionally display the delete icon based on ownership
//if ($is_owner) {
    // If the current user is the owner, do not show the delete icon
    //echo "";
//} else {
    // If the current user is not the owner, show the delete icon
    //echo "<a href=\"javascript:void(0);\" onclick=\"deleteItem('" . $row['acc_id'] . "');\">";
    //echo "<img src=\"assets/img/icons/delete.svg\" alt=\"Delete\">";
    //echo "</a>";
//}
?>
 <?php echo"

</tr>
";
$counter++;
    }
}
?>

</tbody>
</table>
</div>
</div>
</div>

</div>
</div>
</div>


<div class="modal fade" id="showpayment" tabindex="-1" aria-labelledby="showpayment" aria-hidden="true">
<div class="modal-dialog modal-lg">
<div class="modal-content">
<div class="modal-header">
<h5 class="modal-title">Show Payments</h5>
<button type="button" class="close" data-bs-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button>
</div>
<div class="modal-body">
<div class="table-responsive">
<table class="table">
<thead>
<tr>
<th>Date</th>
<th>Reference</th>
<th>Amount	</th>
<th>Paid By	</th>
<th>Paid By	</th>
</tr>
</thead>
<tbody>
<tr class="bor-b1">
<td>2022-03-07	</td>
<td>INV/SL0101</td>
<td>$ 1500.00	</td>
<td>Cash</td>
<td>
<a class="me-2" href="javascript:void(0);">
<img src="assets/img/icons/printer.svg" alt="img">
</a>
<a class="me-2" href="javascript:void(0);" data-bs-target="#editpayment" data-bs-toggle="modal" data-bs-dismiss="modal">
<img src="assets/img/icons/edit.svg" alt="img">
</a>
<a class="me-2 confirm-text" href="javascript:void(0);">
<img src="assets/img/icons/delete.svg" alt="img">
</a>
</td>
</tr>
</tbody>
</table>
</div>
</div>
</div>
</div>
</div>


<div class="modal fade" id="createpayment" tabindex="-1" aria-labelledby="createpayment" aria-hidden="true">
<div class="modal-dialog modal-lg">
<div class="modal-content">
<div class="modal-header">
<h5 class="modal-title">Create Payment</h5>
<button type="button" class="close" data-bs-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button>
</div>
<div class="modal-body">
<div class="row">
<div class="col-lg-6 col-sm-12 col-12">
<div class="form-group">
<label>Customer</label>
<div class="input-group">
<input type="text" value="2022-03-07" class="datetimepicker">
<a class="scanner-set input-group-text">
<img src="assets/img/icons/datepicker.svg" alt="img">
</a>
</div>
</div>
</div>
<div class="col-lg-6 col-sm-12 col-12">
<div class="form-group">
<label>Reference</label>
<input type="text" value="INV/SL0101">
</div>
</div>
<div class="col-lg-6 col-sm-12 col-12">
<div class="form-group">
<label>Received Amount</label>
<input type="text" value="1500.00">
</div>
</div>
<div class="col-lg-6 col-sm-12 col-12">
<div class="form-group">
<label>Paying Amount</label>
<input type="text" value="1500.00">
</div>
</div>
<div class="col-lg-6 col-sm-12 col-12">
<div class="form-group">
<label>Payment type</label>
<select class="select">
<option>Cash</option>
<option>Online</option>
<option>Inprogress</option>
</select>
</div>
</div>
<div class="col-lg-12">
<div class="form-group">
<label>Note</label>
<textarea class="form-control"></textarea>
</div>
</div>
</div>
</div>
<div class="modal-footer">
<button type="button" class="btn btn-submit">Submit</button>
<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
</div>
</div>
</div>
</div>


<div class="modal fade" id="editpayment" tabindex="-1" aria-labelledby="editpayment" aria-hidden="true">
<div class="modal-dialog modal-lg">
<div class="modal-content">
<div class="modal-header">
<h5 class="modal-title">Edit Payment</h5>
<button type="button" class="close" data-bs-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button>
</div>
<div class="modal-body">
<div class="row">
<div class="col-lg-6 col-sm-12 col-12">
<div class="form-group">
<label>Customer</label>
<div class="input-group">
<input type="text" value="2022-03-07" class="datetimepicker">
<a class="scanner-set input-group-text">
<img src="assets/img/icons/datepicker.svg" alt="img">
</a>
</div>
</div>
</div>
<div class="col-lg-6 col-sm-12 col-12">
<div class="form-group">
<label>Reference</label>
<input type="text" value="INV/SL0101">
</div>
</div>
<div class="col-lg-6 col-sm-12 col-12">
<div class="form-group">
<label>Received Amount</label>
<input type="text" value="1500.00">
</div>
</div>
<div class="col-lg-6 col-sm-12 col-12">
<div class="form-group">
<label>Paying Amount</label>
<input type="text" value="1500.00">
</div>
</div>
<div class="col-lg-6 col-sm-12 col-12">
<div class="form-group">
<label>Payment type</label>
<select class="select">
<option>Cash</option>
<option>Online</option>
<option>Inprogress</option>
</select>
</div>
</div>
<div class="col-lg-12">
<div class="form-group">
<label>Note</label>
<textarea class="form-control"></textarea>
</div>
</div>
</div>
</div>
<div class="modal-footer">
<button type="button" class="btn btn-submit">Submit</button>
<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
</div>
</div>
</div>
</div>
<script>
    function deleteItem(accId) {
        if (confirm('Are you sure you want to delete this user account? After deleted this user, he/she is not longer able to login the portal.')) {
            // Create a new XMLHttpRequest object
            var xhr = new XMLHttpRequest();
            
            // Define the request
            xhr.open('POST', 'delete_user.php', true);
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
            
            // Set up the callback function
            xhr.onload = function () {
                if (xhr.status >= 200 && xhr.status < 300) {
                    // Reload the page or perform any other action if deletion is successful
                    window.location.reload();
                } else {
                    // Handle error
                    console.error('Error:', xhr.responseText);
                    alert('Error deleting item: ' + xhr.responseText);
                }
            };
            
            // Send the request
            xhr.send('acc_id=' + accId);
        }
    }
</script>

<script>
    function submitForm() {
        // Programmatically submit the form when the link is clicked
        document.querySelector("form").submit();
    }
</script>

<script>
    // Add event listener for checkbox change
    document.querySelectorAll('.check').forEach(item => {
        item.addEventListener('change', function() {
            let acc_id = this.dataset.accid; // Get acc_id from data attribute
            let ban = this.checked ? 1 : 0; // Determine the ban value based on checkbox state
            updateBan(acc_id, ban); // Call the updateBan function
        });
    });

    // Function to update ban status via AJAX
    function updateBan(acc_id, ban) {
        // AJAX request
        let xhr = new XMLHttpRequest();
        xhr.open('POST', 'update_ban.php', true);
        xhr.setRequestHeader('Content-type', 'application/x-www-form-urlencoded');
        xhr.onload = function() {
            if (xhr.status === 200) {
                // Success, reload the page
                location.reload();
            } else {
                // Error handling
                console.error(xhr.responseText);
            }
        };
        xhr.send('acc_id=' + acc_id + '&ban=' + ban); // Send POST data
    }
</script>


<script data-cfasync="false" src="../../cdn-cgi/scripts/5c5dd728/cloudflare-static/email-decode.min.js"></script><script src="assets/js/jquery-3.6.0.min.js"></script>

<script src="assets/js/feather.min.js"></script>

<script src="assets/js/jquery.slimscroll.min.js"></script>

<script src="assets/js/jquery.dataTables.min.js"></script>
<script src="assets/js/dataTables.bootstrap4.min.js"></script>

<script src="assets/js/bootstrap.bundle.min.js"></script>

<script src="assets/plugins/select2/js/select2.min.js"></script>

<script src="assets/js/moment.min.js"></script>
<script src="assets/js/bootstrap-datetimepicker.min.js"></script>

<script src="assets/plugins/sweetalert/sweetalert2.all.min.js"></script>
<script src="assets/plugins/sweetalert/sweetalerts.min.js"></script>

<script src="assets/js/script.js"></script>
</body>
</html>
<?php
}else{
    header("location: ./signin.php");
}
?>