<?php
require("database.php");

if (isset($_GET['email']) && isset($_GET['token'])) {
    $email = $_GET['email'];
    $token = $_GET['token'];

    $sql = "SELECT * FROM useracc WHERE email = ? AND verification_token = ?";
    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, "ss", $email, $token);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);

    if (mysqli_num_rows($result) > 0) {
        $sql = "UPDATE useracc SET verified = 1, verification_token = '' WHERE email = ?";
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, "s", $email);
        if (mysqli_stmt_execute($stmt)) {
            echo "<script>alert('Email verification successful. You can now sign in.'); window.location='signin.php';</script>";
        } else {
            echo "<script>alert('Email verification failed. Please try again.'); window.location='signin.php';</script>";
        }
    } else {
        echo "<script>alert('Invalid verification link.'); window.location='signin.php';</script>";
    }
    mysqli_stmt_close($stmt);
}
mysqli_close($conn);
?>
