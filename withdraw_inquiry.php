<?php
require("database.php");

if (!isset($_POST['inq_id']) || !isset($_SESSION["user"])) {
    echo "Invalid request.";
    exit();
}

$inq_id = $_POST['inq_id'];
$acc_id = $_SESSION["acc_id"];

// Fetch the inquiry details to check permissions
$sql = "SELECT * FROM `sections` WHERE `inq_id` = ?";
$stmt = mysqli_prepare($conn, $sql);
mysqli_stmt_bind_param($stmt, "s", $inq_id);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

if ($row = mysqli_fetch_assoc($result)) {
    $created_by_id = $row['acc_id'];
    $token = $row['acc_id']; // For anonymous inquiries, acc_id stores the token

    // Retrieve the token from the cookie if it exists
    $anon_token = $_COOKIE["anon_token_$acc_id"] ?? null;

    // Check if the user is authorized to withdraw the inquiry
    if ($created_by_id != $acc_id && $token != $anon_token) {
        echo "Access denied.";
        exit();
    }

    // Update the status to withdrawn
    $sql_update = "UPDATE `sections` SET `status` = 'Withdrawn' WHERE `inq_id` = ?";
    $stmt_update = mysqli_prepare($conn, $sql_update);
    mysqli_stmt_bind_param($stmt_update, "s", $inq_id);
    if (mysqli_stmt_execute($stmt_update)) {
        echo "Inquiry has been withdrawn successfully.";
    } else {
        echo "Failed to withdraw the inquiry.";
    }
    mysqli_stmt_close($stmt_update);
} else {
    echo "Inquiry not found.";
}

mysqli_stmt_close($stmt);
?>
